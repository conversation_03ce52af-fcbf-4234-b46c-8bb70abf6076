#!/bin/bash
release_env=$1
java_applicaton=$2
echo "run with $release_env"
JVM_OPTS="-Xms1024m -Xmx2048m -Duser.timezone=Asia/Shanghai -Djava.security.egd=file:/dev/urandom -Dskywalking.agent.is_cache_enhanced_class=true -Dskywalking.agent.class_cache_mode=MEMORY"
APP_OPTS="--spring.profiles.active=$release_env"
JAVA_OPTS="-javaagent:/skywalking-agent/skywalking-agent.jar"
CMD="$JVM_OPTS $JAVA_OPTS -jar $java_applicaton $APP_OPTS"
echo $CMD
java $CMD