# --------------- Deployment配置 ---------------
apiVersion: apps/v1
kind: Deployment
metadata:
  name: aixg-dpt
  namespace: ${namespace}
  labels:
    app: aixg-app
spec:
  replicas: 1
  selector:
    matchLabels:
      app: aixg-app
  template:
    metadata:
      labels:
        app: aixg-app
    spec:
      imagePullSecrets:
        - name: acr-company-key
      containers:
        - name: aixg-ctn
          image: ${image}
          ports:
            - containerPort: 8020
          readinessProbe: # 就绪探针
            httpGet:
              path: "/aixg/api/healthCheck"
              port: 8020
            initialDelaySeconds: 120
            periodSeconds: 5
          livenessProbe: # 存活探针(用于重启POD)
            httpGet:
              path: "/aixg/api/healthCheck"
              port: 8020
            initialDelaySeconds: 120 # 容器启动后等待 60 秒再开始探测
            periodSeconds: 5        # 每隔 5 秒探测一次
            failureThreshold: 10    # 连续 10 次探测失败后重启容器
          resources:
            requests:
              cpu: "500m"
              memory: "2Gi"
            limits:
              memory: "4Gi"
              cpu: "2000m"
---
# --------------- HPA自动伸缩配置 ---------------
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: aixg-hpa
  namespace: ${namespace}
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: aixg-dpt
  minReplicas: ${minReplicas}
  maxReplicas: 10
  metrics:
    - type: Resource
      resource:
        name: cpu
        target:
          type: Utilization
          averageUtilization: 75
---
# --------------- 内网Service配置 ---------------
apiVersion: v1
kind: Service
metadata:
  name: aixg-svc
  namespace: ${namespace}
spec:
  selector:
    app: aixg-app
  ports:
    - protocol: TCP
      port: 80
      targetPort: 8020
  type: ClusterIP
---
# --------------- MSE网关路由配置 ---------------
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: aixg-igr-internal
  namespace: ${namespace}
  annotations:
    mse.aliyun.com/service-type: "MSE"
    mse.ingress.kubernetes.io/whitelist-source-range: ${apiIpWhitelist}
spec:
  ingressClassName: mse
  tls:
    - hosts:
        - ${host}
      secretName: ${host}-tls-secret
  rules:
    - host: ${host}
      http:
        paths:
          - path: /aixg/api
            pathType: Prefix
            backend:
              service:
                name: aixg-svc
                port:
                  number: 80
          - path: /aixg/gateway
            pathType: Prefix
            backend:
              service:
                name: aixg-svc
                port:
                  number: 80
---
# --------------- MSE网关路由配置 ---------------
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: aixg-igr-external
  namespace: ${namespace}
  annotations:
    mse.aliyun.com/service-type: "MSE"  # 声明使用MSE网关
spec:
  ingressClassName: mse
  tls:
    - hosts:
        - ${host}
      secretName: ${host}-tls-secret
  rules:
    - host: ${host}
      http:
        paths:
          - path: /aixg
            pathType: Prefix
            backend:
              service:
                name: aixg-svc
                port:
                  number: 80
