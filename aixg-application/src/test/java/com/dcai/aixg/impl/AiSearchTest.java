package com.dcai.aixg.impl;

import com.dcai.aixg.AixgApplication;
import com.dcai.aixg.dto.search.HotSearchDTO;
import com.ejuetc.commons.base.response.ApiResponse;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.List;

@RunWith(SpringRunner.class)
@SpringBootTest(classes = {AixgApplication.class}, properties = {"application.yaml"})
public class AiSearchTest {

    @Autowired
    private SearchImpl searchImpl;
	
	@Test
    public void testGetHotSearch() {
		System.out.println("12345");
        ApiResponse<List<HotSearchDTO>> response = searchImpl.getHotSearch();
        System.out.println(response);
		System.out.println("54321");
    }

}
