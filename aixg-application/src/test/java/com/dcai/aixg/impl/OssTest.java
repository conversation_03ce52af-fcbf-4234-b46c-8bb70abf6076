package com.dcai.aixg.impl;


//@RunWith(SpringRunner.class)
//@SpringBootTest
public class OssTest {

//    @Autowired
//    private OssComponent ossComponent;
//
//    @Test
//    public void makeUploadUrl() {
//        URL signUrl = ossComponent.makeUploadUrl("user-upload/%s.txt".formatted(UUID.randomUUID()), 300);
//        System.out.println("Sign URL: " + signUrl);
//    }

    //curl -X PUT -T /Users/<USER>/IdeaProjects/dcai/aixg/docs/temp/demo.txt "https://dcai-aixg.oss-cn-shanghai.aliyuncs.com/broker-photo/1/20250603/28c192b1-0a3e-4092-a881-56ad183a9104.jpg?Expires=1748917805&OSSAccessKeyId=LTAI5tBnSSpJoSdU3kGfcgk6&Signature=8D4dMzcea4dhCtPJYR%2BC8fXX9XA%3D"
}