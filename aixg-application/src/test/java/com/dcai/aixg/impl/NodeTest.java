package com.dcai.aixg.impl;

import com.dcai.aixg.AixgApplication;
import com.dcai.aixg.domain.aiagent.node.NodeService;
import com.ejuetc.commons.base.response.ApiResponse;
import com.ejuetc.commons.base.utils.IOUtils;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.io.FileInputStream;
import java.io.FileNotFoundException;

import static com.alibaba.fastjson.JSON.toJSONString;
import static com.dcai.aixg.domain.aiagent.node.Node4LlmGen.RESULT_TYPE_JSON;
import static com.dcai.aixg.domain.aiagent.node.Node4LlmGen.clearMdJsonFlag;
import static com.dcai.aixg.dto.NodeDTO.Status.SUCCESS;
import static java.lang.Thread.sleep;
import static org.junit.Assert.fail;

@RunWith(SpringRunner.class)
@SpringBootTest(classes = {AixgApplication.class})
public class NodeTest {

    @Autowired
    private NodeService nodeService;

    @Test
    public void testGenerate() throws InterruptedException, FileNotFoundException {
//        String modeName = "豆包";
//        String modeName = "KIMI";
//        String modeName = "通义千问";
        String modeName = "DeepSeek";
        String prom = IOUtils.read(new FileInputStream("/Users/<USER>/IdeaProjects/dcai/aixg/docs/prompt/5_月度报告/proms/prom_月度报告_s2_md2json.md"));
        ApiResponse<Long> response = nodeService.launch4test(modeName, RESULT_TYPE_JSON, prom);
        System.out.println(response);
        sleep(1000 * 60 * 10);
    }

    @Test
    public void testReceiveAsyncResponse() throws InterruptedException, FileNotFoundException {
        String prom = IOUtils.read(new FileInputStream("/Users/<USER>/IdeaProjects/dcai/aixg/docs/prompt/1_政策解读专家/proms/cric_response.md"));
        ApiResponse<?> response = nodeService.receiveAsyncResponse(667L,SUCCESS, prom);
        System.out.println(response);
        sleep(1000 * 60 * 10);
    }

    public static void main(String[] args) throws FileNotFoundException {
        String json = IOUtils.read(new FileInputStream("/Users/<USER>/IdeaProjects/dcai/aixg/docs/temp/s.md"));
        System.out.println(clearMdJsonFlag(json));
    }

}
