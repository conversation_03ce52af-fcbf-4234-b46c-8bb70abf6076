package com.dcai.aixg.impl;

import com.dcai.aixg.dto.OrderDTO;
import com.dcai.aixg.pro.CreateOrderPO;
import com.ejuetc.commons.base.filter.login.SaasLoginToken;
import com.ejuetc.commons.base.response.ApiResponse;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

@RunWith(SpringRunner.class)
@SpringBootTest
public class OrderTest {

    @Autowired
    private OrderImpl orderImpl;

    @Test
    public void testGoods() {
        ApiResponse<?> response = orderImpl.goods(new SaasLoginToken()
                .setToken("token FY:ACC:TOKEN:AIXG::{b7e5b986-94f1-4ba2-9aa3-2ae56479b2a9}")
                .setUserId(9096691966195823619L)
        );
        System.out.println(response);
    }

    @Test
    public void testVasGoods() {
        ApiResponse<?> response = orderImpl.vasGoods(new SaasLoginToken()
                .setToken("token FY:ACC:TOKEN:AIXG::{b7e5b986-94f1-4ba2-9aa3-2ae56479b2a9}")
                .setUserId(9096691966195823619L)
        );
        System.out.println(response);
    }

    @Test
    public void testCreate() {
        ApiResponse<?> response = orderImpl.create(new SaasLoginToken()
                        .setToken("token FY:ACC:TOKEN:AIXG::{b7e5b986-94f1-4ba2-9aa3-2ae56479b2a9}")
                        .setUserId(9096691966195823619L),
                new CreateOrderPO().setGoodsId(5L).setGoodsType(OrderDTO.Type.POINT).setOpenId("o1Fun7RSBIkPBUOHYCrGLInXFaUQ")
        );
        System.out.println(response);
    }
    @Test
    public void testCreateVas() {
        ApiResponse<?> response = orderImpl.create(new SaasLoginToken()
                        .setToken("token FY:ACC:TOKEN:AIXG::{feeeb1e3-bc7d-4e3a-b075-d7bd389829fb}")
                        .setUserId(9086151989681777408L),
                new CreateOrderPO().setGoodsId(1L).setGoodsType(OrderDTO.Type.BUSINESS_CARD)
        );
        System.out.println(response);
    }
    @Test
    public void testCallBack() {
        orderImpl.testCallBack();
    }
    @Test
    public void testApiUpdateCity() {
        orderImpl.testApiUpdateCity();
    }
}
