<?xml version="1.0" encoding="UTF-8"?>
<configuration scan="true" scanPeriod="30 seconds">
    <shutdownHook class="ch.qos.logback.core.hook.DelayingShutdownHook"/>

    <springProperty name="springAppBusi" scope="context" source="spring.application.business"/>
    <springProperty name="springAppName" scope="context" source="spring.application.name"/>
    <springProperty name="springProfile" scope="context" source="spring.profiles.active"/>
    <property name="springFullAppName" value="${springAppBusi}-${springAppName}"/>
    <contextName>${springFullAppName}</contextName>
    <conversionRule conversionWord="clr" converterClass="org.springframework.boot.logging.logback.ColorConverter"/>
    <property name="LOG_PATTERN" value="%clr(%d{yyyy-MM-dd HH:mm:ss.SSS}){faint} [%X{tid}] %clr(${LOG_LEVEL_PATTERN:-%5p}) %clr(%thread){magenta} %clr(%-40.40logger{30}){cyan} %clr(:){faint} %m%n"/>

    <appender name="ALIYUN" class="com.aliyun.openservices.log.logback.LoghubAppender">
        <endpoint>cn-shanghai.log.aliyuncs.com</endpoint>
        <accessKeyId>LTAI5tBnSSpJoSdU3kGfcgk6</accessKeyId>
        <accessKeySecret>******************************</accessKeySecret>
        <project>${springFullAppName}</project>
        <logStore>${springProfile}-logstore</logStore>
        <encoder class="ch.qos.logback.core.encoder.LayoutWrappingEncoder">
            <layout class="org.apache.skywalking.apm.toolkit.log.logback.v1.x.mdc.TraceIdMDCPatternLogbackLayout">
                <Pattern>%X{tid}</Pattern>
            </layout>
            <charset>UTF-8</charset>
        </encoder>
        <mdcFields>*</mdcFields>
    </appender>

    <appender name="CONSOLE" class="ch.qos.logback.core.ConsoleAppender">
        <encoder class="ch.qos.logback.core.encoder.LayoutWrappingEncoder">
            <layout class="org.apache.skywalking.apm.toolkit.log.logback.v1.x.mdc.TraceIdMDCPatternLogbackLayout">
                <Pattern>${LOG_PATTERN}</Pattern>
            </layout>
            <charset>UTF-8</charset>
        </encoder>
    </appender>

    <appender name="FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <encoder>
            <pattern>${LOG_PATTERN}</pattern>
            <charset>UTF-8</charset>
        </encoder>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>/var/dcai/${springAppName}/logback-%d{yyyy-MM-dd}.%i.log</fileNamePattern>
            <timeBasedFileNamingAndTriggeringPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedFNATP">
                <maxFileSize>100MB</maxFileSize>
            </timeBasedFileNamingAndTriggeringPolicy>
            <maxHistory>15</maxHistory>
        </rollingPolicy>
    </appender>

    <appender name="ASYNC_FILE" class="ch.qos.logback.classic.AsyncAppender">
        <appender-ref ref="FILE"/>
        <queueSize>100000</queueSize>
        <discardingThreshold>0</discardingThreshold>
        <neverBlock>true</neverBlock>
    </appender>

    <logger name="com.ejuetc" level="DEBUG"/>
    <logger name="com.dcai" level="DEBUG"/>
    <logger name="org.springframework.ai" level="DEBUG"/>

    <springProfile name="local">
        <root level="INFO">
            <appender-ref ref="CONSOLE"/>
            <appender-ref ref="ASYNC_FILE"/>
        </root>
    </springProfile>

    <springProfile name="test">
        <root level="DEBUG">
            <appender-ref ref="CONSOLE"/>
            <appender-ref ref="ALIYUN"/>
        </root>
    </springProfile>
    <springProfile name="pre">
        <root level="INFO">
            <appender-ref ref="CONSOLE"/>
            <appender-ref ref="ALIYUN"/>
        </root>
    </springProfile>
    <springProfile name="prod">
        <root level="INFO">
            <appender-ref ref="CONSOLE"/>
            <appender-ref ref="ALIYUN"/>
        </root>
    </springProfile>

    <root level="INFO">
        <appender-ref ref="CONSOLE"/>
    </root>
</configuration>
