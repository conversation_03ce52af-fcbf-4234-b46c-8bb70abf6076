spring:
  cloud:
    nacos:
      discovery:
        server-addr: mse-76e13030-nacos-ans.mse.aliyuncs.com:8848
        namespace: ${spring.application.business}
        group: ${spring.profiles.active}
      config:
        server-addr: mse-76e13030-nacos-ans.mse.aliyuncs.com:8848
        file-extension: yaml
        refresh-enabled: true
        namespace: ${spring.application.business}
        group: ${spring.application.name}
  config:
    import: nacos:${spring.application.name}-${spring.profiles.active}.yaml
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: ****************************************************************************
    username: dcai_aixg_test
    password: Yu7*kJg5$dfVbJ98^754^
    hikari:
      maximum-pool-size: 10
      connection-timeout: 5000
  jpa:
    database-platform: org.hibernate.dialect.MySQLDialect
    hibernate:
      ddl-auto: update
    show-sql: true
    open-in-view: true
    properties:
      hibernate:
        format_sql: true
  data:
    redis:
      host: r-uf6zvmt0tsgr8dpl0d.redis.rds.aliyuncs.com
      password: 8KNkiy^*6tgJbnft#5dSu8Btej
      port: 6379
      timeout: 5000
      jedis:
        pool:
          max-active: 120
          max-wait: 3000
          min-idle: 5
          max-idle: 100
redis:
  saas:
    standalone: r-uf6zvmt0tsgr8dpl0d.redis.rds.aliyuncs.com:6379
    password: 8KNkiy^*6tgJbnft#5dSu8Btej
dcai:
  gateway:
    sdk:
      keyCode: "5b00f7801f624a5a8952810bff93ee17"
      keySecret: "DVK+WQ886uzOOgN03QtRstQemWmp6EnZ4WDWdqoqdH4="
      serverUrl: "https://dcai-test.ebaas.com/gateway"
  message:
    url: "http://message-svc.dcai.svc.cluster.local/message"
  aixg:
    saasapi:
      feign-client:
        consumer-server-url: "http://ejuetc-consumer.tst.ejucloud.cn/consumer"
    broker:
      propertyUrl: "https://test-api.fangyou.com"
      merchantUrl: "https://test-svc.fangyou.com"
      id: 9086151989681777408
    ker:
      url: "https://prewww.dichanai.com"
      client_id: "test-ejuetc-fb4qikrwmg"
      client_secret: "sk-oA7v9hJvWIy8qxMRTWqyzApOf3bo4kTJ"
    order:
      callBackUrl: "http://dcai-test.ebaas.com/aixg/api/order/orderPayCallBack"
    webUrl: "https://dcai-test.ebaas.com/cric/article/miniApp?"
      
ejuetc:
  saasapi:
    sdk:
      key-code: "e200a3b268394d6db4ac9b97f2ea5bc9"
      key-secret: "mY24t2ARVPx3l11+Sjw/WNNCxiHX5zB64aWE7IoRIbk="
      host: "http://saasapi-test.ebaas.com/gateway/invoke"
  commons:
    oss:
      access-key-id: LTAI5tBnSSpJoSdU3kGfcgk6
      access-key-secret: ******************************
      oss-endpoint: oss-cn-shanghai.aliyuncs.com
      url-prefix: https://dcai-oss.ebaas.com/
      bucket-name: dcai-oss
      upload-functions:
        aixg-broker-photo:
          date-format: yyyyMMdd
          max-count: 5
          valid-seconds: 900
  loginFilter:
    enable: true
  login:
    processor:
      saas:
        enable: true
#  consumer:
#    url: "http://ejuetc-consumer.tst.ejucloud.cn/consumer"
#  saasApi:
#    keyCode: "e200a3b268394d6db4ac9b97f2ea5bc9"
#    keySecret: "mY24t2ARVPx3l11+Sjw/WNNCxiHX5zB64aWE7IoRIbk="
#    url: "http://saasapi-test.ebaas.com"
