package com.dcai.aixg;

import static com.ejuetc.commons.base.filter.BaseJsonFilter.BASE_FILTER_ORDER;

import java.util.List;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.domain.EntityScan;
import org.springframework.boot.web.servlet.FilterRegistrationBean;
import org.springframework.boot.web.servlet.ServletComponentScan;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.EnableAspectJAutoProxy;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;

import com.ejuetc.commons.base.application.BaseApplication;
import com.ejuetc.commons.base.filter.ClearJsonTypeFilter;

import jakarta.servlet.Filter;

@SpringBootApplication(scanBasePackages = {"com.ejuetc.**", "com.dcai.**"})
@ServletComponentScan(basePackages = {"com.ejuetc.**", "com.dcai.**"})
@EnableJpaRepositories(basePackages = {"com.ejuetc.**", "com.dcai.**"})
@EntityScan(basePackages = {"com.ejuetc.**", "com.dcai.**"})
@EnableDiscoveryClient
@EnableFeignClients({"com.ejuetc.**", "com.dcai.**"})
@EnableScheduling
@EnableAspectJAutoProxy
@EnableAsync
@ComponentScan(basePackages = {"com.ejuetc.**", "com.dcai.**"})
public class AixgApplication extends BaseApplication {

    public static void main(String[] args) {
        SpringApplication.run(AixgApplication.class, args);
    }

    @Bean
    public FilterRegistrationBean<?> jsonTypeInfoFilter() {
        FilterRegistrationBean<Filter> registrationBean = new FilterRegistrationBean<>();
        registrationBean.setFilter(new ClearJsonTypeFilter());   //设置过滤器
        registrationBean.setUrlPatterns(List.of("/web/*"));
        registrationBean.setOrder(BASE_FILTER_ORDER + 2);  //设置优先级
        return registrationBean;
    }

}
