package com.dcai.aixg.kerApi;

import com.ejuetc.commons.base.entity.TitleEnum;

import lombok.Getter;

@Getter
public enum KerApiUrl implements TitleEnum {
    TOKEN("/api/v1/user-center/openapi/auth/token?grant_type=client_credentials&client_id={clientId}&client_secret={clientSecret}"),
    USER_CREATE("/api/v1/user-center/openapi/user/query-or-create?mobile=%s"),
    USER_POINT("/api/v1/user-center/openapi/point/remain/list?userId=%s"),
    POINT_CONFIG("/api/v1/user-center/openapi/point/config"),
    POINT_DETAIL("/api/v1/user-center/openapi/point/change/list?userId=%s&page=%s&pageSize=%s"),
    GOODS_LIST("/api/v1/user-center/openapi/point/goods/list"),
    VAS_GOODS_LIST("/api/v1/user-center/openapi/point/goods/list-onetime-pay?userId=%s&type=customer-pkg-1"),
    CREATE_ORDER("/api/v1/user-center/openapi/point/order/create"),
    QUERY_ORDER("/api/v1/user-center/openapi/point/order/pay-status?userId=%s&orderNo=%s"),
	
	/***** AI搜索相关接口 *****/
    //热点搜索
	HOT_SEARCH("/api/v1/user-center/openapi/hot/search"),
	//创建搜索
	CHAT_CREATE("/api/v1/user-center/openapi/chat/create"),
	//开始搜索
	CHAT_START("/api/v1/user-center/openapi/chat/start"),
	//搜索列表
	CHAT_LIST("/api/v1/user-center/openapi/aisearch/list?userId=%s"),
	//搜索详情
	CHAT_DETAIL("/api/v1/user-center/openapi/chat/detail"),
	//删除搜索消息
	CHAT_MESSAGE_DELETE("/api/v1/user-center/openapi/chat/message/delete?userId=%s&messageId=%s"),
	//删除搜索
	CHAT_DELETE("/api/v1/user-center/openapi/chat/delete?userId=%s&chatId=%s"),
	
	/***** AI报告相关接口 *****/
	//生成报告
	REPORT_CREATE("/api/v1/user-center/openapi/report/create"),
	//报告列表
	REPORT_LIST("/api/v1/user-center/openapi/report/list"),
	//报告详情
	REPORT_DETAIL("/api/v1/user-center/openapi/report/html/detail"),
	//删除报告
	REPORT_DELETE("/api/v1/user-center/openapi/report/delete?userId=%s&reportId=%s"),
	
	/***** AI文章相关接口 *****/
	//生成文章
	WRITE_CREATE("/api/v1/user-center/openapi/write/create?userId=%s&writeType=%s"),
	//文章主题确认
	WRITE_CHAT("/api/v1/user-center/openapi/write/chat"),
	//文章写作
	WRITE_SUBMIT("/api/v1/user-center/openapi/write/submit"),
	//文章列表
	WRITE_LIST("/api/v1/user-center/openapi/write/list?userId=%s"),
	//文章详情
	WRITE_DETAIL("/api/v1/user-center/openapi/write/detail?userId=%s&writeId=%s"),
	//删除文章
	WRITE_DELETE("/api/v1/user-center/openapi/write/delete?userId=%s&writeId=%s"),

	/***** 获取数据 *****/
	//获取价格评测数据
	DATA_PRICE_ASSESSMENT("/api/v1/user-center/openapi/report/data/price-assessment"),
	//获取评测对比数据
	DATA_COMMUNITY_COMPARE("/api/v1/user-center/openapi/report/data/community-compare"),
	//数据结果通知
	DATA_RESULT_NOTICE("/api/v1/user-center/openapi/write/ejuetc/result/notice"),
	;

    private final String title;

    KerApiUrl(String title) {
        this.title = title;
    }

    @Override
    public String toString() {
        return name() + "-" + title;
    }

}
