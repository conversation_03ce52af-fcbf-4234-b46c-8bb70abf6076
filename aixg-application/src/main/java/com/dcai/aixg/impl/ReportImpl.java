package com.dcai.aixg.impl;

import static com.ejuetc.commons.base.querydomain.impl.Domain2DTOConvert.convert2DTO;
import static com.ejuetc.commons.base.response.ApiResponse.succ;
import static com.ejuetc.commons.base.spring.SpringUtil.getBean;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.RestController;

import com.alibaba.cloud.commons.lang.StringUtils;
import com.alibaba.fastjson.JSONObject;
import com.dcai.aixg.api.ReportAPI;
import com.dcai.aixg.domain.broker.Broker;
import com.dcai.aixg.domain.broker.BrokerRpt;
import com.dcai.aixg.domain.task.Report;
import com.dcai.aixg.domain.task.TaskRpt;
import com.dcai.aixg.dto.BrokerDTO;
import com.dcai.aixg.dto.task.ReportCreateDTO;
import com.dcai.aixg.dto.task.ReportDetailDTO;
import com.dcai.aixg.dto.task.ReportInfoDTO;
import com.dcai.aixg.integration.wechat.WechatService;
import com.dcai.aixg.kerApi.KerApi;
import com.dcai.aixg.pro.search.ListQueryPO;
import com.dcai.aixg.pro.task.ReportCreatePO;
import com.ejuetc.commons.base.exception.BusinessException;
import com.ejuetc.commons.base.filter.login.SaasLoginToken;
import com.ejuetc.commons.base.response.ApiResponse;
import com.ejuetc.commons.base.utils.DateTimeUtils;

import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@RefreshScope
@RequiredArgsConstructor
@Slf4j
@RestController
@Transactional(rollbackFor = Exception.class)
public class ReportImpl implements ReportAPI {

    private final BrokerRpt brokerRpt;
    private final TaskRpt taskRpt;
    private final KerApi kerApi;

	@Override
	public ApiResponse<ReportCreateDTO> doReport(SaasLoginToken saasLoginToken, @Valid ReportCreatePO po) {
		Broker broker = brokerRpt.findById(saasLoginToken.getUserId()).orElseThrow(() -> new BusinessException("bc.cpm.aixg.1002"));
//		List<Task> taskList4Ing = taskRpt.findAllByTypeAndStatus(broker, TaskDTO.Type.REPORT, TaskDTO.Status.ING);
//		if (taskList4Ing != null && taskList4Ing.size() > 0) throw new BusinessException("bc.cpm.aixg.1008");
		ReportCreateDTO dto = kerApi.doReport(broker.getKerId(), po);
		Report report = new Report(broker, dto.getReportId(), po);
		report = taskRpt.save(report);
		if (StringUtils.isBlank(broker.getMessageOpenId())) {
	    	String qrCodeUrl = getBean(WechatService.class).getQrCodeTicket(report.getId());
	    	dto.setQrCodeUrl(qrCodeUrl);
		} else {
			report.setOpenId(broker.getMessageOpenId());
			dto.setFollowStatus(true);
		}
		return succ(dto);
	}

	@Override
	public ApiResponse<List<JSONObject>> reportList(SaasLoginToken saasLoginToken, @Valid ListQueryPO po) {
		Broker broker = brokerRpt.findById(saasLoginToken.getUserId()).orElseThrow(() -> new BusinessException("bc.cpm.aixg.1002"));
		return kerApi.reportList(broker.getKerId(), po.getKeyword(), po.getPage(), po.getPageSize());
	}

	@Override
	public ApiResponse<ReportDetailDTO> reportDetail(SaasLoginToken saasLoginToken, String reportId) {
		Broker broker = brokerRpt.findById(saasLoginToken.getUserId()).orElseThrow(() -> new BusinessException("bc.cpm.aixg.1002"));
		ReportDetailDTO result = kerApi.reportDetail(broker.getKerId(), reportId);
		if (result != null && StringUtils.isNotBlank(result.getContent())) {
			if (broker.getCurrentlyValid()) {
				result.setBroker(convert2DTO(broker, new BrokerDTO()));
			}
			if (StringUtils.isNotBlank(result.getContent())) {
				Report report = getReport(reportId);
				result.setHouseInfo(getHouseInfo(report));
				if (report != null) {
					result.setTitle(report.getTitle());
					result.setCreatedAt(DateTimeUtils.dateTime(report.getCreateTime()));
					result.setCreatedAtShow(kerApi.buildCreated_at_show(DateTimeUtils.dateTime(report.getCreateTime())));
				}
			}
		}
		return succ(result);
	}
	
	private List getHouseInfo(Report report) {
		if (report.getHouseInfos() != null && report.getHouseInfos().size() > 0) return report.getHouseInfos4DelegationVO();
		if (report.getHouseIds() != null && report.getHouseIds().size() > 0) {
			List<Long> delegationIds = report.getHouseIds().stream().map(s -> {
	            try {
	                return Long.valueOf(s);
	            } catch (NumberFormatException e) {
	                return null;
	            }
	        }).collect(Collectors.toList());
			return new Report().getHouseInfo(delegationIds);
		}
		return new ArrayList<>();
	}
	
	private Report getReport(String reportId) {
		List<Report> reportList = taskRpt.findAndLockByReportId(reportId);
		if (reportList != null && reportList.size() > 0) return reportList.get(0);
		return null;
	}

	@Override
	public ApiResponse<ReportDetailDTO> reportDetail4Share(String reportId) {
		List<Report> reportList = taskRpt.findAndLockByReportId(reportId);
		if (reportList == null || reportList.size() == 0) throw new BusinessException("bc.cpm.aixg.1007");
		Report report = reportList.get(0);
		ReportDetailDTO result = kerApi.reportDetail(report.getBroker().getKerId(), reportId);
		if (result != null && StringUtils.isNotBlank(result.getContent())) {
			if (report.getBroker().getCurrentlyValid()) {
				result.setBroker(convert2DTO(report.getBroker(), new BrokerDTO()));
			}
			if (StringUtils.isNotBlank(result.getContent())) {
				result.setHouseInfo(getHouseInfo(report));
				result.setTitle(report.getTitle());
				result.setCreatedAt(DateTimeUtils.dateTime(report.getCreateTime()));
				result.setCreatedAtShow(kerApi.buildCreated_at_show(DateTimeUtils.dateTime(report.getCreateTime())));
			}
		}
		return succ(result);
	}

	@Override
	public ApiResponse<ReportInfoDTO> reportInfo(SaasLoginToken saasLoginToken) {
		return succ(new ReportInfoDTO());
	}

	@Override
	public ApiResponse<Boolean> deleteReport(SaasLoginToken saasLoginToken, String reportId) {
		Broker broker = brokerRpt.findById(saasLoginToken.getUserId()).orElseThrow(() -> new BusinessException("bc.cpm.aixg.1002"));
        List<Report> reportList = taskRpt.findAndLockByReportId(reportId);
    	if (reportList != null && reportList.size() > 0) {
        	Report report = reportList.get(0);
        	if (!broker.getId().equals(report.getBroker().getId())) throw new BusinessException("bc.cpm.aixg.1005");
        	report.setLogicDelete();
        	boolean result = kerApi.reportDelete(broker.getKerId(), reportId);
    		if (!result) return succ(Boolean.FALSE);
    	}
    	return succ(Boolean.TRUE);
	}

}
