package com.dcai.aixg.impl;

import com.alibaba.fastjson.JSONObject;
import com.dcai.aixg.api.ExternalAPI;
import com.dcai.aixg.api.OrderAPI;
import com.dcai.aixg.domain.broker.Broker;
import com.dcai.aixg.domain.broker.BrokerRpt;
import com.dcai.aixg.domain.order.Order;
import com.dcai.aixg.domain.order.OrderRpt;
import com.dcai.aixg.dto.GoodsDTO;
import com.dcai.aixg.dto.OrderDTO;
import com.dcai.aixg.kerApi.KerApi;
import com.dcai.aixg.pro.CreateOrderPO;
import com.dcai.aixg.pro.OrderPayCallBackPO;
import com.dcai.aixg.pro.UpdateCityPo;
import com.dcai.gateway.sdk.GatewaySDK;
import com.ejuetc.commons.base.exception.BusinessException;
import com.ejuetc.commons.base.filter.login.SaasLoginToken;
import com.ejuetc.commons.base.response.ApiResponse;
import com.ejuetc.commons.base.utils.StringUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.util.List;

import static com.ejuetc.commons.base.querydomain.impl.Domain2DTOConvert.convert2DTO;

@RefreshScope
@RequiredArgsConstructor
@Slf4j
@RestController
@Transactional(rollbackFor = Exception.class)
public class OrderImpl implements OrderAPI {

    private final BrokerRpt brokerRpt;
    private final OrderRpt orderRpt;
    private final KerApi kerApi;

    @Value("${dcai.aixg.order.callBackUrl}")
    private String callBackUrl;

    @Override
    public ApiResponse<List<GoodsDTO>> goods(SaasLoginToken loginToken) {
        List<GoodsDTO> goodsDTOList = kerApi.getGoods();
        return ApiResponse.succ(goodsDTOList);
    }

    @Override
    public ApiResponse<List<GoodsDTO>> vasGoods(SaasLoginToken loginToken) {
        Broker broker = brokerRpt.findAndLockById(loginToken.getUserId());
        if (broker == null) {
            throw new BusinessException("bc.cpm.aixg.1002");
        }
        List<GoodsDTO> goodsDTOList = kerApi.getVasGoods(broker.getKerId());
        GoodsDTO goodsDTO = goodsDTOList.stream().filter(t -> t.getPrice().compareTo(BigDecimal.ZERO) == 0).findFirst().orElse(null);
        if (goodsDTO != null && !broker.getCurrentlyValid()) {
            broker.setBusinessCardIndate(goodsDTO.getExpireDays());
        }
        return ApiResponse.succ(goodsDTOList);
    }

    @Override
    public ApiResponse<OrderDTO> create(SaasLoginToken loginToken, CreateOrderPO po) {
        Broker broker = brokerRpt.findAndLockById(loginToken.getUserId());
        if (broker == null) {
            throw new BusinessException("bc.cpm.aixg.1002");
        }
        List<GoodsDTO> goodsDTOList = po.getGoodsType() == OrderDTO.Type.POINT ? kerApi.getGoods() : kerApi.getVasGoods(broker.getKerId());
        GoodsDTO goodsDTO = goodsDTOList.stream().filter(t -> t.getId().equals(po.getGoodsId())).findFirst().orElse(null);
        if (goodsDTO == null) {
            throw new BusinessException("bc.cpm.aixg.1004");
        }
        Order order = new Order(broker, goodsDTO, po);
        JSONObject jsonObject = kerApi.createOrder(broker.getKerId(), goodsDTO.getId(), po.getOpenId(), callBackUrl);
        order.payOrder(jsonObject);
        if (StringUtils.isBlank(broker.getOpenId()) && order.getStatus() == OrderDTO.Status.PAYING) {
            broker.setOpenId(po.getOpenId());
        }
        orderRpt.save(order);
        return ApiResponse.succ(convert2DTO(order, new OrderDTO()));
    }



    @Override
    @Scheduled(cron = "${scheduler.tasks.queryOrder}")
    public void orderPayIng() {
        LocalDateTime updateTime = LocalDateTime.now().minusDays(3);
        List<Order> orders = orderRpt.findAndLockByStatusAndUpdateTime(OrderDTO.Status.PAYING, updateTime);
        for (Order order : orders) {
            try {
                JSONObject jsonObject = kerApi.queryOrder(order.getBroker().getKerId(), order.getKerOrderNo());
                order.payCallBack(new OrderPayCallBackPO()
                        .setOrderNo(order.getKerOrderNo())
                        .setExternalOrderNo(jsonObject.containsKey("externalOrderNo") ? jsonObject.getString("externalOrderNo") : null)
                        .setPayAmount(jsonObject.containsKey("payAmount") ? jsonObject.getBigDecimal("payAmount") : null)
                        .setPayTime(jsonObject.containsKey("payTime") ? Timestamp.valueOf(jsonObject.getString("payTime")).toLocalDateTime() : null)
                        .setPayStatus(jsonObject.containsKey("payStatus") ? jsonObject.getInteger("payStatus") : null)
                );
            } catch (Exception e) {
                log.error(e.getMessage());
            }
        }
    }

    private final GatewaySDK gatewaySDK;

    public void testCallBack() {
       ApiResponse<List<OrderDTO>> response = gatewaySDK.feignClient(ExternalAPI.class, "http://aixg-svc.dcai.svc.cluster.local/aixg")
               .orderPayCallBack(
                       new OrderPayCallBackPO()
                               .setOrderNo("11111")
                       .setExternalOrderNo("111")
                       .setPayTime(LocalDateTime.now())
                       .setPayStatus(1)
                       .setPayAmount(BigDecimal.ZERO)
               );
        System.out.println(response);
    }


    public void testApiUpdateCity() {
        ApiResponse<String> response = gatewaySDK.feignClient(ExternalAPI.class, "http://aixg-svc.dcai.svc.cluster.local/aixg")
                .updateCity(new UpdateCityPo().setCityId("310100").setCityName("上海").setUserId(9096743779674658563L));
        System.out.println(response);
    }
}
