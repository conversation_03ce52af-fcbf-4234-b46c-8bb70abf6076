package com.dcai.aixg.impl;

import static com.dcai.aixg.dto.FlowDTO.SrcType.TASK;
import static com.dcai.aixg.dto.FlowDTO.SrcType.TWEET;
import static com.ejuetc.commons.base.querydomain.impl.Domain2DTOConvert.convert2DTO;
import static com.ejuetc.commons.base.response.ApiResponse.succ;
import static com.ejuetc.commons.base.spring.SpringUtil.getBean;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.RestController;

import com.alibaba.cloud.commons.lang.StringUtils;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.dcai.aixg.api.NodeAPI;
import com.dcai.aixg.api.WriteAPI;
import com.dcai.aixg.domain.aiagent.flow.Flow;
import com.dcai.aixg.domain.aiagent.flow.FlowRpt;
import com.dcai.aixg.domain.aiagent.flow.FlowService;
import com.dcai.aixg.domain.broker.Broker;
import com.dcai.aixg.domain.broker.BrokerRpt;
import com.dcai.aixg.domain.task.Policy;
import com.dcai.aixg.domain.task.PolicyRpt;
import com.dcai.aixg.domain.task.Report;
import com.dcai.aixg.domain.task.Task;
import com.dcai.aixg.domain.task.TaskRpt;
import com.dcai.aixg.domain.task.Write;
import com.dcai.aixg.dto.BrokerDTO;
import com.dcai.aixg.dto.FlowDTO;
import com.dcai.aixg.dto.NodeDTO;
import com.dcai.aixg.dto.task.TaskDTO;
import com.dcai.aixg.dto.task.WriteCreateDTO;
import com.dcai.aixg.dto.task.WriteDetailDTO;
import com.dcai.aixg.dto.task.WriteInfoDTO;
import com.dcai.aixg.dto.task.WriteShareDTO;
import com.dcai.aixg.integration.wechat.WechatService;
import com.dcai.aixg.kerApi.KerApi;
import com.dcai.aixg.pro.LaunchFlowPO;
import com.dcai.aixg.pro.search.ListQueryPO;
import com.dcai.aixg.pro.task.WriteCreatePO;
import com.dcai.aixg.pro.task.WriteDataCreatePO;
import com.dcai.aixg.pro.task.WriteSharePO;
import com.ejuetc.commons.base.exception.BusinessException;
import com.ejuetc.commons.base.filter.login.SaasLoginToken;
import com.ejuetc.commons.base.response.ApiResponse;
import com.ejuetc.commons.base.utils.ThreadUtils;
import com.ejuetc.consumer.api.delegation.ApiQueryListPO;
import com.ejuetc.consumer.api.delegation.DelegationAPI;
import com.ejuetc.consumer.web.vo.DelegationVO;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;

import jakarta.persistence.criteria.Predicate;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@RefreshScope
@RequiredArgsConstructor
@Slf4j
@RestController
@Transactional(rollbackFor = Exception.class)
public class WriteImpl implements WriteAPI {

    private final BrokerRpt brokerRpt;
    private final TaskRpt taskRpt;
    private final FlowRpt flowRpt;
    private final PolicyRpt policyRpt;
    private final KerApi kerApi;

    @Autowired
    private FlowService flowService;

    @Override
    public ApiResponse<List<String>> policyList(SaasLoginToken saasLoginToken, String cityName) {
        Broker broker = brokerRpt.findById(saasLoginToken.getUserId()).orElseThrow(() -> new BusinessException("bc.cpm.aixg.1002"));
        List<Policy> policyList = policyRpt.findByCityName(cityName);
        return succ(policyList.stream().map(Policy::getTitle).collect(Collectors.toList()));
    }

    @Override
    public ApiResponse<WriteCreateDTO> doWrite(SaasLoginToken saasLoginToken, @Valid WriteCreatePO po) {
        log.info("WriteImpl.doWrite, po={}", JSONObject.toJSONString(po));
        po.checkParams();
        //buildWriteCreatePO(po);
        Broker broker = brokerRpt.findById(saasLoginToken.getUserId()).orElseThrow(() -> new BusinessException("bc.cpm.aixg.1002"));
        po.setUserId(broker.getKerId());
        Write write = new Write(broker, po);
        write = getBean(WriteImpl.class).createWrite(write);
        LaunchFlowPO launchFlowPO = new LaunchFlowPO()
                .setSrcType(TASK)
                .setSrcId(write.getId())
                .setConfigCode(po.getConfigCode())
                .setRequest(po.toMap())
                .setRunMode(po.getRunMode());
        FlowDTO flowDTO = null;
        try {
        	ApiResponse<FlowDTO> flowResp = flowService.launch(launchFlowPO);
        	if (flowResp == null) throw new BusinessException("bc.cpm.aixg.1002");
        	if (!flowResp.isSucc()) throw new BusinessException("bc.cpm.aixg.1014", flowResp.getMessage());
        	flowDTO = flowResp.getData();
        } catch (Exception e) {
        	throw e;
        }
        write.setFlowId(flowDTO.getId());
        NodeDTO currentNode = flowDTO.getCurrentNode();
        WriteCreateDTO dto = new WriteCreateDTO()
        		.setTaskId(write.getId())
        		.setWriteId(currentNode.getProcessInfo2())
        		.setArticleId(currentNode.getProcessInfo3());
        if (StringUtils.isBlank(broker.getMessageOpenId())) {
            String qrCodeUrl = getBean(WechatService.class).getQrCodeTicket(write.getId());
            dto.setQrCodeUrl(qrCodeUrl);
        } else {
            write.setOpenId(broker.getMessageOpenId());
            dto.setFollowStatus(true);
        }
        if (po.getWriteType().equals("17")) {
        	//价值评估报告
        	ThreadUtils.asyncExec(() -> getBean(WriteImpl.class).doGetData4PriceAssessment(currentNode.getId(), po.makeWriteDataCreatePO(po.getHouseInfo().get(0))));
        } else if (po.getWriteType().equals("12")) {
    		//评测对比专家:需要单独调用
        	//getBean(WriteImpl.class).doGetData4CommunityCompare(null);
        }
        return succ(dto);
    }
    
    private void buildWriteCreatePO(WriteCreatePO po) {
    	if (po.getHouseInfo() != null && po.getHouseInfo().size() > 0) {
        	List<Long> delegationIds = po.getHouseInfo().stream().map(e -> e.getLong("id")).collect(Collectors.toList());
        	ApiResponse<List<DelegationVO>> delegationResp = getBean(DelegationAPI.class).queryDetail(new ApiQueryListPO().setDelegationIds(delegationIds));
        	if (delegationResp != null && delegationResp.isSucc()) {
        		List<DelegationVO> delegationList = delegationResp.getData();
        		if (delegationList != null && delegationList.size() > 0 && po.getHouseInfo().size() == delegationList.size()) {
        			List<JSONObject> delegationJsons = delegationList.stream().map(d -> {
        				ObjectMapper mapper = new ObjectMapper();
        				String jsonstr = null;
						try {
							jsonstr = mapper.writeValueAsString(d);
						} catch (JsonProcessingException e1) {
						}
        				return JSON.parseObject(jsonstr);
        			}).collect(Collectors.toList());
        			po.setHouseInfo(delegationJsons);
        		}
        	}
        }
    	if (po.getHouseCompareA() != null) {
        	List<Long> delegationIds = Arrays.asList(po.getHouseCompareA().getLong("id"));
    		ApiResponse<List<DelegationVO>> delegationResp = getBean(DelegationAPI.class).queryDetail(new ApiQueryListPO().setDelegationIds(delegationIds));
        	if (delegationResp != null && delegationResp.isSucc()) {
        		List<DelegationVO> delegationList = delegationResp.getData();
        		if (delegationList != null && delegationList.size() > 0 && po.getHouseInfo().size() == delegationList.size()) {
        			ObjectMapper mapper = new ObjectMapper();
    				String jsonstr = null;
					try {
						jsonstr = mapper.writeValueAsString(delegationList.get(0));
					} catch (JsonProcessingException e1) {
					}
        			po.setHouseCompareA(JSON.parseObject(jsonstr));
        		}
        	}
    	}
    	if (po.getHouseCompareB() != null) {
        	List<Long> delegationIds = Arrays.asList(po.getHouseCompareB().getLong("id"));
    		ApiResponse<List<DelegationVO>> delegationResp = getBean(DelegationAPI.class).queryDetail(new ApiQueryListPO().setDelegationIds(delegationIds));
        	if (delegationResp != null && delegationResp.isSucc()) {
        		List<DelegationVO> delegationList = delegationResp.getData();
        		if (delegationList != null && delegationList.size() > 0 && po.getHouseInfo().size() == delegationList.size()) {
        			ObjectMapper mapper = new ObjectMapper();
    				String jsonstr = null;
					try {
						jsonstr = mapper.writeValueAsString(delegationList.get(0));
					} catch (JsonProcessingException e1) {
					}
        			po.setHouseCompareB(JSON.parseObject(jsonstr));
        		}
        	}
    	}
    }

    @Transactional(propagation = Propagation.REQUIRES_NEW)
    protected Write createWrite(Write write) {
        return taskRpt.save(write);
    }

    @Override
    public ApiResponse<WriteCreateDTO> doWriteByTaskId(Long taskId, Long flowId, WriteCreatePO po) {
        log.info("WriteImpl.doWriteByTaskId, taskId={}, flowId={}, po={}", taskId, flowId, JSONObject.toJSONString(po));
        po.checkParams();
        Write write = taskRpt.findByTaskId(taskId);
        if (write == null) throw new BusinessException("bc.cpm.aixg.1006");
        WriteCreateDTO dto = kerApi.doWrite(write.getBroker().getKerId(), po);
        write.writeSubmit(dto, flowId);
        return succ(dto);
    }

	@Override
	public ApiResponse<WriteCreateDTO> doWrite(Long kerId, WriteCreatePO po) {
		log.info("WriteImpl.doWrite, kerId={}, po={}", kerId, JSONObject.toJSONString(po));
		WriteCreateDTO dto = kerApi.doWrite(kerId, po);
		return succ(dto);
	}

    @Override
    public ApiResponse<List<WriteDetailDTO>> libraryWriteList(SaasLoginToken saasLoginToken, @Valid ListQueryPO po) {
    	Broker broker = brokerRpt.findById(saasLoginToken.getUserId()).orElseThrow(() -> new BusinessException("bc.cpm.aixg.1002"));
    	Page<Task> task = findWriteList(broker, po);
    	List<WriteDetailDTO> result = task.stream().map(t -> t.makeWriteDetailDTO()).collect(Collectors.toList());
    	return succ(result);
    }
    
    private Page<Task> findWriteList(Broker broker, ListQueryPO po) {
    	Pageable pageable = PageRequest.of(po.getPage(), po.getPageSize());
        Sort sort = Sort.by(Sort.Direction.DESC, "createTime");
        Specification<Task> specification = (root, query, cb) -> {
            List<Predicate> predicates = new ArrayList<>();
            predicates.add(cb.equal(root.get("broker"), broker));
            if (StringUtils.isNotBlank(po.getWriteType())) {
                predicates.add(cb.equal(root.get("writeType"), po.getWriteType()));
            }
            if (StringUtils.isNotBlank(po.getSubType())){
                predicates.add(cb.equal(root.get("subType"), po.getSubType()));
            }
            if (po.getStartDate() != null) {
                predicates.add(cb.greaterThanOrEqualTo(root.get("createTime"), po.getStartDate()));
            }
            if (po.getEndDate() != null) {
                predicates.add(cb.lessThanOrEqualTo(root.get("createTime"), po.getEndDate()));
            }
            if (po.getStatus() != null) {
            	predicates.add(cb.equal(root.get("status"), po.getStatus()));
            }
            return query.where(predicates.toArray(new Predicate[predicates.size()])).getRestriction();
        };
    	return taskRpt.findAll(specification, pageable);
	}

    @Override
    public ApiResponse<List<WriteDetailDTO>> writeList(SaasLoginToken saasLoginToken, @Valid ListQueryPO po) {
        Broker broker = brokerRpt.findById(saasLoginToken.getUserId()).orElseThrow(() -> new BusinessException("bc.cpm.aixg.1002"));
        return kerApi.writeList(broker.getKerId(), po);
    }

    @Override
    public ApiResponse<WriteDetailDTO> writeDetail(SaasLoginToken saasLoginToken, String writeId) {
        Broker broker = brokerRpt.findById(saasLoginToken.getUserId()).orElseThrow(() -> new BusinessException("bc.cpm.aixg.1002"));
        WriteDetailDTO result = kerApi.writeDetail(broker.getKerId(), writeId);
        if (result != null && result.getTaskStatus().equals("DONE")) {
            if (broker.getCurrentlyValid()) {
                result.setBroker(convert2DTO(broker, new BrokerDTO()));
            }
            if (StringUtils.isNotBlank(result.getContent())) {
                List<Write> writeList = taskRpt.findAndLockByWriteId(writeId);
                if (writeList != null && writeList.size() > 0) {
                    Write write = writeList.get(0);
                    result.setContent(write.getContent());
                    if (StringUtils.isNotBlank(write.getContent())) result.setContentJson(JSONObject.parseObject(write.getContent()));
                }
            }
        }
        return succ(result);
    }

    @Override
    public ApiResponse<WriteDetailDTO> writeDetail4Share(String writeId) {
        List<Write> writeList = taskRpt.findAndLockByWriteId(writeId);
        if (writeList == null || writeList.size() == 0) throw new BusinessException("bc.cpm.aixg.1006");
        Write write = writeList.get(0);
        WriteDetailDTO result = kerApi.writeDetail(write.getBroker().getKerId(), writeId);
        if (result != null && result.getTaskStatus().equals("DONE")) {
            result.setContent(write.getContent());
            if (StringUtils.isNotBlank(write.getContent())) result.setContentJson(JSONObject.parseObject(write.getContent()));
            if (write.getBroker().getCurrentlyValid()) {
                result.setBroker(convert2DTO(write.getBroker(), new BrokerDTO()));
            }
        }
        return succ(result);
    }

    @Override
    public ApiResponse<WriteInfoDTO> writeInfo(SaasLoginToken saasLoginToken) {
        return succ(new WriteInfoDTO());
    }

    @Override
    public ApiResponse<Boolean> deleteWrite(SaasLoginToken saasLoginToken, String writeId) {
        Broker broker = brokerRpt.findById(saasLoginToken.getUserId()).orElseThrow(() -> new BusinessException("bc.cpm.aixg.1002"));
        List<Write> writeList = taskRpt.findAndLockByWriteId(writeId);
        if (writeList != null && writeList.size() > 0) {
            Write write = writeList.get(0);
            if (!broker.getId().equals(write.getBroker().getId())) throw new BusinessException("bc.cpm.aixg.1005");
            write.setLogicDelete();
            boolean result = kerApi.writeDelete(broker.getKerId(), writeId);
            if (!result) return succ(Boolean.FALSE);
        }
        return succ(Boolean.TRUE);
    }

    @Override
    public ApiResponse<WriteShareDTO> createWriteShare(SaasLoginToken saasLoginToken, WriteSharePO po) {
        Broker broker = brokerRpt.findById(saasLoginToken.getUserId()).orElseThrow(() -> new BusinessException("bc.cpm.aixg.1002"));
        Write write = (Write) taskRpt.findAndLockById(po.getTaskId());
        if (write == null) throw new BusinessException("bc.cpm.aixg.1006");
        if (write.getSubType() != TaskDTO.SubType.ORIGIN) throw new BusinessException("bc.cpm.aixg.1014", "仅支持对原始文章进行分享操作");
        Write shareWrite = new Write(write, po.getSubType());
        shareWrite = getBean(WriteImpl.class).createWrite(shareWrite);
        LaunchFlowPO launchFlowPO = new LaunchFlowPO()
                .setSrcType(TWEET)
                .setSrcId(shareWrite.getId())
                .setConfigCode((shareWrite.getSubType() == TaskDTO.SubType.REDNOTE) ? "SHARE_REDNOTE_FLOW" : "SHARE_WECHAT_FLOW")
                .setRequest(JSON.parseObject(write.getContent()))
                .setRunMode(LaunchFlowPO.RunMode.SYNC);
        ApiResponse<FlowDTO> flowResponse = flowService.launch(launchFlowPO);
        WriteShareDTO result = new WriteShareDTO();
        result.setContent(flowResponse.getData().getResponse());
        result.setChapterImages(shareWrite.generatorImage(po.getChapters()));
        return succ(result);
    }

    @Override
    public ApiResponse<String> changeFollow(SaasLoginToken saasLoginToken, String writeId) {
        List<Write> writeList = taskRpt.findAndLockByWriteId(writeId);
        if (writeList != null && writeList.isEmpty()) {
            Write write = writeList.get(0);
            write.setOpenId(null);
            String qrCodeUrl = getBean(WechatService.class).getQrCodeTicket(write.getId());
            return succ(qrCodeUrl);
        }
        List<Report> reportList = taskRpt.findAndLockByReportId(writeId);
        if (reportList != null && reportList.size() > 0) {
            Report report = reportList.get(0);
            report.setOpenId(null);
            String qrCodeUrl = getBean(WechatService.class).getQrCodeTicket(report.getId());
            return succ(qrCodeUrl);
        }
        throw new BusinessException("bc.cpm.aixg.1006");
    }

	@Override
	public ApiResponse<WriteCreateDTO> createWrite4BrokerByFlowId(Long brokerId, Long flowId) {
		Broker broker = brokerRpt.findById(brokerId).orElseThrow(() -> new BusinessException("bc.cpm.aixg.1002"));
		Flow flow = flowRpt.findById(flowId).orElseThrow(() -> new BusinessException("bc.cpm.aixg.1002"));
		Write write = new Write(broker, flow);
        write = getBean(WriteImpl.class).createWrite(write);
        WriteCreateDTO dto = new WriteCreateDTO().setTaskId(write.getId());
		return succ(dto);
	}

	@Override
	public ApiResponse<List<String>> lifeInfos(SaasLoginToken saasLoginToken) {
		Broker broker = brokerRpt.findById(saasLoginToken.getUserId()).orElseThrow(() -> new BusinessException("bc.cpm.aixg.1002"));
		List<String> result = Arrays.asList("商场开业", "招商引资", "地铁开挖", "道路拓宽", "学校新建", "办学政策改变", "动迁动态", "新建医院", "名医/名师常驻", "其他");
		return succ(result);
	}

	@Override
    @Transactional(propagation = Propagation.REQUIRES_NEW)
	public ApiResponse<WriteCreateDTO> notifyWriteResult(Long taskId, FlowDTO.Status status, String response) {
        log.info("WriteImpl.notifyWriteResult, taskId={}, status={}, response={}", taskId, status, response);
		Write write = taskRpt.findByTaskId(taskId);
		write.handleCallBack4Create(status);
		if (write.getStatus() == TaskDTO.Status.DONE) {
			JSONObject jsonObject = JSONObject.parseObject(response);
			write.buildTaskDetail(jsonObject.getString("title"), null, response, LocalDateTime.now());
			//write.handleSendMessage();
			if (write.getWriteType().equals("17")) {
				kerApi.noticeDataResult(write.getArticleId(), "2", write.getTitle(), write.getTitle());
			}
		}
		taskRpt.save(write);
		return succ();
	}

	@Override
    public ApiResponse<?> doGetData4PriceAssessment(Long nodeId, WriteDataCreatePO po) {
    	ApiResponse<JSONObject> data = getBean(WriteAPI.class).getData4PriceAssessment(po);
    	NodeDTO.Status status = data.isSucc() ? NodeDTO.Status.SUCCESS : NodeDTO.Status.FAILED;
        return getBean(NodeAPI.class).receiveAsyncResponse(nodeId, status, data.getData().toJSONString());
    }

	@Override
	public ApiResponse<JSONObject> getData4PriceAssessment(WriteDataCreatePO po) {
		JSONObject result = kerApi.getData4PriceAssessment(po);
		return succ(result);
	}

	@Override
	public ApiResponse<String> getData4CommunityCompare(List<WriteDataCreatePO> pos) {
		String result = kerApi.getData4CommunityCompare(pos);
		return succ(result);
	}

	@Override
	public ApiResponse<String> doGetData4CommunityCompare(List<WriteDataCreatePO> pos) {
		ApiResponse<String> communityCompareId = getData4CommunityCompare(pos);
		return null;
	}

}
