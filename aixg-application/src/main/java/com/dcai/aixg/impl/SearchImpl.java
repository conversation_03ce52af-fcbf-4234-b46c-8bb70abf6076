package com.dcai.aixg.impl;

import static com.ejuetc.commons.base.filter.LoginTokenFilter.LOGIN_INFO_ATT;
import static com.ejuetc.commons.base.response.ApiResponse.succ;

import java.util.List;

import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.http.MediaType;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestAttribute;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import com.alibaba.cloud.commons.lang.StringUtils;
import com.dcai.aixg.api.SearchAPI;
import com.dcai.aixg.domain.broker.Broker;
import com.dcai.aixg.domain.broker.BrokerRpt;
import com.dcai.aixg.domain.search.Search;
import com.dcai.aixg.domain.search.SearchRpt;
import com.dcai.aixg.dto.search.ChatDetailDTO;
import com.dcai.aixg.dto.search.ChatListDTO;
import com.dcai.aixg.dto.search.HotSearchDTO;
import com.dcai.aixg.kerApi.KerApi;
import com.dcai.aixg.pro.search.ChatCreatePO;
import com.dcai.aixg.pro.search.ChatDetailPO;
import com.dcai.aixg.pro.search.ListQueryPO;
import com.dcai.aixg.pro.search.VoteSearchPO;
import com.ejuetc.commons.base.exception.BusinessException;
import com.ejuetc.commons.base.filter.login.SaasLoginToken;
import com.ejuetc.commons.base.response.ApiResponse;

import io.swagger.v3.oas.annotations.Operation;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@RefreshScope
@RequiredArgsConstructor
@Slf4j
@RestController
@Transactional(rollbackFor = Exception.class)
public class SearchImpl implements SearchAPI {

    private final BrokerRpt brokerRpt;
    private final SearchRpt searchRpt;
    private final KerApi kerApi;
    
	@Override
	public ApiResponse<List<HotSearchDTO>> getHotSearch() {
        List<HotSearchDTO> result = kerApi.getHotSearch();
        return succ(result);
	}
	
	@Operation(summary = "WEB_开始搜索/再答一次")
    @PostMapping(value = "/web/search/doSearch", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
	public SseEmitter doSearch(@RequestAttribute(value = LOGIN_INFO_ATT) SaasLoginToken saasLoginToken,
            @RequestBody @Valid ChatCreatePO po) {
        Broker broker = brokerRpt.findById(saasLoginToken.getUserId()).orElseThrow(() -> new BusinessException("bc.cpm.aixg.1002"));
        Search search = new Search(broker, po);
        SseEmitter result = kerApi.doSearch(broker.getKerId(), po.getChatId(), po.getQuery(), search);
        if (StringUtils.isNotEmpty(po.getChatId())) {
        	List<Search> searchList = searchRpt.findAndLockByChatId(po.getChatId());
        	if (searchList != null && searchList.size() > 0) {
        		search = searchList.get(0);
        		search.setVoteStatus(0);
        	}
        }
        searchRpt.save(search);
        return result;
	}
	
	@Operation(summary = "WEB_开始搜索/再答一次")
    @GetMapping(value = "/web/search/doSearch1", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
	public SseEmitter doSearch1() {
		ChatCreatePO po = new ChatCreatePO();
		po.setChatId("32");
		po.setQuery("上海静安慧芝湖花园小区");
		Long userId = 9094565244017103363L;
        Broker broker = brokerRpt.findById(userId).orElseThrow(() -> new BusinessException("bc.cpm.aixg.1002"));
        Search search = new Search(broker, po);
        SseEmitter result = kerApi.doSearch(broker.getKerId(), po.getChatId(), po.getQuery(), search);
        if (StringUtils.isNotEmpty(po.getChatId())) {
        	List<Search> searchList = searchRpt.findAndLockByChatId(po.getChatId());
        	if (searchList != null && searchList.size() > 0) {
        		search = searchList.get(0);
        		search.setVoteStatus(0);
        	}
        }
        searchRpt.save(search);
        return result;
	}

	@Override
	public ApiResponse<List<ChatListDTO>> chatList(SaasLoginToken saasLoginToken, @Valid ListQueryPO po) {
		Broker broker = brokerRpt.findById(saasLoginToken.getUserId()).orElseThrow(() -> new BusinessException("bc.cpm.aixg.1002"));
		ApiResponse<List<ChatListDTO>> result = kerApi.chatList(broker.getKerId(), po.getKeyword(), po.getPage(), po.getPageSize());
//		if (result != null && result.isSucc()) {
//			List<ChatListDTO> dtos = result.getData();
//			if (dtos != null && dtos.size() > 0) {
//				Map<String, ChatListDTO> chatListMap = dtos.stream().collect(Collectors.toMap(dto -> dto.getChatId(), dto -> dto));
//				List<Search> searchList = searchRpt.findByChatIds(result.getData().stream().map(dto -> dto.getChatId()).collect(Collectors.toList()));
//				List<ChatListDTO> data = searchList.stream().filter(s -> chatListMap.containsKey(s.getChatId())).map(s -> chatListMap.get(s.getChatId())).toList();
//				result.setData(data);
//			}
//		}
		return result;
	}

	@Override
	public ApiResponse<ChatDetailDTO> chatDetail(SaasLoginToken saasLoginToken, @Valid ChatDetailPO po) {
        Broker broker = brokerRpt.findById(saasLoginToken.getUserId()).orElseThrow(() -> new BusinessException("bc.cpm.aixg.1002"));
        ChatDetailDTO result = kerApi.chatDetail(broker.getKerId(), po.getChatId(), po.getLimit(), po.getLastMsgId());
        return succ(result);
	}

	@Override
	public ApiResponse<Boolean> deleteSearch(SaasLoginToken saasLoginToken, String chatId) {
        Broker broker = brokerRpt.findById(saasLoginToken.getUserId()).orElseThrow(() -> new BusinessException("bc.cpm.aixg.1002"));
        List<Search> searchList = searchRpt.findAndLockByChatId(chatId);
    	if (searchList != null && searchList.size() > 0) {
    		Search search = searchList.get(0);
        	if (!broker.getId().equals(search.getBroker().getId())) throw new BusinessException("bc.cpm.aixg.1005");
        	search.setLogicDelete();
        	boolean result = kerApi.chatDelete(broker.getKerId(), chatId);
    		if (!result) return succ(Boolean.FALSE);
    	}
    	return succ(Boolean.TRUE);
	}

	@Override
	public ApiResponse<Boolean> voteSearch(SaasLoginToken saasLoginToken, @Valid VoteSearchPO po) {
        Broker broker = brokerRpt.findById(saasLoginToken.getUserId()).orElseThrow(() -> new BusinessException("bc.cpm.aixg.1002"));
        List<Search> searchList = searchRpt.findAndLockByChatId(po.getChatId());
    	if (searchList == null || searchList.size() == 0) throw new BusinessException("bc.cpm.aixg.1004");
		Search search = searchList.get(0);
    	if (!broker.getId().equals(search.getBroker().getId())) throw new BusinessException("bc.cpm.aixg.1005");
    	search.setVoteStatus(po.getVoteStatus());
		return succ(Boolean.TRUE);
	}

}
