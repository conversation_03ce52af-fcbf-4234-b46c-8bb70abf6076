package com.dcai.aixg.dto.ai;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.util.List;

/**
 * 图表控件
 * 
 * <AUTHOR>
 */
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
@Schema(description = "图表控件")
public class ChartWidget extends DocumentWidget {
    
    /**
     * 横轴类别标签（柱状图/折线图必填，饼图不需要）
     */
    @Schema(description = "横轴类别标签")
    private List<String> cols;
    
    /**
     * 数据系列（格式根据图表类型变化）
     */
    @Schema(description = "数据系列")
    private List<ChartSeries> content;

    @Override
    public WidgetType getWidgetType() {
        return WidgetType.CHART;
    }
    

}
