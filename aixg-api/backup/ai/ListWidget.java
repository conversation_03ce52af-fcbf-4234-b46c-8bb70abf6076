package com.dcai.aixg.dto.ai;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.util.List;

/**
 * 列表控件
 * 
 * <AUTHOR>
 */
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
@Schema(description = "列表控件")
public class ListWidget extends DocumentWidget {
    
    /**
     * 列表项数组
     */
    @Schema(description = "列表项数组")
    private List<ListItem> content;

    @Override
    public WidgetType getWidgetType() {
        return WidgetType.LIST;
    }
    

}
