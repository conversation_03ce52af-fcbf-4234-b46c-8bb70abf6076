package com.dcai.aixg.dto.ai;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.util.List;

/**
 * 表格控件
 * 
 * <AUTHOR>
 */
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
@Schema(description = "表格控件")
public class TableWidget extends DocumentWidget {
    
    /**
     * 列标题数组（至少2列）
     */
    @Schema(description = "列标题数组")
    private List<String> cols;
    
    /**
     * 行数据数组（至少1行）
     */
    @Schema(description = "行数据数组")
    private List<List<TableCell>> content;

    @Override
    public WidgetType getWidgetType() {
        return WidgetType.TABLE;
    }
    

}
