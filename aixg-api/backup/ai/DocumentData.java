package com.dcai.aixg.dto.ai;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import java.util.List;

/**
 * AI生成的文档数据结构
 * 用于Spring AI结构化输出转换
 *
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@Schema(description = "AI生成的文档数据结构")
public class DocumentData {

    /**
     * 文档类型枚举
     *
     * <AUTHOR>
     */
    @Getter
    public enum DocumentType {
        POLICY_COMMENT("政策解读"),
        SALE_COMPARE("卖点对比"),//价值评测
        LIFE_EXPERT("生活专家"),
        HOUSING_EXPERT("房源专家"),
        MONTHLY_REPORT("月度报告"),
        INDUSTRY_ANALYST("行业分析"),
        VALUE_EVALUATION("价值评估");

        private final String description;

        DocumentType(String description) {
            this.description = description;
        }
    }

    /**
     * 文档ID，用于标识文档，可选
     */
    @Schema(description = "文档ID", example = "1234567890")
    private Long id;

    /**
     * 文档类型
     */
    @Schema(description = "文档类型")
    private DocumentType type;

    /**
     * 文档主标题
     */
    @Schema(description = "文档主标题", example = "继承与税费:2025年上海房屋继承政策解读与影响分析")
    private String title;

    /**
     * 文档副标题
     */
    @Schema(description = "文档副标题", example = "专业解读助您理解政策变化")
    private String subtitle;

    /**
     * 控件数组，包含文档中所有控件，按照自上而下的顺序排列
     */
    @Schema(description = "控件数组")
    private List<DocumentWidget> widgets;
}
