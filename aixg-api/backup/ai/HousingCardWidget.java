package com.dcai.aixg.dto.ai;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.util.List;

/**
 * 房产卡片控件
 * 
 * <AUTHOR>
 */
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
@Schema(description = "房产卡片控件")
public class HousingCardWidget extends DocumentWidget {
    
    /**
     * 房产名称
     */
    @Schema(description = "房产名称", example = "高兴花园")
    private String name;
    
    /**
     * 户型描述
     */
    @Schema(description = "户型描述", example = "1室1厅1卫")
    private String layout;
    
    /**
     * 建筑面积
     */
    @Schema(description = "建筑面积", example = "47m²")
    private String area;
    
    /**
     * 楼层信息
     */
    @Schema(description = "楼层信息", example = "6/6层")
    private String floor;
    
    /**
     * 地理位置
     */
    @Schema(description = "地理位置", example = "梅陇镇")
    private String location;
    
    /**
     * 总价
     */
    @Schema(description = "总价", example = "235万")
    private String price;
    
    /**
     * 单价
     */
    @Schema(description = "单价", example = "50,000元/m²")
    private String unitPrice;
    
    /**
     * 特色标签数组
     */
    @Schema(description = "特色标签数组")
    private List<String> tags;

    @Override
    public WidgetType getWidgetType() {
        return WidgetType.HOUSING_CARD;
    }
    

}
