package com.dcai.aixg.dto.ai;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

/**
 * 文本控件
 * 
 * <AUTHOR>
 */
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
@Schema(description = "文本控件")
public class TextWidget extends DocumentWidget {
    
    /**
     * 文本内容
     */
    @Schema(description = "文本内容", example = "这是一段文本内容")
    private String content;

    @Override
    public WidgetType getWidgetType() {
        return WidgetType.TEXT;
    }
    

}
