package com.dcai.aixg.dto.ai;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 表格单元格数据
 * 
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@Schema(description = "表格单元格数据")
public class TableCell {
    
    /**
     * 单元格数据类型枚举
     */
    public enum CellType {
        /**
         * 文本
         */
        TEXT,
        
        /**
         * 图片URL
         */
        IMAGE,
        
        /**
         * 进度值(0-100)
         */
        PROGRESS_BAR,
        
        /**
         * 涨跌幅
         */
        CHANGE
    }
    
    /**
     * 数据类型
     */
    @Schema(description = "数据类型", example = "TEXT")
    private CellType type;
    
    /**
     * 单元格标题
     */
    @Schema(description = "单元格标题", example = "标题")
    private String title;
    
    /**
     * 单元格值
     */
    @Schema(description = "单元格值", example = "内容")
    private Object content;
    
    /**
     * 是否推荐该选项（仅比较列有效）
     */
    @Schema(description = "是否推荐该选项", example = "true")
    private Boolean recommended;
    

}
