package com.dcai.aixg.dto.ai;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

/**
 * 图片控件
 * 
 * <AUTHOR>
 */
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
@Schema(description = "图片控件")
public class ImageWidget extends DocumentWidget {
    
    /**
     * 图片内容（URL或SVG代码）
     */
    @Schema(description = "图片内容", example = "https://example.com/image.jpg")
    private String content;

    @Override
    public WidgetType getWidgetType() {
        return WidgetType.IMAGE;
    }
    

}
