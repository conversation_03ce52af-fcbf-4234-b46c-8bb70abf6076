package com.dcai.aixg.dto.ai;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 列表项数据
 * 
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@Schema(description = "列表项数据")
public class ListItem {
    
    /**
     * 列表项标题
     */
    @Schema(description = "列表项标题", example = "项目标题")
    private String title;
    
    /**
     * 列表项内容
     */
    @Schema(description = "列表项内容", example = "项目内容描述")
    private String content;
    

}
