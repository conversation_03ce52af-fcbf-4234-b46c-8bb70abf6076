package com.dcai.aixg.api;

import static com.ejuetc.commons.base.filter.LoginTokenFilter.LOGIN_INFO_ATT;

import java.util.List;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestAttribute;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import com.alibaba.fastjson.JSONObject;
import com.dcai.aixg.dto.FlowDTO;
import com.dcai.aixg.dto.task.WriteCreateDTO;
import com.dcai.aixg.dto.task.WriteDetailDTO;
import com.dcai.aixg.dto.task.WriteInfoDTO;
import com.dcai.aixg.dto.task.WriteShareDTO;
import com.dcai.aixg.pro.search.ListQueryPO;
import com.dcai.aixg.pro.task.WriteCreatePO;
import com.dcai.aixg.pro.task.WriteDataCreatePO;
import com.dcai.aixg.pro.task.WriteSharePO;
import com.ejuetc.commons.base.filter.login.SaasLoginToken;
import com.ejuetc.commons.base.response.ApiResponse;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;

@Tag(name = "文章接口")
@FeignClient(name = "aixg", path = "/aixg", contextId = "WriteAPI")
public interface WriteAPI {

    @Operation(summary = "WEB_政策列表")
    @GetMapping("/web/write/policyList")
    ApiResponse<List<String>> policyList(@RequestAttribute(value = LOGIN_INFO_ATT) SaasLoginToken saasLoginToken,
    		@RequestParam(name = "cityName") String cityName);

    @Operation(summary = "WEB_文章写作")
    @PostMapping("/web/write/doWrite")
    ApiResponse<WriteCreateDTO> doWrite(@RequestAttribute(value = LOGIN_INFO_ATT) SaasLoginToken saasLoginToken,
            @RequestBody @Valid WriteCreatePO po);
    
    @Operation(summary = "API_指定文章推送给经纪人")
    @PostMapping("/api/write/createWrite4BrokerByFlowId")
    ApiResponse<WriteCreateDTO> createWrite4BrokerByFlowId(@RequestParam(name = "brokerId") Long brokerId,
    		@RequestParam(name = "flowId") Long flowId);
    
    @Operation(summary = "API_根据任务ID进行文章写作")
    @PostMapping("/api/write/doWriteByTaskId")
    ApiResponse<WriteCreateDTO> doWriteByTaskId(@RequestParam(name = "taskId") Long taskId,
    		@RequestParam(name = "flowId") Long flowId,
    		@RequestBody @Valid WriteCreatePO po);

    @Operation(summary = "API_进行文章写作")
    @PostMapping("/api/write/sendWrite")
    ApiResponse<WriteCreateDTO> doWrite(@RequestParam(name = "kerId") Long kerId,
                                                @RequestBody @Valid WriteCreatePO po);

    @Operation(summary = "WEB_资料库_文章列表")
    @PostMapping("/web/write/library_list")
    ApiResponse<List<WriteDetailDTO>> libraryWriteList(@RequestAttribute(value = LOGIN_INFO_ATT) SaasLoginToken saasLoginToken,
            @RequestBody @Valid ListQueryPO po);

    @Operation(summary = "WEB_文章列表")
    @PostMapping("/web/write/list")
    ApiResponse<List<WriteDetailDTO>> writeList(@RequestAttribute(value = LOGIN_INFO_ATT) SaasLoginToken saasLoginToken,
            @RequestBody @Valid ListQueryPO po);

    @Operation(summary = "WEB_文章详情")
    @GetMapping("/web/write/detail")
    ApiResponse<WriteDetailDTO> writeDetail(@RequestAttribute(value = LOGIN_INFO_ATT) SaasLoginToken saasLoginToken,
    		@RequestParam(name = "writeId") String writeId);

    @Operation(summary = "WEB_文章详情_分享用")
    @GetMapping("/web/write/detail_share")
    ApiResponse<WriteDetailDTO> writeDetail4Share(@RequestParam(name = "writeId") String writeId);

    @Operation(summary = "WEB_任务已发起_更换微信重新关注公众号")
    @GetMapping("/web/task/changeFollow")
    ApiResponse<String> changeFollow(@RequestAttribute(value = LOGIN_INFO_ATT) SaasLoginToken saasLoginToken,
    		@RequestParam(name = "writeId") String writeId);

    @Operation(summary = "WEB_文章主题和提示信息")
    @GetMapping("/web/write/info")
    ApiResponse<WriteInfoDTO> writeInfo(@RequestAttribute(value = LOGIN_INFO_ATT) SaasLoginToken saasLoginToken);

    @Operation(summary = "WEB_生活专家_猜你想说")
    @GetMapping("/web/write/lifeInfos")
    ApiResponse<List<String>> lifeInfos(@RequestAttribute(value = LOGIN_INFO_ATT) SaasLoginToken saasLoginToken);
    
    @Operation(summary = "WEB_删除文章")
    @GetMapping("/web/write/deleteWrite")
    ApiResponse<Boolean> deleteWrite(@RequestAttribute(value = LOGIN_INFO_ATT) SaasLoginToken saasLoginToken,
    		@RequestParam(name = "writeId") String writeId);
    
    @Operation(summary = "API_获取价格评测数据")
    @PostMapping("/api/write/getData4PriceAssessment")
    ApiResponse<JSONObject> getData4PriceAssessment(@RequestBody @Valid WriteDataCreatePO po);
    
    @Operation(summary = "API_执行获取价格评测数据")
    @PostMapping("/api/write/doGetData4PriceAssessment")
    ApiResponse<?> doGetData4PriceAssessment(@RequestParam(name = "nodeId") Long nodeId, @RequestBody @Valid WriteDataCreatePO po);
    
    @Operation(summary = "API_获取评测对比数据")
    @PostMapping("/api/write/getData4CommunityCompare")
    ApiResponse<String> getData4CommunityCompare(@RequestBody @Valid List<WriteDataCreatePO> pos);
    
    @Operation(summary = "API_执行获取评测对比数据")
    @PostMapping("/api/write/doGetData4CommunityCompare")
    ApiResponse<String> doGetData4CommunityCompare(@RequestBody @Valid List<WriteDataCreatePO> pos);
    
    @Operation(summary = "WEB_文章写作分享(小红书/微信朋友圈)")
    @PostMapping("/web/write/createWriteShare")
    ApiResponse<WriteShareDTO> createWriteShare(@RequestAttribute(value = LOGIN_INFO_ATT) SaasLoginToken saasLoginToken,
    		@RequestBody @Valid WriteSharePO po);
    
    @Operation(summary = "API_文章生成结果")
    @PostMapping("/api/write/notifyWriteResult")
    ApiResponse<WriteCreateDTO> notifyWriteResult(@RequestParam(name = "taskId") Long taskId, 
    		@RequestParam(name = "status") FlowDTO.Status status,
    		@RequestParam(name = "response") String response);

}
