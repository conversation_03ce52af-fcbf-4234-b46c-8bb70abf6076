package com.dcai.aixg.api;

import com.dcai.aixg.dto.OrderDTO;
import com.dcai.aixg.dto.task.TaskDTO;
import com.dcai.aixg.pro.ApiEditBrokerPO;
import com.dcai.aixg.pro.OrderPayCallBackPO;
import com.dcai.aixg.pro.UpdateCityPo;
import com.dcai.aixg.pro.task.CreateTaskCallBackPO;
import com.ejuetc.commons.base.response.ApiResponse;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

@Tag(name = "Api接口")
@FeignClient(name = "aixg", path = "/aixg", contextId = "externalAPI")
public interface ExternalAPI {

    @Operation(summary = "API_购买回调")
    @PostMapping("/api/order/orderPayCallBack")
    ApiResponse<List<OrderDTO>> orderPayCallBack(@RequestBody OrderPayCallBackPO po);

    @Operation(summary = "API_更新用户城市")
    @PostMapping("/api/broker/updateCity")
    ApiResponse<String> updateCity(@RequestBody UpdateCityPo po);

    @Operation(summary = "API_更新用户信息")
    @PostMapping("/api/broker/updateUserInfo")
    ApiResponse<String> updateUserInfo(@RequestBody ApiEditBrokerPO po);

    @Operation(summary = "API_任务回调")
    @PostMapping("/api/task/createTaskCallBack")
    ApiResponse<TaskDTO> createTaskCallBack(@RequestBody CreateTaskCallBackPO po);
    
}
