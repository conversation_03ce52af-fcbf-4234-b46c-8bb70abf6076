package com.dcai.aixg.api;

import static com.ejuetc.commons.base.filter.LoginTokenFilter.LOGIN_INFO_ATT;

import java.util.List;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestAttribute;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import com.dcai.aixg.dto.search.ChatDetailDTO;
import com.dcai.aixg.dto.search.ChatListDTO;
import com.dcai.aixg.dto.search.HotSearchDTO;
import com.dcai.aixg.pro.search.ChatDetailPO;
import com.dcai.aixg.pro.search.ListQueryPO;
import com.dcai.aixg.pro.search.VoteSearchPO;
import com.ejuetc.commons.base.filter.login.SaasLoginToken;
import com.ejuetc.commons.base.response.ApiResponse;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;

@Tag(name = "搜索接口")
@FeignClient(name = "aixg", path = "/aixg", contextId = "SearchAPI")
public interface SearchAPI {

    @Operation(summary = "WEB_获取热点搜索")
    @GetMapping("/web/search/getHotSearch")
    ApiResponse<List<HotSearchDTO>> getHotSearch();

    @Operation(summary = "WEB_删除搜索")
    @GetMapping("/web/search/deleteSearch")
    ApiResponse<Boolean> deleteSearch(@RequestAttribute(value = LOGIN_INFO_ATT) SaasLoginToken saasLoginToken,
    		@RequestParam(name = "chatId") String chatId);

    @Operation(summary = "WEB_搜索的好评/差评")
    @GetMapping("/web/search/voteSearch")
    ApiResponse<Boolean> voteSearch(@RequestAttribute(value = LOGIN_INFO_ATT) SaasLoginToken saasLoginToken,
    		@RequestBody @Valid  VoteSearchPO po);

    @Operation(summary = "WEB_搜索列表")
    @PostMapping("/web/search/list")
    ApiResponse<List<ChatListDTO>> chatList(@RequestAttribute(value = LOGIN_INFO_ATT) SaasLoginToken saasLoginToken,
            @RequestBody @Valid ListQueryPO po);

    @Operation(summary = "WEB_搜索详情")
    @PostMapping("/web/search/detail")
    ApiResponse<ChatDetailDTO> chatDetail(@RequestAttribute(value = LOGIN_INFO_ATT) SaasLoginToken saasLoginToken,
            @RequestBody @Valid ChatDetailPO po);

}
