package com.dcai.aixg.api;

import com.dcai.aixg.dto.GoodsDTO;
import com.dcai.aixg.dto.OrderDTO;
import com.dcai.aixg.pro.CreateOrderPO;
import com.dcai.aixg.pro.OrderPayCallBackPO;
import com.ejuetc.commons.base.filter.login.SaasLoginToken;
import com.ejuetc.commons.base.response.ApiResponse;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import java.util.List;

import static com.ejuetc.commons.base.filter.LoginTokenFilter.LOGIN_INFO_ATT;

@Tag(name = "订单接口")
@FeignClient(name = "aixg", path = "/aixg", contextId = "orderAPI")
public interface OrderAPI {

    @Operation(summary = "WEB_获取商品列表")
    @GetMapping("/web/order/goods")
    ApiResponse<List<GoodsDTO>> goods(@RequestAttribute(value = LOGIN_INFO_ATT) SaasLoginToken loginToken);

    @Operation(summary = "WEB_增值服务商品列表")
    @GetMapping("/web/order/vasGoods")
    ApiResponse<List<GoodsDTO>> vasGoods(@RequestAttribute(value = LOGIN_INFO_ATT) SaasLoginToken loginToken);

    @Operation(summary = "WEB_购买积分商品")
    @PostMapping("/web/order/create")
    ApiResponse<OrderDTO> create(@RequestAttribute(value = LOGIN_INFO_ATT) SaasLoginToken loginToken,
                                 @RequestBody @Valid CreateOrderPO po);

    @Operation(summary = "WEB_超时支付中查询")
    @GetMapping("/web/order/orderPayIng")
    void orderPayIng();
}
