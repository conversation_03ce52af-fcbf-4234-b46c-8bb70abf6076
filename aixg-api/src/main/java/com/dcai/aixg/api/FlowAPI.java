package com.dcai.aixg.api;

import com.dcai.aixg.dto.FlowDTO;
import com.dcai.aixg.pro.LaunchFlowPO;
import com.ejuetc.commons.base.response.ApiResponse;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * 生成相关API接口
 *
 * <AUTHOR>
 */
@Tag(name = "AI流程接口")
@FeignClient(name = "aixg", path = "/aixg", contextId = "FlowAPI")
public interface FlowAPI {

    @Operation(summary = "API_发起流程")
    @PostMapping("/api/flow/launch")
    ApiResponse<FlowDTO> launch(@RequestBody @Valid LaunchFlowPO po);
}
