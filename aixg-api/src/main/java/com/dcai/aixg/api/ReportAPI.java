package com.dcai.aixg.api;

import static com.ejuetc.commons.base.filter.LoginTokenFilter.LOGIN_INFO_ATT;

import java.util.List;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestAttribute;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import com.alibaba.fastjson.JSONObject;
import com.dcai.aixg.dto.task.ReportCreateDTO;
import com.dcai.aixg.dto.task.ReportDetailDTO;
import com.dcai.aixg.dto.task.ReportInfoDTO;
import com.dcai.aixg.pro.search.ListQueryPO;
import com.dcai.aixg.pro.task.ReportCreatePO;
import com.ejuetc.commons.base.filter.login.SaasLoginToken;
import com.ejuetc.commons.base.response.ApiResponse;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;

@Tag(name = "报告接口")
@FeignClient(name = "aixg", path = "/aixg", contextId = "ReportAPI")
public interface ReportAPI {

    @Operation(summary = "WEB_生成报告")
    @PostMapping("/web/report/doReport")
    ApiResponse<ReportCreateDTO> doReport(@RequestAttribute(value = LOGIN_INFO_ATT) SaasLoginToken saasLoginToken,
            @RequestBody @Valid ReportCreatePO po);

    @Operation(summary = "WEB_报告列表")
    @PostMapping("/web/report/list")
    ApiResponse<List<JSONObject>> reportList(@RequestAttribute(value = LOGIN_INFO_ATT) SaasLoginToken saasLoginToken,
            @RequestBody @Valid ListQueryPO po);

    @Operation(summary = "WEB_报告详情")
    @GetMapping("/web/report/detail")
    ApiResponse<ReportDetailDTO> reportDetail(@RequestAttribute(value = LOGIN_INFO_ATT) SaasLoginToken saasLoginToken,
    		@RequestParam(name = "reportId") String reportId);

    @Operation(summary = "WEB_报告详情_分享用")
    @GetMapping("/web/report/reportDetail4Share")
    ApiResponse<ReportDetailDTO> reportDetail4Share(@RequestParam(name = "reportId") String reportId);

    @Operation(summary = "WEB_报告主题和提示信息")
    @GetMapping("/web/report/info")
    ApiResponse<ReportInfoDTO> reportInfo(@RequestAttribute(value = LOGIN_INFO_ATT) SaasLoginToken saasLoginToken);
    
    @Operation(summary = "WEB_删除报告")
    @GetMapping("/web/report/deleteReport")
    ApiResponse<Boolean> deleteReport(@RequestAttribute(value = LOGIN_INFO_ATT) SaasLoginToken saasLoginToken,
    		@RequestParam(name = "reportId") String reportId);

}
