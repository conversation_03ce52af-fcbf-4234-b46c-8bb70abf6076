package com.dcai.aixg.pro.search;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@NoArgsConstructor
public class ChatDetailPO {

    @NotNull(message = "请先创建搜索")
    @Schema(name = "chatId")
    private String chatId;
    
    @Schema(name = "limit")
    private Integer limit = 20;
    
    @Schema(name = "lastMsgId")
    private String lastMsgId;

}
