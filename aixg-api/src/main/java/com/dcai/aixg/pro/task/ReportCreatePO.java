package com.dcai.aixg.pro.task;

import java.util.List;

import com.alibaba.fastjson.JSONObject;
import com.dcai.aixg.dto.task.TaskDTO;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@NoArgsConstructor
public class ReportCreatePO {
	
	@NotNull(message = "请输入报告类型")
    @Schema(description = "报告类型, COMPARE:优劣势对比, MARKET_POLICY_ANALYSIS:市场政策分析, SUPPORTING_FACILITY_RESEARCH:城市配套深度调研, PRICE_ASSESSMENT_REPORT:二手房价格评测")
    private TaskDTO.ReportType reportType;
	
	//@NotNull(message = "请输入报告主题")
	@Schema(description = "报告主题")
	private String topic;
    
    @Schema(description = "二手房价格评测")
	private ReportPriceAssessmentReportPO priceAssessmentReport;
	
    @Schema(description = "房源信息")
	private List<JSONObject> houses;
    
    @Schema(description = "优劣势对比参数")
	private Compare compare;
    
    @Schema(description = "配套报告参数")
	private SupportingFacilityReport supportingFacilityReport;
    
//    @Data
//    @NoArgsConstructor
//    @Accessors(chain = true)
//    @Schema(name = "二手房价格评测")
//    public class PriceAssessmentReport {
//        @Schema(description = "户型")
//        private Layout layout;
//        
//        @Schema(description = "小区")
//        private Community community;
//    }
//    
//    @Data
//    @NoArgsConstructor
//    @Accessors(chain = true)
//    @Schema(name = "户型")
//    public class Layout {
//        @Schema(description = "户型名")
//        private String name;
//
//        @Schema(description = "室")
//        private String roomCount;
//
//        @Schema(description = "厅")
//        private String hallCount;
//    }
//    
//    @Data
//    @NoArgsConstructor
//    @Accessors(chain = true)
//    @Schema(name = "小区")
//    public class Community {
//        @Schema(description = "贝壳小区id")
//        private String id;
//
//        @Schema(description = "小区名")
//        private String name;
//    }
    
    @Data
    @NoArgsConstructor
    @Accessors(chain = true)
    public class Compare {
    	@Schema(description = "标的物类型, 小区, 商圈, 板块, 区域, 城市")
    	private String subjectType;

        @Schema(description = "褒")
        private ReportSubject positive;

        @Schema(description = "贬")
        private ReportSubject negative;
    }
    
    @Data
    @NoArgsConstructor
    @Accessors(chain = true)
    public class SupportingFacilityReport {
    	@Schema(description = "城市")
    	private String subjectType;
    	
    	@Schema(description = "配套类型: 教育,商业 等等")
    	private List<String> types;
    	
    	@Schema(description = "标的物")
        private ReportSubject subject;
    }
    
    @Data
    @NoArgsConstructor
    @Accessors(chain = true)
    public class ReportSubject {
    	@Schema(description = "城市")
    	private String cityName;
    	
    	@Schema(description = "区域")
    	private String districtName;
        
    	@Schema(description = "板块")
    	private String areaName;
        
    	@Schema(description = "商圈")
    	private String businessCircleName;
        
    	@Schema(description = "小区")
    	private String communityName;
    }

}
