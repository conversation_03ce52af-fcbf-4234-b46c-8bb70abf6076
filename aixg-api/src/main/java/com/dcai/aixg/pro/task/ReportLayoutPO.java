package com.dcai.aixg.pro.task;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@NoArgsConstructor
public class ReportLayoutPO {
    @Schema(description = "户型名")
    private String name;

    @Schema(description = "室")
    private String roomCount;

    @Schema(description = "厅")
    private String hallCount;
}
