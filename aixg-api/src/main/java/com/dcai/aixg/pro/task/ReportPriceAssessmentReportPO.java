package com.dcai.aixg.pro.task;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@NoArgsConstructor
public class ReportPriceAssessmentReportPO {
    @Schema(description = "户型")
    private ReportLayoutPO layout;
    
    @Schema(description = "小区")
    private ReportCommunityPO community;


}
