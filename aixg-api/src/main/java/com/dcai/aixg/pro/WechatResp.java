package com.dcai.aixg.pro;

import com.fasterxml.jackson.databind.JsonNode;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

@AllArgsConstructor
@NoArgsConstructor
@Data
public class WechatResp {
    private Integer errcode;
    private String errmsg;

    public boolean isSuccess() {
        return errcode == null || errcode == 0;
    }

    // 创建错误响应的静态方法，需要传入具体的子类Class
    public static <T extends WechatResp> T error(Class<T> clazz, String msg) {
        try {
            T instance = clazz.getDeclaredConstructor().newInstance();
            instance.setErrcode(-1);
            instance.setErrmsg(msg);
            return instance;
        } catch (Exception e) {
            throw new RuntimeException("创建错误响应失败", e);
        }
    }

    @EqualsAndHashCode(callSuper = true)
    @Data
    @NoArgsConstructor
    @Accessors(chain = true)
    public static class WechatOpenId extends WechatResp {
        private String openId;
        private String sessionKey;
        private String unionId;
    }

    @Data
    @NoArgsConstructor
    @Accessors(chain = true)
    public static class PhoneNumber {
        /**
         * 用户绑定的手机号（国外手机号会有区号）
         */
        private String phoneNumber;
        /**
         * 没有区号的手机号
         */
        private String purePhoneNumber;
        /**
         * 区号
         */
        private String countryCode;
        /**
         * 数据水印,timestamp,appid
         */
        private JsonNode watermark;
    }
}
