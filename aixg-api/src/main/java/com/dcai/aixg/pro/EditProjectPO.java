package com.dcai.aixg.pro;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@NoArgsConstructor
@Schema(name = "经纪人")
public class EditProjectPO {

    @NotNull(message = "经纪人ID不能为空")
    @Schema(description = "SaaS user ID")
    protected Long id;

    @NotNull(message = "手机号不能为空")
    @Size(min = 11, max = 11, message = "手机号长度必须为11位")
    @Schema(description = "手机号")
    protected String phone;

}
