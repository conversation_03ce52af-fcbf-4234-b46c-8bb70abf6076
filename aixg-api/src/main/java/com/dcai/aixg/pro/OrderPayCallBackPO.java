package com.dcai.aixg.pro;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
@Accessors(chain = true)
@NoArgsConstructor
public class OrderPayCallBackPO {
    @Schema(description = "克而瑞订单编号")
    private String orderNo;
    @Schema(description = "支付金额")
    private BigDecimal payAmount;
    @Schema(description = "支付状态 0-未支付，1-支付成功，2-支付中 3-支付失败")
    private Integer payStatus;
    @Schema(description = "支付时间")
    private LocalDateTime payTime;
    @Schema(description = "微信支付订单号")
    private String externalOrderNo;
}
