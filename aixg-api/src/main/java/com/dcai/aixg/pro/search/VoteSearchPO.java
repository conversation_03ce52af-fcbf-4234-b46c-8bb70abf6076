package com.dcai.aixg.pro.search;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@NoArgsConstructor
public class VoteSearchPO {

    @NotNull(message = "搜索ID不能为空")
    @Schema(name = "chatId", description = "搜索ID")
    private String chatId;

    @NotNull(message = "评价状态不能为空")
    @Schema(name = "voteStatus", description = "评价状态，1：好评，-1：差评")
    private int voteStatus;

}
