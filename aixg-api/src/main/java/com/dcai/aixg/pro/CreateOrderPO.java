package com.dcai.aixg.pro;

import com.dcai.aixg.dto.OrderDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@NoArgsConstructor
public class CreateOrderPO {

    @NotNull(message = "请选择要支付的商品")
    @Schema(description = "商品id")
    private Long goodsId;

    @NotNull(message = "商品类型不能为空")
    @Schema(description = "商品类型")
    private OrderDTO.Type goodsType;

    @NotNull(message = "微信openId不能为空")
    @Schema(description = "微信openId")
    private String openId;
}
