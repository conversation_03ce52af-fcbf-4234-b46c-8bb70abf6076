package com.dcai.aixg.pro.task;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@NoArgsConstructor
public class WriteDataCreatePO {
	
    @Schema(name = "cityName", description = "城市名")
    private String cityName;
	
    @Schema(name = "projectName", description = "小区名")
    private String projectName;
	
    @Schema(name = "projectId", description = "小区ID")
    private String projectId;
	
    @Schema(name = "regionName", description = "区域名")
    private String regionName;
	
    @Schema(name = "areaName", description = "版块名")
    private String areaName;
	
    @Schema(name = "layout", description = "户型")
    private String layout;

}
