package com.dcai.aixg.pro;

import jakarta.validation.Constraint;
import jakarta.validation.Payload;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

@Constraint(validatedBy = LoginInfoPOValidator.class)
@Target({ ElementType.TYPE })
@Retention(RetentionPolicy.RUNTIME)
public @interface LoginInfoPOValid {
    String message() default "Invalid login parameters based on loginType.";
    Class<?>[] groups() default {};
    Class<? extends Payload>[] payload() default {};
}
