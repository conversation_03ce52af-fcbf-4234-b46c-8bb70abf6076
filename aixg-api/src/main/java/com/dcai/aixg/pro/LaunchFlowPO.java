package com.dcai.aixg.pro;

import com.dcai.aixg.dto.FlowDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;

import java.util.Map;

/**
 * 创建工作流请求参数
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@Schema(description = "创建工作流请求参数")
public class LaunchFlowPO {

    /**
     * 运行模式枚举
     */
    public enum RunMode {
        SYNC,   // 同步执行
        ASYNC   // 异步执行
    }

    @NotBlank(message = "发起流程来源类型不能为空")
    @Schema(description = "发起流程来源类型")
    private FlowDTO.SrcType srcType;

    @NotBlank(message = "发起流程来源ID不能为空")
    @Schema(description = "发起流程来源ID")
    private Long srcId;

    @NotBlank(message = "工作流配置代码不能为空")
    @Schema(description = "工作流配置代码", example = "HOUSING_COMPARISON_FLOW")
    private String configCode;

    @NotNull(message = "请求参数不能为空")
    @Schema(description = "请求参数", example = "{\"delegationId1\": 53463, \"delegationId2\": 102152}")
    private Map<String, Object> request;

    @Schema(description = "运行模式", example = "SYNC")
    private RunMode runMode = RunMode.SYNC;

}
