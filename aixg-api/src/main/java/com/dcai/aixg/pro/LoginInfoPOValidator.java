package com.dcai.aixg.pro;

import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;
import org.apache.commons.lang3.StringUtils;

public class LoginInfoPOValidator implements ConstraintValidator<LoginInfoPOValid, LoginInfoPO> {

    @Override
    public void initialize(LoginInfoPOValid constraintAnnotation) {

    }

    @Override
    public boolean isValid(LoginInfoPO value, ConstraintValidatorContext constraintValidatorContext) {
        if (value == null) {
            return false; // 如果对象为null，不合法的
        }

        // 根据 loginType 校验字段是否必填
        Integer loginType = value.getLoginType();

        if (loginType == null) {
            return false; // loginType 为null是不合法的
        }
        if (StringUtils.isBlank(value.getPhone())) {
            return false; //手机号为null 不合法
        }
        switch (loginType) {
            case 1: // 微信登录
                // openId 必填，phone、code 不必填
                return value.getOpenId() != null && !value.getOpenId().isEmpty();
            case 2: // 验证码登录
                // phone 和 code 必填，openId 不必填
                return value.getPhone() != null && !value.getPhone().isEmpty()
                        && value.getCode() != null && !value.getCode().isEmpty();
            default:
                return false; // 不合法的 loginType
        }
    }
}
