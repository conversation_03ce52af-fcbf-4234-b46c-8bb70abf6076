package com.dcai.aixg.pro.search;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@NoArgsConstructor
public class ChatCreatePO {
	
    @Schema(name = "chatId", description = "搜索ID，再答一次时提供")
    private String chatId;
	
	@NotNull(message = "请输入搜索内容")
    @Schema(name = "query", description = "搜索内容")
    private String query;

}
