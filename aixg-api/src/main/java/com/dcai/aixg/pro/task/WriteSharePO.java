package com.dcai.aixg.pro.task;

import java.util.List;

import com.dcai.aixg.dto.task.TaskDTO;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@NoArgsConstructor
public class WriteSharePO {

    @Schema(name = "taskId", description = "任务ID")
    private Long taskId;

    @Schema(name = "subType", description = "分享类型")
    private TaskDTO.SubType subType;

    @Schema(name = "chapters", description = "需要生成图片的章节列表")
    private List<String> chapters;
}
