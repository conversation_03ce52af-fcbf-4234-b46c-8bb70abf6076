package com.dcai.aixg.pro.task;

import com.dcai.aixg.dto.task.TaskDTO;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@NoArgsConstructor
public class CreateTaskCallBackPO {

    @NotNull(message = "任务ID不能为空")
    @Schema(description = "任务ID: 报告取值reportId, 文章取值:articleId")
    private String taskId;
    
    @NotNull(message = "任务类型不能为空")
    @Schema(description = "任务类型: REPORT-报告, WRITE-文章")
    private TaskDTO.Type taskType;
    
    @NotNull(message = "任务状态不能为空")
    @Schema(description = "任务状态: ING-执行中, DONE-执行成功, FAIL-执行失败, UNKNOW-未知")
    private TaskDTO.Status taskStatus;
    
    @Schema(description = "备注信息")
    private String msg;

}
