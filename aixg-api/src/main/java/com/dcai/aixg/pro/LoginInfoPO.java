package com.dcai.aixg.pro;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

@Data
@LoginInfoPOValid
@Accessors(chain = true)
@NoArgsConstructor
public class LoginInfoPO {

    @Schema(description = "登录类型 1 微信登录 2验证码 必填")
    private Integer loginType;
    @Schema(description = "手机号 必填")
    private String phone;
    @Schema(description = "微信openId 微信登录必填")
    private String openId;
    @Schema(description = "验证码 验证码登录必填")
    private String code;
}
