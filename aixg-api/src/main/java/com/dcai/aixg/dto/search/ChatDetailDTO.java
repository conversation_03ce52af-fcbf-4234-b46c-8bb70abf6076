package com.dcai.aixg.dto.search;

import java.util.List;

import com.alibaba.fastjson.JSONObject;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@NoArgsConstructor
public class ChatDetailDTO {

    @Schema(description = "lastMsgId")
	private String lastMsgId;

    @Schema(description = "hasMore")
	private String hasMore;

    @Schema(description = "list")
	private List<JSONObject> list;

}
