package com.dcai.aixg.dto;

import com.ejuetc.commons.base.querydomain.api.QueryDomain;
import com.ejuetc.commons.base.querydomain.api.QueryField;
import lombok.*;

import java.time.LocalDateTime;

/**
 * 生成步骤DTO
 *
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@QueryDomain("com.dcai.aixg.domain.node.GenerateStep")
public class GenerateStepDTO {

    @Getter
    public enum Type {
        QUERY_DELEGAION("查询房源"),
        QUERY_COMMUNITY("查询小区"),
        ;

        private final String description;

        Type(String description) {
            this.description = description;
        }

    }

    /**
     * 步骤ID
     */
    @QueryField
    private Long id;

    /**
     * 生成任务ID
     */
    @QueryField("node.id")
    private Long generateId;

    /**
     * 步骤标题
     */
    @QueryField
    private String title;

    /**
     * 请求内容
     */
    @QueryField
    private String request;

    /**
     * 响应内容
     */
    @QueryField
    private String response;

    /**
     * 执行时长(毫秒)
     */
    @QueryField
    private Long duration;

    /**
     * 状态描述
     */
    private String statusDescription;

    /**
     * 备忘录
     */
    private String memo;

    /**
     * 错误信息
     */
    private String errorMessage;

    /**
     * 开始时间
     */
    private LocalDateTime startedAt;

    /**
     * 完成时间
     */
    private LocalDateTime completedAt;

    /**
     * 步骤顺序
     */
    private Integer stepOrder;

    /**
     * 步骤类型
     */
    private String stepType;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

}
