package com.dcai.aixg.dto.task;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@NoArgsConstructor
public class WriteCreateDTO {

    @Schema(name = "qrCodeUrl", description = "二维码图片地址")
	private String qrCodeUrl;

    @Schema(name = "taskId")
	private Long taskId;

    @Schema(name = "writeId")
	private String writeId;

    @Schema(name = "articleId")
    private String articleId;

    @Schema(name = "ask")
    private String ask;
    
    @Schema(name = "topic")
    private String topic;
    
    @Schema(name = "title")
    private String title;
    
    @Schema(name = "cricDataId")
    private String cricDataId;

    @Schema(name = "wechatAccount", description = "公众号名称")
	private String wechatAccount = "AI地产销冠";

    @Schema(name = "followStatus", description = "是否关注公众号: true: 已关注 false: 未关注")
	private boolean followStatus = false;

}
