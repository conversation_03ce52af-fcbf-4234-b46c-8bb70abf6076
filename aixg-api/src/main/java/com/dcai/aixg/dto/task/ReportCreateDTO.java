package com.dcai.aixg.dto.task;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@NoArgsConstructor
public class ReportCreateDTO {

    @Schema(description = "二维码图片地址")
	private String qrCodeUrl;

    @Schema(description = "文章Id")
	private String reportId;

    @Schema(name = "wechatAccount", description = "公众号名称")
	private String wechatAccount = "AI地产销冠";

    @Schema(name = "followStatus", description = "是否关注公众号: true: 已关注 false: 未关注")
	private boolean followStatus = false;

}
