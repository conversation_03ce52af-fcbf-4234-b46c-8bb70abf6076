package com.dcai.aixg.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

@Data
@NoArgsConstructor
@Accessors(chain = true)
public class LoginInfoDTO {

    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long id;

    private String token;

    private String phone;

    private String cityId;

    private String cityName;

    private String name;

    private String companyName;

    private String icon;

    private String openId;
}
