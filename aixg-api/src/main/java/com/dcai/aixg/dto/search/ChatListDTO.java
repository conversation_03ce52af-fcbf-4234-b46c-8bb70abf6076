package com.dcai.aixg.dto.search;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@NoArgsConstructor
public class ChatListDTO {

    @Schema(description = "搜索ID")
	private String chatId;
    
    @Schema(description = "搜索标题")
	private String title;
    
    @Schema(description = "搜索信息")
	private String message;
    
    @Schema(description = "搜索时间")
	private String created_at;
    
    @Schema(description = "创建时间（前端展示用）")
	private String created_at_show;

}
