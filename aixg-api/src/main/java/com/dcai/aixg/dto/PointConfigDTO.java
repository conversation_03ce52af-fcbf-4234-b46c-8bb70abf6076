package com.dcai.aixg.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.util.List;

@Data
@Accessors(chain = true)
@NoArgsConstructor
@Schema(name = "积分消耗配置")
public class PointConfigDTO {


    @Schema(description = "名称")
    private String name;

    @Schema(description = "名称")
    private List<PointConfigDetail> describe;

    @Data
    public static class PointConfigDetail {

        @Schema(description = "code")
        private String code;

        @Schema(description = "标题")
        private String title;
        @Schema(description = "消耗积分")
        private String use;
        @Schema(description = "备注")
        private String description;
        @Schema(description = "概要")
        private String summary;
    }
}
