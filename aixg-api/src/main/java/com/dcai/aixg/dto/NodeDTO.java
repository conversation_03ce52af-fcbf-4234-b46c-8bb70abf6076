package com.dcai.aixg.dto;

import java.time.LocalDateTime;
import java.util.List;

import com.ejuetc.commons.base.querydomain.api.QueryDomain;
import com.ejuetc.commons.base.querydomain.api.QueryField;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.Getter;
import lombok.NoArgsConstructor;

/**
 * 生成任务DTO
 *
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
//@QueryDomain("com.dcai.aixg.domain.node.Generate")
@QueryDomain("com.dcai.aixg.domain.aiagent.node.Node")
public class NodeDTO {

    /**
     * 任务类型枚举
     */
    @Getter
    public enum Type {
        LLM_GEN("大模型生成"),
        POLICY_COMMENT("政策解读"),
        SALE_COMPARE("卖点对比"),//价值评测
        LIFE_EXPERT("生活专家"),
        HOUSING_EXPERT("房源专家"),
        MONTHLY_REPORT("月度报告"),
        INDUSTRY_ANALYST("行业分析"),
        VALUE_EVALUATION("价值评估");

        private final String description;

        Type(String description) {
            this.description = description;
        }

    }

    /**
     * 生成状态枚举
     */
    @Getter
    public enum Status {
        WAIT("待生成"),
        GENERATING("生成中"),
        SUCCESS("成功"),
        FAILED("失败"),
        CANCELLED("已取消");

        private final String description;

        Status(String description) {
            this.description = description;
        }

    }

    /**
     * 任务ID
     */
    @QueryField
    private Long id;

    /**
     * 配置信息
     */
    @QueryField
    private ConfigDTO config;

    /**
     * 请求内容
     */
    @QueryField
    private String request;

    /**
     * 系统提示
     */
    @QueryField
    private String systemPrompt;

    /**
     * 用户提示
     */
    @QueryField
    private String userPrompt;

    /**
     * 响应内容
     */
    @QueryField
    private String response;

    /**
     * 执行时长(毫秒)
     */
    @QueryField
    private Long duration;

    /**
     * 状态
     */
    @QueryField
    private Status status;

    /**
     * 状态描述
     */
    private String statusDescription;

    /**
     * 备忘录
     */
    @QueryField
    private String memo;

    /**
     * 错误信息
     */
    @QueryField
    private String errorMessage;

    /**
     * 开始时间
     */
    @QueryField
    private LocalDateTime startedAt;

    /**
     * 完成时间
     */
    @QueryField
    private LocalDateTime completedAt;

    /**
     * 创建时间
     */
    @QueryField
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @QueryField
    private LocalDateTime updateTime;

    /**
     * 生成步骤列表
     */
    private List<GenerateStepDTO> steps;

    /**
     * 进度信息
     */
    private ProgressInfo progressInfo;
    
    /**
     * 处理过程信息1
     */
    @QueryField
    private String processInfo1;

    /**
     * 处理过程信息2
     */
    @QueryField
    private String processInfo2;

    /**
     * 处理过程信息3
     */
    @QueryField
    private String processInfo3;

    /**
     * 进度信息
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ProgressInfo {
        /**
         * 总步骤数
         */
        private Integer totalSteps;

        /**
         * 已完成步骤数
         */
        private Integer completedSteps;

        /**
         * 当前步骤
         */
        private String currentStep;

        /**
         * 进度百分比
         */
        private Double progressPercentage;

        /**
         * 预计剩余时间(毫秒)
         */
        private Long estimatedRemainingTime;
    }

}
