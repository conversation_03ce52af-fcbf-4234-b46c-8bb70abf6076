package com.dcai.aixg.dto;

import com.ejuetc.commons.base.querydomain.api.QueryDomain;
import lombok.*;
import lombok.experimental.Accessors;

/**
 * 工作流DTO
 *
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@Accessors(chain = true)
@QueryDomain("com.dcai.aixg.domain.aiagent.config.Config")
public class ConfigDTO {

    /**
     * 工作流类型枚举
     */
    @Getter
    public enum Type {
        NODE("生成器"),
        SUBFLOW("子流程"),
        ;

        private final String description;

        Type(String description) {
            this.description = description;
        }
    }

}
