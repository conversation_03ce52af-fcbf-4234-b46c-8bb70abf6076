package com.dcai.aixg.dto.task;

import com.ejuetc.commons.base.entity.BaseDTO;
import com.ejuetc.commons.base.querydomain.api.QueryDomain;
import com.ejuetc.commons.base.querydomain.api.QueryField;
import com.fasterxml.jackson.annotation.JsonTypeInfo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

@Getter
@Setter
@Accessors(chain = true)
@NoArgsConstructor
@QueryDomain("com.dcai.aixg.domain.task.Policy")
@ToString(callSuper = true)
@JsonTypeInfo(use = JsonTypeInfo.Id.CLASS)
@Schema(name = "政策")
public class PolicyDTO extends BaseDTO<PolicyDTO> {
	
	public PolicyDTO(Long id) {
        super(id);
    }

    @QueryField
    @Schema(description = "城市名称")
    private String cityName;

    @QueryField
    @Schema(description = "政策标题")
    private String title;

}
