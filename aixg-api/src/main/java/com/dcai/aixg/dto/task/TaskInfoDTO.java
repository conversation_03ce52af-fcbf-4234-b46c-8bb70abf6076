package com.dcai.aixg.dto.task;

import java.util.List;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@NoArgsConstructor
@AllArgsConstructor
public class TaskInfoDTO {

    @Schema(description = "推荐主题")
	private List<String> topics;

    @Schema(description = "输入提示")
	private String tip;

}
