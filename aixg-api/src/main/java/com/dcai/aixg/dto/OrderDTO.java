package com.dcai.aixg.dto;

import com.alibaba.fastjson.JSONObject;
import com.ejuetc.commons.base.entity.BaseDTO;
import com.ejuetc.commons.base.entity.TitleEnum;
import com.ejuetc.commons.base.querydomain.api.QueryDomain;
import com.ejuetc.commons.base.querydomain.api.QueryField;
import com.fasterxml.jackson.annotation.JsonTypeInfo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

@Getter
@Setter
@Accessors(chain = true)
@NoArgsConstructor
@QueryDomain("com.dcai.aixg.domain.order.Order")
@ToString(callSuper = true)
@JsonTypeInfo(use = JsonTypeInfo.Id.CLASS)
@Schema(name = "订单")
public class OrderDTO extends BaseDTO<OrderDTO> {

    public OrderDTO(Long id) {
        super(id);
    }

    @QueryField
    @Schema(description = "经纪人")
    private BrokerDTO broker;

    @QueryField
    @Schema(description = "微信openId")
    private String openId;

    @QueryField
    @Schema(description = "类型")
    private OrderDTO.Type orderType;

    @QueryField
    @Schema(description = "商品id")
    private Long goodsId;

    @QueryField
    @Schema(description = "商品信息")
    protected GoodsDTO goodsJson;

    @QueryField
    @Schema(description = "付款信息")
    private BigDecimal payAmount;

    @QueryField
    @Schema(description = "支付状态")
    private OrderDTO.Status status;

    @QueryField
    @Schema(description = "创建订单克而瑞应答")
    private JSONObject kerResp;

    @QueryField
    @Schema(description = "克而瑞订单编号")
    private String kerOrderNo;

    @Getter
    public enum Type implements TitleEnum {
        POINT("积分"),
        BUSINESS_CARD("名片");
        private final String title;

        Type(String title) {
            this.title = title;
        }

        @Override
        public String toString() {
            return name() + "-" + title;
        }
    }

    @Getter
    public enum Status implements TitleEnum {
        PAY_WAIT("待支付"),
        PAYING("支付中"),
        PAY_SUCC("支付成功"),
        PAY_FAIL("支付失败"),
        PAY_OVERDUE("逾期未支付"),
        UNKNOW("支付结果未知");
        private final String title;

        Status(String title) {
            this.title = title;
        }

        @Override
        public String toString() {
            return name() + "-" + title;
        }
    }
}
