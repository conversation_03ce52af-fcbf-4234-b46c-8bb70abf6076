package com.dcai.aixg.dto;

import com.ejuetc.commons.base.entity.BaseDTO;
import com.ejuetc.commons.base.querydomain.api.QueryDomain;
import com.ejuetc.commons.base.querydomain.api.QueryField;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonTypeInfo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

@Getter
@Setter
@Accessors(chain = true)
@NoArgsConstructor
@QueryDomain("com.dcai.aixg.domain.banner.Banner")
@ToString(callSuper = true)
@JsonTypeInfo(use = JsonTypeInfo.Id.CLASS)
@Schema(name = "banner")
public class BannerDTO  extends BaseDTO<BannerDTO> {
    public BannerDTO(Long id) {super(id);}

    @QueryField(value = "id")
    @Schema(description = "id")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long id;

    @QueryField
    @Schema(description = "城市id")
    private String cityId;

    @QueryField
    @Schema(description = "小程序id")
    private String appId;

    @QueryField
    @Schema(description = "跳转链接")
    private String path;

    @QueryField
    @Schema(description = "图片链接")
    private String imgUrl;

    @QueryField
    @Schema(description = "名称")
    private String name;
}
