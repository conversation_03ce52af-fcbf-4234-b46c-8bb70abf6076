package com.dcai.aixg.dto;

import com.ejuetc.commons.base.entity.BaseDTO;
import com.ejuetc.commons.base.entity.TitleEnum;
import com.ejuetc.commons.base.querydomain.api.QueryDomain;
import com.fasterxml.jackson.annotation.JsonTypeInfo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@NoArgsConstructor
@QueryDomain("com.dcai.aixg.domain.tweet.Tweet")
@ToString(callSuper = true)
@JsonTypeInfo(use = JsonTypeInfo.Id.CLASS)
@Schema(name = "推文任务")
public class TweetDTO extends BaseDTO<TweetDTO> {

    public TweetDTO(Long id) {
        super(id);
    }

    @Getter
    public enum Type implements TitleEnum {
        MONTHLY_REPORT("月度市场分析"),
        INDUSTRY_ANALYST("行业政策分析");
        private final String title;

        Type(String title) {
            this.title = title;
        }

        @Override
        public String toString() {
            return name() + "-" + title;
        }
    }

    @Getter
    public enum Status implements TitleEnum {
        WAIT("待执行"),
        ING("生成中"),
        DONE("已生成"),
        FAIL("生成失败"),
        UNKNOW("未知");
        private final String title;

        Status(String title) {
            this.title = title;
        }

        @Override
        public String toString() {
            return name() + "-" + title;
        }
    }
}
