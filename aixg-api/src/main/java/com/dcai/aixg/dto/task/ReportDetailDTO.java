package com.dcai.aixg.dto.task;

import java.util.List;

import com.dcai.aixg.dto.BrokerDTO;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@NoArgsConstructor
public class ReportDetailDTO {

    @Schema(description = "reportId")
	private String reportId;
    
    @Schema(description = "报告正文")
	private String content;
    
    @Schema(description = "broker")
	private BrokerDTO broker;
	
    @Schema(description = "房源")
	private List houseInfo;
    
    @Schema(description = "报告标题")
	private String title;
    
    @Schema(description = "文章时间")
	private String createdAt;
    
    @Schema(description = "创建时间（前端展示用）")
	private String createdAtShow;

}
