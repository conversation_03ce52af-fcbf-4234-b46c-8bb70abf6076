package com.dcai.aixg.dto;

import com.ejuetc.commons.base.querydomain.api.QueryDomain;
import lombok.Builder;
import lombok.Data;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * 工作流DTO
 *
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@Accessors(chain = true)
@QueryDomain("com.dcai.aixg.domain.aiagent.commons.ProcessBase")
public class ProcessDTO {

    /**
     * 工作流类型枚举
     */
    @Getter
    public enum Type {
        FLOW("流程"),
        GENERATE("生成器"),
        FUNCTION_CALL("函数调用"),
        ;

        private final String description;

        Type(String description) {
            this.description = description;
        }
    }

}
