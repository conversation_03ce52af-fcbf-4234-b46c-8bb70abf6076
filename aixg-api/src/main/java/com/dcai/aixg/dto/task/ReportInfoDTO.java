package com.dcai.aixg.dto.task;

import java.util.Arrays;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class ReportInfoDTO {
	
    @Schema(description = "固定标的优劣分析")
	private TaskInfoDTO compare;

    @Schema(description = "市场政策类分析")
	private TaskInfoDTO marketPolicyAnalysis;

    @Schema(description = "城市配套深度调研")
	private TaskInfoDTO supportingFacilityResearch;
    
    public ReportInfoDTO() {
    	this.compare = new TaskInfoDTO(Arrays.asList(), 
    			"我将根据你输入的赞贬对象进行报告创作，如有补充内容可填写，如果没有补充内容，也可以选择跳过。");
    	this.marketPolicyAnalysis = new TaskInfoDTO(Arrays.asList("土拍遇冷，房企财报更新，新房市场扑朔迷离，建议购房者侧重考量二手房，风险小"), 
    			"你可以输入近期房产市场热议政策、话题，以及你希望推演出的市场趋势分析、购房策略建议，我将为你创作深度报告。");
    	this.supportingFacilityResearch = new TaskInfoDTO(Arrays.asList(), 
    			"我将根据你录入对象进行深度报告创作，如有补充内容可填写，如果没有补充内容，也可以选择跳过。");
    }

}
