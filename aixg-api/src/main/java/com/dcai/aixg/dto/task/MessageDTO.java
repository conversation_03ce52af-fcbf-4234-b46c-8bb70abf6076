package com.dcai.aixg.dto.task;

import com.ejuetc.commons.base.entity.TitleEnum;
import com.ejuetc.commons.base.querydomain.api.QueryDomain;
import com.fasterxml.jackson.annotation.JsonTypeInfo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

@Getter
@Setter
@Accessors(chain = true)
@NoArgsConstructor
@QueryDomain("com.dcai.aixg.domain.task.Message")
@ToString(callSuper = true)
@JsonTypeInfo(use = JsonTypeInfo.Id.CLASS)
@Schema(name = "消息")
public class MessageDTO {

    @Getter
    public enum Status implements TitleEnum {
        SEND_WAIT("待发送"),
        SEND_ING("发送中"),
        SEND_SUCC("发送成功"),
        SEND_FAIL("发送失败"),
        UNKNOW("发送结果未知");
        private final String title;

        Status(String title) {
            this.title = title;
        }

        @Override
        public String toString() {
            return name() + "-" + title;
        }
    }
}
