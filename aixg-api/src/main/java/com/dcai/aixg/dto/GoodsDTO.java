package com.dcai.aixg.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

@Data
@Accessors(chain = true)
@NoArgsConstructor
public class GoodsDTO {

    @Schema(description = "商品id")
    private Long id;
    @Schema(description = "商品名称")
    private String name;
    @Schema(description = "说明")
    private String desc;
    @Schema(description = "原始价格")
    private BigDecimal originalPrice;
    @Schema(description = "优惠后价格")
    private BigDecimal price;
    @Schema(description = "有效期")
    private Integer expireDays;
    @Schema(description = "积分")
    private Integer point;
}
