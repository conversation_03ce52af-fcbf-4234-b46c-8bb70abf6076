package com.dcai.aixg.integration.wechat;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

@Data
@Configuration
@ConfigurationProperties(prefix = "wechat")
public class WechatConfig {
    private String appId;
    private String appSecret;

    public String getAccessTokenUrl() {
        return "https://api.weixin.qq.com/cgi-bin/token?grant_type=client_credential&appid=%s&secret=%s".formatted(appId, appSecret);
    }

    public String getQrCodeTicket(String accessToken) {
        return "https://api.weixin.qq.com/cgi-bin/qrcode/create?access_token=%s".formatted(accessToken);
    }

    public String getQrCodeUrl(String ticket) {
        return "https://mp.weixin.qq.com/cgi-bin/showqrcode?ticket=%s".formatted(ticket);
    }

    public String getMessageUrl(String accessToken) {
        return "https://api.weixin.qq.com/cgi-bin/message/template/send?access_token=%s".formatted(accessToken);
    }

    public String getSendMessageUrl(String accessToken) {
        return "https://api.weixin.qq.com/cgi-bin/message/custom/send?access_token=%s".formatted(accessToken);
    }
}
