package com.dcai.aixg.integration;

import com.dcai.aixg.pro.EditBrokerPO;
import com.dcai.aixg.pro.LoginInfoPO;
import com.dcai.aixg.pro.UpdateCityPo;
import com.dcai.aixg.pro.WechatAuthorizationPhonePO;
import com.ejuetc.commons.base.response.ApiResponse;
import io.swagger.v3.oas.annotations.Operation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

@FeignClient("property-repo-a")
public interface WxLoginApi {

    @Operation(summary = "获取微信登录OpenId")
    @GetMapping({"/v2/wechat-online/api-new/authorization/getAiXgOpenId"})
    ApiResponse<String> getAiXgOpenId(@RequestParam("code") String code);

    @Operation(summary = "获取手机号码及用户信息")
    @PostMapping({"/v2/wechat-online/api-new/authorization/getAiXgPhone"})
    ApiResponse<String> getAiXgPhone(@RequestBody WechatAuthorizationPhonePO param);

    @Operation(summary = "登录")
    @PostMapping({"/v2/personal/aiXgLogin"})
    ApiResponse<String> login(@RequestBody LoginInfoPO po);

    @Operation(summary = "查询用户数据")
    @GetMapping({"/v2/personal/aiXgQuery"})
    ApiResponse<String> aiXgQuery(@RequestParam("userId") Long userId);

    @Operation(summary = "更新城市")
    @PostMapping({"/v2/personal/aiXgUpdateCity"})
    ApiResponse<String> updateCity(@RequestHeader("Authorization") String authorizationHeader, @RequestBody UpdateCityPo po);

    @Operation(summary = "更新图像相关信息")
    @PostMapping({"/v2/personal/aiXgUpdateUser"})
    ApiResponse<String> aiXgUpdateUser(@RequestHeader("Authorization") String authorizationHeader, @RequestBody EditBrokerPO po);


}
