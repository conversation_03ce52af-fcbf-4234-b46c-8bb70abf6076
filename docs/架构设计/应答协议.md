# 统一文档应答结构

## 1 文档结构描述

- **文档结构**：文档由自上而下排序的多个"控件"构成(各级标题和文字段落都是控件);(控件可考虑流式输出,前端可逐个绘制,呈现生成过程)
- **控件定义**：控件拥有统一的结构化字段和个性化的数据字段,兼顾结构统一呈现多样;
- **定位编号**：对完整态文档各要素进行分级编号(章节\段落\栏目\控件),输出时可分段缺失,但段落编号不变;

### 报文样例

```json
{
  "id": 1234567890,
  "type": "文档类型(参考文档类型枚举)",
  "title": "文档标题",
  "subtitle": "文档副标题",
  "widgets": [
    {
      "serial": "1",
      "type": "控件类型",
      "style": "样式",
      "title": "标题",
      "xxx": {
      }
    }
  ]
}
```

### 字段说明

| 字段           | 类型     | 必填 | 说明                                                                                                                                                                                                                      |
|--------------|--------|----|-------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|
| 回调函数id       | number | 否  | 文档ID,用于标识文档,可选                                                                                                                                                                                                          |
| 回调函数type     | string | 是  | 文档类型<br/>`POLICY_INTERPRETATION_EXPERT` 政策解读专家<br/>`VALUE_ASSESSMENT_EXPERT` 价值评测专家<br/>`REAL_ESTATE_LIFESTYLE_EXPERT` 房产生活专家<br/>`HOUSING_EXPERT` 房源专家<br/>`MONTHLY_REPORT` 月度报告<br/>`INDUSTRY_POLICY_ANALYSIS` 行业政策分析 |
| 回调函数title    | string | 是  | 文档主标题                                                                                                                                                                                                                   |
| 回调函数subtitle | string | 否  | 文档副标题                                                                                                                                                                                                                   |
| 回调函数widgets  | array  | 是  | 控件数组，包含文档中所有控件，按照自上而下的顺序排列                                                                                                                                                                                              |

---

## 2. 控件定义

### 控件类型枚举

| 控件类型         | 说明   | 样式                                                                                         |
|--------------|------|--------------------------------------------------------------------------------------------|
| TEXT         | 文本   | `SECTION`(章节标题),`PARAGRAPH`(段落标题),`ENTRY`(条目标题),`PLAIN`(无修饰),`BLOCK`(文本框),`EMPHASIS`(重点突出) |
| IMAGE        | 图片   | `URL`(图片URL),`SVG`(SVG图片代码)                                                                |
| LIST         | 列表   | `SERIAL`(序号),`ITEM`(无序项)                                                                   |
| TABLE        | 表格   | <暂无>                                                                                       |
| CHART        | 图表   | `BAR`(柱状图), `LINE`(折线图), `PIE`(饼图)                                                         |
| HOUSING_CARD | 房产卡片 | <暂无>                                                                                       |

### 2.1 文本(TEXT)

#### 报文样例

```json
{
  "serial": "1",
  "type": "TEXT",
  "style": "文本样式",
  "title": "标题",
  "content": "文本内容"
}
```

#### 字段说明

| 字段      | 类型     | 必填 | 说明                                                                                                                |
|---------|--------|----|-------------------------------------------------------------------------------------------------------------------|
| serial  | string | 是  | 控件编号，用于定位控件在文档中的位置                                                                                                |
| type    | string | 否  | 固定值`TEXT`                                                                                                         |
| style   | string | 否  | 样式:<br/>`SECTION` 章节标题<br/>`PARAGRAPH` 段落标题<br/>`ENTRY` 条目标题<br/>`PLAIN` 无修饰<br/>`BLOCK` 文本框<br/>`EMPHASIS` 重点突出) |
| title   | string | 否  | 标题                                                                                                                |
| content | string | 否  | 文本内容或副标题                                                                                                          |

### 2.2 图片(IMAGE)

#### 报文样例

```json
{
  "serial": "1",
  "type": "IMAGE",
  "style": "样式",
  "title": "标题",
  "content": "图片路径"
}
```

#### 字段说明

| 字段      | 类型     | 必填 | 说明                             |
|---------|--------|----|--------------------------------|
| serial  | string | 是  | 控件编号，用于定位控件在文档中的位置             |
| type    | string | 否  | 固定值`IMAGE`                     |
| style   | string | 否  | 样式: `URL` 图片URL;`SVG` SVG图片代码; |
| title   | string | 否  | 标题                             |
| content | string | 否  | 图片URL或SVG代码                    |

### 2.3 列表(LIST)

#### 报文样例

```json
{
  "serial": "2",
  "type": "LIST",
  "style": "SERIAL/ITEM",
  "title": "标题",
  "content": [
    {
      "title": "标题",
      "content": "内容"
    }
  ]
}
```

#### 字段说明

| 字段        | 类型     | 必填 | 说明                            |
|-----------|--------|----|-------------------------------|
| serial    | string | 是  | 控件编号，用于定位控件在文档中的位置            |
| type      | string | 否  | 固定值`LIST`                     |
| style     | string | 否  | 样式(`SERIAL` 有序列表,`ITEM` 无序列表) |
| title     | string | 否  | 标题                            |
| content   | array  | 否  | 列表项数组                         |
| ∟ title   | string | 否  | 列表项标题                         |
| ∟ content | string | 否  | 列表项内容                         |

### 2.4 表格(TABLE)

#### 报文样例

```json
{
  "serial": "3",
  "type": "TABLE",
  "title": "表格标题",
  "cols": [
    "列标题1",
    "列标题2"
  ],
  "content": [
    [
      {
        "type": "TEXT",
        "content": "单元格值",
        "recommended": true
      },
      {
        "type": "IMAGE",
        "content": "http://example.com/image.jpg"
      }
    ]
  ]
}
```

#### 字段说明

| 字段            | 类型        | 必填 | 说明                                                                               |
|---------------|-----------|----|----------------------------------------------------------------------------------|
| serial        | string    | 是  | 控件编号，用于定位控件在文档中的位置                                                               |
| type          | string    | 否  | 固定值`TABLE`                                                                       |
| title         | string    | 否  | 表格标题                                                                             |
| style         | string    | 否  | 样式风格(`NORMAL` 普通表格,`BOARD` 数据面板)                                                 |
| cols          | array     | 是  | 列标题数组（至少2列）                                                                      |
| content       | array[][] | 是  | 行数据数组（至少1行）                                                                      |
| ∟ type        | string    | 是  | 数据类型：<br>`TEXT`文本<br>`IMAGE`图片URL<br>`PROGRESS_BAR`进度值(0-100) ,<br/>`CHANGE` 涨跌幅 |
| ∟ title       | any       | 否  | 单元格值                                                                             |
| ∟ content     | any       | 是  | 单元格值                                                                             |
| ∟ recommended | boolean   | 否  | 是否推荐该选项（仅比较列有效）                                                                  |

### 2.5 图表控件 (CHART)

#### 报文样例

```json
{
  "serial": "4",
  "type": "CHART",
  "style": "BAR/LINE/PIE",
  "title": "图表标题",
  "cols": [
    "类别1",
    "类别2"
  ],
  "content": [
    {
      "title": "系列1",
      "content": [
        10,
        20
      ]
    }
  ]
}
```

#### 字段说明

| 字段        | 类型     | 必填 | 说明                                      |
|-----------|--------|----|-----------------------------------------|
| serial    | string | 是  | 控件编号                                    |
| type      | string | 是  | 固定值 `CHART`                             |
| style     | string | 是  | 图表类型：`BAR`(柱状图), `LINE`(折线图), `PIE`(饼图) |
| title     | string | 是  | 图表标题                                    |
| cols      | array  | 否  | 横轴类别标签（柱状图/折线图必填，饼图不需要）                 |
| content   | array  | 是  | 数据系列（格式根据图表类型变化）                        |
| ∟ title   | string | 是  | 系列名称                                    |
| ∟ content | array  | 是  | 数据值（柱状图/折线图为数值数组，饼图为数值）                 |

### 不同类型的数据格式

**柱状图/折线图 (BAR/LINE)**

#### 报文样例

```json
{
  "serial": "4",
  "type": "CHART",
  "style": "BAR",
  "title": "市场数据趋势",
  "cols": [
    "2024-07",
    "2024-08"
  ],
  "content": [
    {
      "title": "新增挂牌",
      "content": [
        11032,
        11567
      ]
    },
    {
      "title": "成交量",
      "content": [
        2015,
        1987
      ]
    }
  ]
}
```

**饼图 (PIE)**

#### 报文样例

```json
{
  "serial": "5",
  "type": "CHART",
  "style": "PIE",
  "title": "区域成交分布",
  "content": [
    {
      "title": "浦东新区",
      "content": 572
    },
    {
      "title": "闵行区",
      "content": 305
    }
  ]
}
```

### 2.6 房产卡片 (HOUSING_CARD)

#### 报文样例

```json
{
  "serial": "6",
  "title": "稀缺洋房",
  "type": "HOUSING_CARD",
  "name": "高兴花园",
  "layout": "1室1厅1卫",
  "area": "47m²",
  "floor": "6/6层",
  "location": "梅陇镇",
  "price": "235万",
  "unit_price": "50,000元/m²",
  "tags": [
    "唯一",
    "七日热门"
  ]
}
```

#### 字段说明

| 字段         | 类型     | 必填 | 说明                 |
|------------|--------|----|--------------------|
| serial     | string | 是  | 控件编号，用于定位控件在文档中的位置 |
| type       | string | 是  | 固定值`HOUSING_CARD`  |
| title      | string | 否  | 标题                 |
| name       | string | 是  | 房产名称               |
| layout     | string | 是  | 户型描述               |
| area       | string | 是  | 建筑面积               |
| floor      | string | 是  | 楼层信息               |
| location   | string | 是  | 地理位置               |
| price      | string | 是  | 总价                 |
| unit_price | string | 是  | 单价                 |
| tags       | array  | 否  | 特色标签数组             |

---