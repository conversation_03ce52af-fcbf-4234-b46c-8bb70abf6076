请将以下房源对比分析报告转换为DocumentData结构化对象：

**原始分析报告内容：**
```
${prevGenerateResponse}
```

**转换要求：**
请将上述分析报告内容转换为DocumentData结构化对象，保持原有的分析结论和数据。

**结构要求：**

1. **DocumentData主体结构：**
   - type: DocumentType.HOUSING_EXPERT (房源专家)
   - title: 从原报告中提取或生成吸引人的对比标题
   - widgets: 控件列表，按以下层次组织

2. **控件组织层次：**
   - **1**: TextWidget(type="TEXT", style=SECTION, title="房源基础信息")
   - **1.1**: TableWidget(type="TABLE", title="房源核心参数对比", 3列表格，通过recommended属性标识优势)
   - **1.2**: ListWidget(type="LIST", style=ITEM, title="对比结论", 包含价格优势、户型优势、位置优势、居住体验等结论)
   - **2**: TextWidget(type="TEXT", style=SECTION, title="投资价值分析")
   - **2.1**: TableWidget(type="TABLE", title="投资价值对比", 3列表格，通过recommended属性标识优势)
   - **3**: TextWidget(type="TEXT", style=SECTION, title="购房建议")
   - **3.1**: ListWidget(type="LIST", style=ITEM, title="针对性推荐", 包含不同类型购房者的建议)
   - **3.2**: TextWidget(type="TEXT", style=EMPHASIS, title="最终推荐", 包含最终推荐结论)

**表格结构要求：**
- **3列表格**：["对比维度", "房源A名称", "房源B名称"]
- **不要添加"优势方"列**：通过recommended属性标识优势
- **每行3个TableCell**：对应3列数据

**TableCell 正确格式示例：**
```
// 第1列：对比维度（不设置recommended）
{
  "type": "TEXT",
  "content": "总价"
}

// 第2列：房源A数据（根据分析设置recommended）
{
  "type": "TEXT",
  "content": "94万",
  "recommended": true    // 如果房源A在此维度有优势
}

// 第3列：房源B数据（根据分析设置recommended）
{
  "type": "TEXT",
  "content": "323.9万",
  "recommended": false   // 如果房源B在此维度无优势
}
```

**重要注意事项：**
- **禁止添加"优势方"列**：表格只能有3列
- **必须使用recommended属性**：在房源A/B的单元格中设置true/false
- TableCell的type字段使用枚举值：TEXT, IMAGE, PROGRESS_BAR, CHANGE
- content字段包含实际的显示内容
- 第1列（对比维度）不设置recommended字段

3. **关键要求：**
   - **必须设置控件的type字段**，Jackson需要此字段识别控件类型
     - TextWidget: "type": "TEXT"
     - TableWidget: "type": "TABLE"
     - ListWidget: "type": "LIST"
     - HousingCardWidget: "type": "HOUSING_CARD"
   - 从原报告中准确提取房源名称、价格、户型等关键信息
   - TableCell中的recommended字段必须基于原报告的分析结论设置
   - 保持原报告中的具体数字、价格、面积等关键数据
   - 不要添加原报告中没有的信息或分析
   - 忠实转换原报告内容，不要修改分析结论