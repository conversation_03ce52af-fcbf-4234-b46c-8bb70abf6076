请基于以下周边配套数据和小区信息数据，生成专业的区域价值分析markdown文档：

**小区基本信息：**

```json
${community_info}
```

**周边配套数据：**

```json
${around_data}
```

**生成要求：**

### 1. 内容要求
- **专业价值表达**：参考样例文件的表达风格，将基础配套信息转化为专业的价值表述
- **核心价值突出**：在开篇明确区域的核心价值定位和独特优势
- **配套价值化**：将距离、设施等基础信息转化为生活价值和投资价值表述
- **层次化组织**：按照区域价值→交通→配套→教育→品质的逻辑层次组织内容

### 2. 表达风格要求
- **价值导向**：突出区域的投资价值和生活价值，而非简单罗列配套
- **专业标签**：为重要配套和区域特色创建专业标签和价值标识
- **量化表述**：将距离、时间等数据转化为便利性和价值表述
- **场景化描述**：将配套优势转化为具体的生活场景和体验描述

### 3. 格式要求
- 严格按照markdown格式规范
- 使用专业的房地产行业表达方式
- 重要信息使用加粗强调
- 保持内容的逻辑性和可读性

### 4. 数据处理要求
- **严格基于输入数据**：所有内容必须基于提供的JSON数据，严禁虚构
- **充分利用数据**：必须充分利用输入数据中的所有有效配套信息
- **价值化转换**：将基础数据转换为价值表述，但不改变事实内容
- **专业化表达**：使用专业术语和表达方式提升内容品质

### 5. 质量标准要求
- **内容深度**：每个章节都要有充实的内容，不能只是简单罗列
- **价值挖掘**：深度挖掘每个配套设施的价值意义和生活影响
- **专业水准**：达到专业房地产分析报告的质量标准
- **客户导向**：站在购房者角度分析区域价值和投资潜力

## 输出示例结构参考

基于样例文件的结构，生成的文档应包含以下要素：

```markdown
## 区域价值
作为[城市][区域]的[定位特征]，[小区名称]坐拥[核心优势]，[绿化率/特色]营造出[居住环境特色]。项目由[开发商]开发，[物业公司]管理（物业费X元/月/㎡），完美融合[社区特色]与[居住体验]。

区域核心价值体现于：
- [价值点1]：[具体描述]
- [价值点2]：[具体描述]
- [价值点3]：[具体描述]
- [价值点4]：[具体描述]

## 交通网络
- 轨交动脉：[具体地铁信息和便利性]
- 路网中枢：[道路连接和交通便利性]
- 公交覆盖：[公交线路和网络密度]

## 生活配套
- 医疗旗舰：[重点医疗机构分类展示]
- 商业矩阵：[主要商业设施特色描述]
- 休闲图鉴：[娱乐休闲设施]

## 教育资源
- 全龄教育链：[各阶段教育机构]
- 特色优势：[教育资源独特价值]

## 小区品质
- 生态美学：[绿化景观特色]
- 建筑基因：[建筑特征和品质]
- 服务标准：[物业服务水平]
```

## 重要提醒

1. **样例文件参考**：请深度参考`区域价值_家辉.md`文件的表达风格和结构组织方式
2. **价值导向**：重点突出区域的投资价值、生活价值和稀缺性
3. **专业表达**：使用房地产行业的专业术语和表达方式
4. **数据忠实**：严格基于输入数据，绝不虚构任何信息
5. **内容丰富**：确保每个章节都有充实的内容和深度分析

## 样例文件风格参考

请参考以下表达风格：

### 价值表述风格
- "作为上海闵行区金虹桥板块的顶级住宅标杆"
- "坐拥虹桥高尔夫球场南向景观"
- "62% 超高绿化率营造出都市绿洲般的居住环境"
- "完美融合国际化社区氛围与私密尊贵体验"

### 配套描述风格
- "全球枢纽地位：距虹桥机场 6.9 公里，无缝连接国际国内"
- "精英圈层聚合：企业高管与外籍人士占比超 70%"
- "资产硬通货属性：196,262 元 /㎡均价彰显顶级保值力"

### 设施标签风格
- "高岛屋百货（日系顶奢）"
- "尚嘉中心（LVMH 旗舰综合体）"
- "和睦家国际医院（高端私立）"
- "上海第六人民医院（三甲公立）"

请严格按照以上要求和风格，生成高质量的区域价值分析文档。
