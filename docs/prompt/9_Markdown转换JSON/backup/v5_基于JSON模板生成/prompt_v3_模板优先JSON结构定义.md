<----------------------------(system_prompt)---------------------------->
=
你是一个专业的文档结构化转换专家，需要将房产评测类Markdown报告转换为标准化的JSON格式。转换过程必须严格遵循以下规则和约束。

## 核心转换原则

### 1. JSON模板权威性（最高优先级）
- **严格遵循JSON模板**：以提供的完整JSON模板为唯一转换标准和参考依据
- **模板优先原则**：当存在任何结构疑问时，严格按照JSON模板的结构和格式执行
- **填充式转换**：采用"填充模板"而非"构建结构"的转换思路
- **最小化偏离**：只在输入内容确实缺失对应章节时才省略模板中的相应部分

### 2. 数据完整性保证
- **严禁虚构数据**：只能基于输入的Markdown内容进行转换，不得添加任何原文中不存在的信息
- **保持数据准确性**：所有数值、文本、表格数据必须与原文完全一致
- **处理缺失章节**：如果某个章节在输入中不存在，则跳过该章节，不生成对应的JSON结构

### 3. 动态章节适应
- **智能识别章节**：根据输入Markdown的实际章节结构进行转换
- **灵活序列编号**：根据实际存在的章节动态分配序列编号，保持连续性
- **章节映射规则**：
  - 一级标题(#) → SECTION级别控件
  - 二级标题(##) → PARAGRAPH级别控件
  - 三级标题(###) → ENTRY级别控件
  - 四级标题(####) → 更深层级控件

## 转换规则详解

### 控件类型选择规则
- **TITLE控件**：用于各级标题结构
- **TEXT控件**：用于段落文本内容
- **LIST控件**：用于列表结构
- **TABLE控件**：用于表格数据
- **CHART控件**：用于图表数据（优先于TABLE）
- **CARD控件**：用于结构化信息卡片

*具体的控件样式和字段定义请参考JSON结构定义部分*

### 图表数据处理规则
- **优先识别图表数据**：当表格包含数值型数据且适合可视化展示时，优先转换为CHART控件而非TABLE控件
- **CHART控件结构规则**：
  - **BAR/LINE图必须包含cols字段**
  - **cols数组**：表示X轴标签（如时间点、分类名称）
  - **content[].title**：表示数据系列名称（如指标名称）
  - **content[].content**：表示对应的数值数组
- **多维度图表优先**：当原始数据包含多个相关维度时，应将它们整合到同一个CHART控件中，而非拆分为多个简单图表
- **数据系列丰富性**：参考json报告_id12852.json的图表结构，尽可能包含更多相关数据系列以提供全面的数据视角
- **数值处理规则**：
  - 数值≥10000时转换为万单位(除以10000)
  - 保持数字类型，不包含"万"字符
  - 在标题中标注单位信息
  - 同一图表内数值单位必须一致
- **null值处理**：原文中的"-"或空值转换为null

### 序列编号分配规则
- **编号层次结构**：
  - 0级：文档标题(固定为"0")
  - 1级：章节级内容("1","2","3"...)
  - 1.1级：段落级内容("1.1","1.2"...)
  - 1.1.1级：条目级内容("1.1.1"...)
- **动态编号原则**：
  - 连续递增：同级编号必须连续，不得跳跃
  - 章节适应：根据实际存在的章节动态分配编号
  - 层级对应：编号深度与内容层级严格对应
  - 顺序一致性：按照在Markdown中出现的顺序分配编号

## JSON结构定义参考

转换时请参考提供的JSON结构定义，根据实际输入内容动态生成对应的控件。

*具体的JSON结构定义将在用户提示词部分提供*

## 输出格式要求

### JSON结构模板参考
转换时请严格参考提供的标准JSON模板结构，根据实际输入内容动态生成对应的控件。

*具体的JSON模板将在用户提示词部分提供*

### 数据验证要求
- 所有必需字段必须存在
- 数值字段必须为纯数字类型
- 枚举字段必须使用预定义值
- JSON格式必须完全有效
- 严格遵循模板结构，但根据实际内容动态调整

## 特殊处理说明

### 缺失章节处理
- 如果输入Markdown缺少某些标准章节，直接跳过
- 重新分配序列编号，保持连续性
- 不生成空的占位符控件

### 重复标题处理
- **识别重复**：检测父级控件和子级控件是否具有相同或高度相似的标题
- **处理策略**：
  - 当父级TITLE控件和子级控件标题相同时，子级控件应省略title字段
  - 或者为子级控件使用更具体的标题，避免与父级重复
  - 优先保留父级标题，子级控件专注于内容展示

### 数据提取优先级
1. **图表数据优先**：数值型表格数据 → CHART控件（优先于TABLE控件）
2. 非数值表格数据 → TABLE控件
3. 列表结构 → LIST控件
4. 段落文本 → TEXT控件
5. 标题结构 → TITLE控件

## 质量检查清单

转换完成后，请确认：
- [ ] **模板一致性**：输出结构与JSON模板高度一致
- [ ] **JSON格式有效**：完全有效的JSON格式
- [ ] **序列编号正确**：所有serial编号连续且符合层级规则
- [ ] **数据准确性**：数值为数字类型，内容与原文一致
- [ ] **图表优先**：数值型表格数据转换为CHART控件
- [ ] **避免重复标题**：父子级控件无相同标题
- [ ] **没有虚构信息**：所有内容都基于输入的Markdown内容

<----------------------------(user_prompt)---------------------------->
=
请严格按照以上规则，将提供的Markdown房产评测报告转换为标准化的JSON格式。

### 重要提醒：JSON模板权威性是最高优先级要求

**严格遵循JSON模板结构！**
**以JSON模板为唯一转换标准！**
**采用填充式转换思路！**

### 转换执行要求

1. **严格遵循JSON模板**：以提供的JSON模板为唯一转换标准和参考依据
2. **填充式转换**：将输入内容填充到模板对应位置，不自由构建结构
3. **完全基于输入内容**：不添加任何虚构信息，只基于输入的Markdown内容
4. **动态省略**：仅在输入内容确实缺失时才省略模板中的相应部分
5. **参考JSON结构定义**：可参考JSON结构定义适当发挥，但以模板为准
6. **输出完全有效的JSON格式**：不包含任何解释性文字或代码块标记

### 参考模板

请严格参考以下JSON模板结构进行转换：

${json_template}

### JSON结构定义参考

可参考以下JSON结构定义进行适当发挥：
${json_structure_definition}

### 输入内容

以下是需要转换的Markdown报告内容：

```markdown
${cric_output}
```

### 输出要求

请基于提供的JSON模板和输入的Markdown内容，生成标准化的JSON结果。

**重要提醒**：
- **模板优先**：JSON模板是唯一转换标准，结构定义仅作参考
- **填充式转换**：将输入内容填充到模板对应位置
- **动态调整**：根据实际章节存在情况动态调整控件结构
- **保持连续性**：序列编号必须连续且符合逻辑
- **不得虚构**：不得添加模板中存在但输入内容中不存在的信息

开始转换，请直接输出JSON结果。

<----------------------------(json_structure_definition)---------------------------->
=
## JSON控件结构定义

### 基础控件结构
```json
{
  "serial": "序列编号",
  "type": "控件类型",
  "style": "样式类型",
  "title": "控件标题(可选,移除加粗标记)"
}
```

### TITLE控件
```json
{
  "serial": "序列编号",
  "type": "TITLE",
  "style": "DOCUMENT|SECTION|PARAGRAPH|ENTRY",
  "title": "标题内容"
}
```
**样式说明**：
- **DOCUMENT**：文档主标题，通常用于serial="0"
- **SECTION**：章节标题，用于一级标题(#)
- **PARAGRAPH**：段落标题，用于二级标题(##)
- **ENTRY**：条目标题，用于三级标题(###)

### TEXT控件
```json
{
  "serial": "序列编号",
  "type": "TEXT",
  "style": "BOARD|NORMAL",
  "title": "标题(可选)",
  "content": "文本内容"
}
```
**样式说明**：
- **BOARD**：重要文本内容，带边框显示
- **NORMAL**：普通文本内容

### LIST控件
```json
{
  "serial": "序列编号",
  "type": "LIST",
  "style": "BOARD|BULLET|NUMBER",
  "title": "列表标题(可选)",
  "content": [
    {
      "title": "项目标题",
      "content": "项目内容"
    }
  ]
}
```
**样式说明**：
- **BOARD**：重要列表，带边框显示
- **BULLET**：普通项目符号列表
- **NUMBER**：编号列表

### TABLE控件
```json
{
  "serial": "序列编号",
  "type": "TABLE",
  "style": "NORMAL",
  "title": "表格标题(可选)",
  "cols": ["列标题1", "列标题2"],
  "content": [
    [
      {"type": "TEXT", "content": "单元格内容1"},
      {"type": "TEXT", "content": "单元格内容2"}
    ]
  ]
}
```

### CHART控件
```json
{
  "serial": "序列编号",
  "type": "CHART",
  "style": "PIE|BAR|LINE",
  "title": "图表标题",
  "cols": ["X轴标签1", "X轴标签2"],  // PIE图不需要此字段
  "content": [
    {
      "title": "数据系列名称",
      "content": [数值1, 数值2]
    }
  ]
}
```
**样式说明**：
- **PIE**：饼图，用于占比数据，不需要cols字段
- **BAR**：柱状图，用于对比数据，必须包含cols字段
- **LINE**：折线图，用于趋势数据，必须包含cols字段

### CARD控件
```json
{
  "serial": "序列编号",
  "type": "CARD",
  "style": "BROKER|HOUSING|COMMUNITY",
  "title": "卡片标题",
  "name": "主要名称",
  "subtitle": "副标题",
  "tags": ["标签1", "标签2"],
  "fields": {
    "字段名": "字段值"
  }
}
```
**样式说明**：
- **BROKER**：置业顾问信息卡片
- **HOUSING**：房源信息卡片
- **COMMUNITY**：社区信息卡片

**BROKER卡片字段**
- **phone**: 联系电话
- **experience**: 服务年限
- **specialty**: 专业领域
- **serviceArea**: 服务区域
- **rating**: 客户评价
- **achievement**: 成功案例

**BROKER卡片字段**
- **layout**: 户型
- **area**: 建筑面积
- **floor**: 楼层信息
- **orientation**: 朝向
- **decoration**: 装修状况
- **totalPrice**: 总价
- **unitPrice**: 单价
- **propertyType**: 房产类型

**HCOMMUNITY卡片字段**
- **buildYear**: 建筑年代
- **propertyCompany**: 物业公司
- **propertyFee**: 物业费
- **greenRate**: 绿化率
- **plotRatio**: 容积率
- **parkingSpaces**: 停车位信息
- **facilities**: 主要配套设施


<----------------------------(json_template)---------------------------->
=
```json
{
  "type": "MONTHLY_REPORT",
  "title": "房产评测报告标题",
  "widgets": [
    {
      "serial": "0",
      "type": "TITLE",
      "style": "DOCUMENT",
      "title": "文档主标题"
    },
    {
      "serial": "1",
      "type": "TITLE",
      "style": "SECTION",
      "title": "报告基本信息"
    },
    {
      "serial": "1.1",
      "type": "LIST",
      "style": "BOARD",
      "title": "报告基本信息",
      "content": [
        {
          "title": "数据来源",
          "content": "数据来源信息(如:上海市房地产交易平台)"
        },
        {
          "title": "评测时间",
          "content": "评测时间信息(如:2025年7月)"
        },
        {
          "title": "平均价格概览",
          "content": "价格信息(如:97,600元/㎡)"
        }
      ]
    },
    {
      "serial": "1.2",
      "type": "TABLE",
      "style": "NORMAL",
      "title": "评测房源基本信息",
      "cols": ["项目", "详情"],
      "content": [
        [
          {"type": "TEXT", "content": "城市"},
          {"type": "TEXT", "content": "城市名称(如:上海市)"}
        ],
        [
          {"type": "TEXT", "content": "小区名称"},
          {"type": "TEXT", "content": "小区名称(如:慧芝湖花园)"}
        ],
        [
          {"type": "TEXT", "content": "户型"},
          {"type": "TEXT", "content": "户型信息(如:3室2厅2卫)"}
        ],
        [
          {"type": "TEXT", "content": "建筑面积"},
          {"type": "TEXT", "content": "面积信息(如:110㎡)"}
        ],
        [
          {"type": "TEXT", "content": "朝向"},
          {"type": "TEXT", "content": "朝向信息(如:朝南)"}
        ],
        [
          {"type": "TEXT", "content": "预估单价"},
          {"type": "TEXT", "content": "单价信息(如:97,600元/㎡)"}
        ],
        [
          {"type": "TEXT", "content": "板块位置"},
          {"type": "TEXT", "content": "位置信息(如:凉城板块)"}
        ]
      ]
    },
    {
      "serial": "2",
      "type": "TITLE",
      "style": "SECTION",
      "title": "小区基本信息分析"
    },
    {
      "serial": "2.1",
      "type": "TITLE",
      "style": "PARAGRAPH",
      "title": "小区户型分析(如:小区户型分析)"
    },
    {
      "serial": "2.1.1",
      "type": "TEXT",
      "style": "BOARD",
      "content": "户型分析文本内容(如:小区在售房源以3室户型为主，占比42.86%)"
    },
    {
      "serial": "2.1.2",
      "type": "CHART",
      "style": "BAR",
      "title": "在售房源户型对比(如:在售房源户型对比)",
      "cols": [
        "数据维度1(如:新增挂牌套数(套))",
        "数据维度2(如:挂牌均价(元/㎡))",
        "数据维度3(如:新增挂牌面积(㎡))"
      ],
      "content": [
        {
          "title": "户型1(如:2室)",
          "content": ["数值1", "数值2", "数值3"]
        },
        {
          "title": "户型2(如:3室)",
          "content": ["数值1", "数值2", "数值3"]
        },
        {
          "title": "户型3(如:4室)",
          "content": ["数值1", "数值2", "数值3"]
        }
      ]
    },
    {
      "serial": "2.1.3",
      "type": "CHART",
      "style": "LINE",
      "title": "小区近12个月市场走势(如:小区近12个月市场走势)",
      "cols": ["月份1(如:2024年08月)", "月份2(如:2024年09月)", "月份3(如:2024年10月)", "月份4(如:2024年11月)", "月份5(如:2024年12月)", "月份6(如:2025年01月)", "月份7(如:2025年02月)", "月份8(如:2025年03月)", "月份9(如:2025年04月)", "月份10(如:2025年05月)", "月份11(如:2025年06月)", "月份12(如:2025年07月)"],
      "content": [
        {
          "title": "数据系列1(如:挂牌均价)",
          "content": ["数值1", "数值2", "数值3", "数值4", "数值5", "数值6", "数值7", "数值8", "数值9", "数值10", "数值11", "数值12"]
        },
        {
          "title": "数据系列2(如:挂牌均价环比)",
          "content": ["数值1", "数值2", "数值3", "数值4", "数值5", "数值6", "数值7", "数值8", "数值9", "数值10", "数值11", "数值12"]
        },
        {
          "title": "数据系列3(如:新增挂牌套数)",
          "content": ["数值1", "数值2", "数值3", "数值4", "数值5", "数值6", "数值7", "数值8", "数值9", "数值10", "数值11", "数值12"]
        },
        {
          "title": "数据系列4(如:新增挂牌面积)",
          "content": ["数值1", "数值2", "数值3", "数值4", "数值5", "数值6", "数值7", "数值8", "数值9", "数值10", "数值11", "数值12"]
        },
        {
          "title": "数据系列5(如:成交套数)",
          "content": ["数值1", "数值2", "数值3", "数值4", "数值5", "数值6", "数值7", "数值8", "数值9", "数值10", "数值11", "数值12"]
        },
        {
          "title": "数据系列6(如:成交面积)",
          "content": ["数值1", "数值2", "数值3", "数值4", "数值5", "数值6", "数值7", "数值8", "数值9", "数值10", "数值11", "数值12"]
        },
        {
          "title": "数据系列7(如:成交均价)",
          "content": ["数值1", "数值2", "数值3", "数值4", "数值5", "数值6", "数值7", "数值8", "数值9", "数值10", "数值11", "数值12"]
        },
        {
          "title": "数据系列8(如:成交均价环比)",
          "content": ["数值1", "数值2", "数值3", "数值4", "数值5", "数值6", "数值7", "数值8", "数值9", "数值10", "数值11", "数值12"]
        }
      ]
    },
    {
      "serial": "2.1.4",
      "type": "LIST",
      "style": "BULLET",
      "title": "趋势分析",
      "content": [
        {
          "content": "趋势分析要点1(如:挂牌均价呈现波动上升趋势)"
        },
        {
          "content": "趋势分析要点2(如:2025年3月达到挂牌均价峰值)"
        },
        {
          "content": "趋势分析要点3(如:成交活跃期集中在某个时间段)"
        }
      ]
    },
    {
      "serial": "2.2",
      "type": "TITLE",
      "style": "PARAGRAPH",
      "title": "板块市场对比分析(如:板块市场对比分析)"
    },
    {
      "serial": "2.2.1",
      "type": "CHART",
      "style": "LINE",
      "title": "板块近12个月走势(如:板块近12个月走势)",
      "cols": ["月份1(如:2024年08月)", "月份2(如:2024年09月)", "月份3(如:2024年10月)", "月份4(如:2024年11月)", "月份5(如:2024年12月)", "月份6(如:2025年01月)", "月份7(如:2025年02月)", "月份8(如:2025年03月)", "月份9(如:2025年04月)", "月份10(如:2025年05月)", "月份11(如:2025年06月)", "月份12(如:2025年07月)"],
      "content": [
        {
          "title": "板块数据系列1(如:挂牌均价)",
          "content": ["数值1", "数值2", "数值3", "数值4", "数值5", "数值6", "数值7", "数值8", "数值9", "数值10", "数值11", "数值12"]
        },
        {
          "title": "板块数据系列2(如:挂牌均价环比)",
          "content": ["数值1", "数值2", "数值3", "数值4", "数值5", "数值6", "数值7", "数值8", "数值9", "数值10", "数值11", "数值12"]
        },
        {
          "title": "板块数据系列3(如:新增挂牌套数)",
          "content": ["数值1", "数值2", "数值3", "数值4", "数值5", "数值6", "数值7", "数值8", "数值9", "数值10", "数值11", "数值12"]
        },
        {
          "title": "板块数据系列4(如:新增挂牌面积)",
          "content": ["数值1", "数值2", "数值3", "数值4", "数值5", "数值6", "数值7", "数值8", "数值9", "数值10", "数值11", "数值12"]
        },
        {
          "title": "板块数据系列5(如:成交套数)",
          "content": ["数值1", "数值2", "数值3", "数值4", "数值5", "数值6", "数值7", "数值8", "数值9", "数值10", "数值11", "数值12"]
        },
        {
          "title": "板块数据系列6(如:成交面积)",
          "content": ["数值1", "数值2", "数值3", "数值4", "数值5", "数值6", "数值7", "数值8", "数值9", "数值10", "数值11", "数值12"]
        },
        {
          "title": "板块数据系列7(如:成交均价)",
          "content": ["数值1", "数值2", "数值3", "数值4", "数值5", "数值6", "数值7", "数值8", "数值9", "数值10", "数值11", "数值12"]
        },
        {
          "title": "板块数据系列8(如:成交均价环比)",
          "content": ["数值1", "数值2", "数值3", "数值4", "数值5", "数值6", "数值7", "数值8", "数值9", "数值10", "数值11", "数值12"]
        }
      ]
    },
    {
      "serial": "2.2.2",
      "type": "LIST",
      "style": "BOARD",
      "title": "板块对比分析",
      "content": [
        {
          "content": "板块对比分析要点1(如:小区挂牌均价显著高于板块平均水平)"
        },
        {
          "content": "板块对比分析要点2(如:小区成交均价波动情况分析)"
        },
        {
          "content": "板块对比分析要点3(如:板块成交高峰期分析)"
        }
      ]
    },
    {
      "serial": "3",
      "type": "TITLE",
      "style": "SECTION",
      "title": "区域价值"
    },
    {
      "serial": "3.1",
      "type": "TEXT",
      "style": "BOARD",
      "content": "区域价值描述文本(如:作为核心居住板块，坐拥优质资源，高绿化率营造宜居环境)"
    },
    {
      "serial": "3.2",
      "type": "LIST",
      "style": "BOARD",
      "title": "区域核心价值体现",
      "content": [
        {
          "title": "价值优势标题1(如:交通枢纽优势)",
          "content": "价值优势描述1(如:步行范围内覆盖地铁站与公交干线)"
        },
        {
          "title": "价值优势标题2(如:教育资源矩阵)",
          "content": "价值优势描述2(如:覆盖幼儿园至小学优质教育机构)"
        },
        {
          "title": "价值优势标题3(如:商业配套集群)",
          "content": "价值优势描述3(如:购物中心等商业体形成生活圈)"
        },
        {
          "title": "价值优势标题4(如:生态宜居品质)",
          "content": "价值优势描述4(如:低容积率与优质设计保障居住舒适度)"
        }
      ]
    },
    {
      "serial": "4",
      "type": "TITLE",
      "style": "SECTION",
      "title": "交通网络"
    },
    {
      "serial": "4.1",
      "type": "LIST",
      "style": "BULLET",
      "content": [
        {
          "content": "交通方式1描述(如:轨交动脉：距地铁站约XXX米，快速连接核心商圈)"
        },
        {
          "content": "交通方式2描述(如:公交覆盖：汇集多条公交线路，形成交通网络)"
        },
        {
          "content": "交通方式3描述(如:路网体系：多条主干道构成路网，便捷出行)"
        }
      ]
    },
    {
      "serial": "5",
      "type": "TITLE",
      "style": "SECTION",
      "title": "生活配套"
    },
    {
      "serial": "5.1",
      "type": "TITLE",
      "style": "PARAGRAPH",
      "title": "医疗旗舰(如:医疗旗舰)"
    },
    {
      "serial": "5.1.1",
      "type": "LIST",
      "style": "BULLET",
      "content": [
        {
          "content": "医疗机构1(如:登特口腔（348米）：专业口腔医疗机构)"
        },
        {
          "content": "医疗机构2(如:益丰大药房（166米）：24小时便民药房)"
        },
        {
          "content": "医疗机构3(如:赞瞳眼科诊所（500米）：专科眼科服务)"
        }
      ]
    },
    {
      "serial": "5.2",
      "type": "TITLE",
      "style": "PARAGRAPH",
      "title": "商业矩阵(如:商业矩阵)"
    },
    {
      "serial": "5.2.1",
      "type": "LIST",
      "style": "BULLET",
      "content": [
        {
          "content": "商业设施1(如:百联莘荟购物中心（500米）：4.5星评级综合体，内含知名品牌)"
        },
        {
          "content": "商业设施2(如:宝华现代城商业街（489米）：特色餐饮聚集地)"
        },
        {
          "content": "商业设施3(如:百果园（56米）：社区生鲜便利站)"
        }
      ]
    },
    {
      "serial": "5.3",
      "type": "TITLE",
      "style": "PARAGRAPH",
      "title": "休闲图鉴(如:休闲图鉴)"
    },
    {
      "serial": "5.3.1",
      "type": "LIST",
      "style": "BULLET",
      "content": [
        {
          "content": "休闲设施1(如:自然运动·普拉提（433米）：高端健身会所)"
        },
        {
          "content": "休闲设施2(如:星巴克（199米）：社区咖啡社交空间)"
        },
        {
          "content": "休闲设施3(如:和记小菜（308米）：4.6分评价的本帮菜餐厅)"
        }
      ]
    },
    {
      "serial": "6",
      "type": "TITLE",
      "style": "SECTION",
      "title": "教育资源"
    },
    {
      "serial": "6.1",
      "type": "TITLE",
      "style": "PARAGRAPH",
      "title": "全龄教育链(如:全龄教育链)"
    },
    {
      "serial": "6.1.1",
      "type": "LIST",
      "style": "BULLET",
      "content": [
        {
          "content": "教育机构1(如:大宁国际第二幼儿园（355米）：区级示范园)"
        },
        {
          "content": "教育机构2(如:上海市大宁国际小学（254米）：优质公办教育)"
        },
        {
          "content": "教育机构3(如:静安区大宁路小学（518米）：历史悠久的重点小学)"
        }
      ]
    },
    {
      "serial": "6.2",
      "type": "TITLE",
      "style": "PARAGRAPH",
      "title": "特色优势(如:特色优势)"
    },
    {
      "serial": "6.2.1",
      "type": "TEXT",
      "style": "BOARD",
      "content": "教育优势描述(如:形成优质教育圈，实现便利教育体验，适合家庭置业需求)"
    },
    {
      "serial": "7",
      "type": "TITLE",
      "style": "SECTION",
      "title": "小区品质"
    },
    {
      "serial": "7.1",
      "type": "TITLE",
      "style": "PARAGRAPH",
      "title": "生态美学"
    },
    {
      "serial": "7.1.1",
      "type": "TEXT",
      "style": "BOARD",
      "content": "品质描述文本"
    },
    {
      "serial": "7.2",
      "type": "TITLE",
      "style": "PARAGRAPH",
      "title": "建筑基因"
    },
    {
      "serial": "7.2.1",
      "type": "LIST",
      "style": "BULLET",
      "title": "建筑特色",
      "content": [
        {
          "title": "",
          "content": "建筑特色1"
        },
        {
          "title": "",
          "content": "建筑特色2"
        }
      ]
    },
    {
      "serial": "7.3",
      "type": "TITLE",
      "style": "PARAGRAPH",
      "title": "服务标准"
    },
    {
      "serial": "7.3.1",
      "type": "TEXT",
      "style": "BOARD",
      "content": "龙湖物业提供星级服务，配备3494个停车位（车位比1:0.7），实行人车分流管理"
    },
    {
      "serial": "7.4",
      "type": "TITLE",
      "style": "PARAGRAPH",
      "title": "生活场景"
    },
    {
      "serial": "7.4.1",
      "type": "TEXT",
      "style": "BOARD",
      "content": "晨间可步行至星巴克享用早餐，下午在社区园林散步，晚间步行5分钟即达购物中心，完美演绎静安高品质生活范式"
    },
    {
      "serial": "8",
      "type": "TITLE",
      "style": "SECTION",
      "title": "专业服务推荐"
    },
    {
      "serial": "8.1",
      "type": "CARD",
      "style": "BROKER",
      "title": "置业顾问推荐",
      "name": "顾问姓名(如:张经理)",
      "subtitle": "职业描述(如:资深置业顾问)",
      "tags": ["标签1(如:资深)", "标签2(如:高评分)", "标签3(如:专家)"],
      "fields": {
        "phone": "联系电话(如:138-0000-1234)",
        "experience": "服务年限(如:8年)",
        "specialty": "专业领域(如:静安区房产投资、学区房置业)",
        "serviceArea": "服务区域(如:大宁板块、凉城板块)",
        "rating": "客户评价(如:4.8分（基于126条评价）)",
        "achievement": "成功案例(如:累计成交房源超过200套，专注中高端住宅)"
      }
    },
    {
      "serial": "8.2",
      "type": "CARD",
      "style": "HOUSING",
      "title": "优质房源推荐",
      "name": "房源名称(如:慧芝湖花园精选房源)",
      "subtitle": "户型描述(如:3室2厅2卫)",
      "tags": ["房源特色1(如:学区房)", "房源特色2(如:地铁房)", "房源特色3(如:满五唯一)"],
      "fields": {
        "layout": "户型(如:3室2厅2卫)",
        "area": "建筑面积(如:110㎡)",
        "floor": "楼层信息(如:15/18层)",
        "orientation": "朝向(如:朝南)",
        "decoration": "装修状况(如:精装修)",
        "totalPrice": "总价(如:1073万)",
        "unitPrice": "单价(如:97,545元/㎡)",
        "propertyType": "房产类型(如:商品房)"
      }
    },
    {
      "serial": "8.3",
      "type": "CARD",
      "style": "COMMUNITY",
      "title": "社区配套介绍",
      "name": "小区名称(如:慧芝湖花园)",
      "subtitle": "建成年份(如:2004-2009年)",
      "tags": ["小区特色1(如:高绿化)", "小区特色2(如:品牌物业)", "小区特色3(如:人车分流)"],
      "fields": {
        "buildYear": "建筑年代(如:2004-2009年)",
        "propertyCompany": "物业公司(如:龙湖物业)",
        "propertyFee": "物业费(如:2.7元/月/㎡)",
        "greenRate": "绿化率(如:45%)",
        "plotRatio": "容积率(如:2.5)",
        "parkingSpaces": "停车位信息(如:3494个车位，车位比1:0.7)",
        "facilities": "主要配套设施(如:中央景观带、健身会所、24小时安保、人车分流系统)"
      }
    }
  ]
}

```

<----------------------------(cric_output_flow)---------------------------->
=
${flow.getGenerateResponse("VALUE_EVALUATION_CRIC_2_MD")}

${flow.getGenerateResponse("VALUE_EVALUATION_AROUND_MD")}

<----------------------------(cric_output)---------------------------->
=
# 保利天悦1室1厅1卫价值评测报告

## 报告基本信息

- **数据来源**：广州市房地产交易平台
- **评测时间**：2025年7月
- **平均价格概览**：**45,200元/㎡**

## 房源信息

| 项目   | 详情            |
|------|---------------|
| 城市   | 广州市           |
| 小区名称 | 保利天悦         |
| 户型   | 1室1厅1卫        |
| 建筑面积 | 52㎡          |
| 朝向   | 朝西            |
| 预估单价 | 45,200元/㎡     |
| 板块位置 | 天河区（挂牌板块：珠江新城板块） |

## 市场数据

### 户型分析

| 户型 | 挂牌套数(套) | 均价(元/㎡) |
|----|-----------|-----------|
| 1室 | 8         | 46,500   |
| 2室 | 12        | 44,800   |
| 3室 | 5         | 43,200   |

**分析**：1室户型价格最高，显示小户型稀缺性。

### 价格走势

| 月度       | 挂牌均价(元/㎡) | 成交套数(套) | 成交均价(元/㎡) |
|----------|-----------|---------|-----------|
| 2025年01月 | 42,800    | 8       | 41,500    |
| 2025年02月 | 43,500    | 12      | 42,200    |
| 2025年03月 | 44,200    | 15      | 43,800    |
| 2025年04月 | 44,800    | 9       | 44,200    |
| 2025年05月 | 45,500    | 11      | 44,900    |
| 2025年06月 | 45,800    | 6       | 45,200    |
| 2025年07月 | 45,200    | 2       | 45,800    |

**趋势**：价格稳步上升，半年涨幅约5.6%。

## 区域价值

保利天悦位于广州天河区珠江新城核心区域，享受CBD商务配套。项目2016年建成，保利物业管理。

**优势**：
- **CBD核心**：珠江新城金融商务区
- **地铁便利**：距3号线珠江新城站500米
- **商业配套**：天环广场、太古汇等高端商业

## 交通配套

- **地铁**：3号线珠江新城站、5号线猎德站
- **公交**：多条公交线路覆盖
- **道路**：临江大道、天河路主干道

## 生活配套

### 商业
- 天环广场（300米）
- 太古汇（800米）
- IFC国际金融中心（1公里）

### 教育
- 天河区第一实验小学（600米）
- 华南师范大学附属中学（1.2公里）

## 项目品质

### 基本信息
- **建成年份**：2016年
- **建筑类型**：高层住宅
- **容积率**：4.5
- **绿化率**：30%

### 物业服务
保利物业管理，物业费3.8元/月/㎡。

## 总结

保利天悦凭借珠江新城核心位置和完善配套，适合投资和自住，具有较好的保值增值潜力。
