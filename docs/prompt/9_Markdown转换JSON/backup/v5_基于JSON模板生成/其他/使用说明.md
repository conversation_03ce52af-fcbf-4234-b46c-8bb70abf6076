# Markdown到JSON转换解决方案使用说明

## 方案概述

本解决方案提供了一套完整的Markdown房产评测报告到JSON格式的转换工具，包含标准化模板、AI提示词和使用指南。

## 文件结构

```
v5/
├── markdown报告.md          # 示例Markdown报告
├── json报告.json           # 示例JSON输出
├── JSON结构定义文档.md      # JSON格式规范文档
├── json_template.json      # 标准JSON模板
├── ai_conversion_prompt.md # AI转换提示词
└── 使用说明.md             # 本文档
```

## 核心特性

### 1. 动态章节适应
- **智能识别**：自动识别输入Markdown的实际章节结构
- **灵活处理**：支持章节缺失、顺序变化的情况
- **自动编号**：根据实际内容动态分配序列编号

### 2. 数据完整性保证
- **严禁虚构**：只基于输入内容转换，不添加任何虚假信息
- **准确转换**：保持所有数值、文本、表格数据的准确性
- **结构完整**：确保输出JSON格式完全有效

### 3. 多种控件支持
- **TITLE**：文档标题和章节标题
- **TEXT**：段落文本和分析内容
- **LIST**：各种列表结构
- **TABLE**：表格数据展示
- **CHART**：图表数据可视化
- **CARD**：结构化信息卡片

## 使用方法

### 方法一：直接使用AI提示词

1. **准备输入**：将需要转换的Markdown报告准备好
2. **应用提示词**：将`ai_conversion_prompt.md`中的提示词复制到AI对话中
3. **输入内容**：在提示词后面粘贴Markdown报告内容
4. **获取结果**：AI将直接输出标准化的JSON格式

### 方法二：基于模板定制

1. **参考模板**：查看`json_template.json`了解标准结构
2. **分析输入**：分析待转换Markdown的章节结构
3. **定制转换**：根据实际需求调整转换规则
4. **执行转换**：使用定制后的规则进行转换

## 转换规则说明

### 章节映射规则

| Markdown语法 | JSON控件类型 | Style属性 | Serial格式 |
|-------------|-------------|-----------|-----------|
| # 主标题     | TITLE       | DOCUMENT  | "0"       |
| ## 一级章节  | TITLE       | SECTION   | "1","2"   |
| ### 二级段落 | TITLE       | PARAGRAPH | "1.1","1.2" |
| #### 三级条目| TITLE       | ENTRY     | "1.1.1"   |

### 内容类型识别

| 内容特征 | 控件类型 | Style选择 |
|---------|---------|-----------|
| 段落文本 | TEXT | BOARD/EMPHASIS/PLAIN |
| 有序列表 | LIST | SERIAL |
| 无序列表 | LIST | BULLET |
| 重点列表 | LIST | BOARD |
| 数据表格 | TABLE | NORMAL/BOARD |
| 数值数据 | CHART | PIE/BAR/LINE |

### 数据处理规则

1. **数值转换**：≥10000的数值转换为万单位
2. **空值处理**："-"或空值转换为null
3. **格式清理**：移除title字段的加粗标记
4. **内容保留**：content字段保留加粗标记

## 质量保证

### 转换前检查
- [ ] Markdown格式规范
- [ ] 章节结构清晰
- [ ] 表格格式正确
- [ ] 数据内容完整

### 转换后验证
- [ ] JSON格式有效
- [ ] 序列编号连续
- [ ] 数据类型正确
- [ ] 内容完整准确

## 常见问题处理

### Q1: 章节缺失怎么办？
**A**: 系统会自动跳过缺失章节，重新分配序列编号保持连续性。

### Q2: 表格格式不规范？
**A**: 系统会尝试修复，无法修复时转换为TEXT控件。

### Q3: 数值数据异常？
**A**: 无法解析的数值保持原文本格式，不会强制转换。

### Q4: 如何处理特殊字符？
**A**: 系统会正确处理JSON转义，确保输出格式有效。

## 扩展定制

### 添加新控件类型
1. 在JSON结构定义中添加新控件规范
2. 更新AI提示词中的识别规则
3. 在模板中添加示例结构

### 调整转换规则
1. 修改`ai_conversion_prompt.md`中的转换规则
2. 更新示例和边界情况处理
3. 测试验证转换效果

### 适配新报告类型
1. 分析新报告的结构特征
2. 调整章节映射规则
3. 更新控件类型选择逻辑

## 技术支持

如需技术支持或功能定制，请参考：
- `JSON结构定义文档.md`：详细的JSON格式规范
- `ai_conversion_prompt.md`：完整的转换规则说明
- 示例文件：`markdown报告.md` 和 `json报告.json`

## 版本信息

- **当前版本**：v5
- **更新日期**：2025年7月
- **主要特性**：动态章节适应、数据完整性保证、多控件支持
