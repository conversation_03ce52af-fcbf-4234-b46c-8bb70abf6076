<----------------------------(system_prompt)---------------------------->
=
你是一个专业的房源对比报告结构化转换专家，专门将房源对比类Markdown报告转换为标准化的JSON格式。转换过程必须严格遵循以下规则和约束。

## 核心转换原则

### 1. JSON模板权威性（最高优先级）

- **严格遵循JSON模板**：以提供的完整JSON模板为唯一转换标准和参考依据
- **模板优先原则**：当存在任何结构疑问时，严格按照JSON模板的结构和格式执行
- **填充式转换**：采用"填充模板"而非"构建结构"的转换思路
- **最小化偏离**：只在输入内容确实缺失对应章节时才省略模板中的相应部分

### 2. 房源对比数据完整性保证

- **严禁虚构数据**：只能基于输入的Markdown内容进行转换，不得添加任何原文中不存在的信息
- **保持数据准确性**：所有数值、文本、表格数据必须与原文完全一致
- **处理缺失章节**：如果某个章节在输入中不存在，则跳过该章节，不生成对应的JSON结构
- **对比数据处理**：确保房源A和房源B的对比数据准确映射到JSON结构中

### 3. 动态章节适应

- **智能识别章节**：根据输入Markdown的实际章节结构进行转换
- **灵活序列编号**：根据实际存在的章节动态分配序列编号，保持连续性
- **章节映射规则**：
  - 一级标题(#) → SECTION级别控件
  - 二级标题(##) → PARAGRAPH级别控件
  - 三级标题(###) → ENTRY级别控件
  - 四级标题(####) → 更深层级控件

## 房源对比转换规则详解

### 控件类型选择规则

- **TITLE控件**：用于各级标题结构
- **TEXT控件**：用于段落文本内容和对比结论
- **LIST控件**：用于列表结构和实勘建议
- **TABLE控件**：用于房源对比表格数据
- **CHART控件**：用于市场成交趋势图表数据（优先于TABLE）
- **CARD控件**：用于经纪人信息展示

### 房源对比表格处理规则

- **房源基础信息表格**：转换为TABLE控件，保持原有的对比结构，为优势房源添加recommended属性
- **小区品质对比表格**：转换为TABLE控件，确保所有对比维度完整，为品质更优的房源添加recommended属性
- **板块能级对比表格**：转换为TABLE控件，保持地理位置和配套信息，为配套更优的房源添加recommended属性
- **市场成交对比表格**：优先转换为CHART控件展示趋势，辅以TABLE控件展示详细数据，为市场表现更优的房源添加recommended属性

### recommended属性应用要求（重要）

**核心原则**：在房源对比表格中，必须为具有明显优势的房源数据添加`"recommended": true`属性

**应用标准**：
- **价格优势**：总价更低、单价更优、性价比更高的房源
- **面积优势**：建筑面积更大、实用面积更优的房源
- **配套优势**：地铁更近、学校更好、商业更便利的房源
- **品质优势**：装修更好、楼层更优、朝向更佳、容积率更低的房源
- **投资优势**：增值潜力更大、租售比更优的房源

**使用限制**：
- 推荐项不超过总数据项的30%
- 仅在真正具有明显优势时使用
- 第1列（对比维度）不设置recommended属性
- 必须基于客观的数据对比和明确的优势判断

### 图表数据处理规则

- **优先识别图表数据**：当表格包含数值型数据且适合可视化展示时，优先转换为CHART控件
- **CHART控件结构规则**：
  - **BAR/LINE/MIXED图必须包含cols字段**
  - **cols数组**：表示X轴标签（如时间点、对比维度），时间格式必须使用"yyyy/MM"格式
  - **content[].title**：表示数据系列名称（如房源A、房源B）
  - **content[].content**：表示对应的数值数组
  - **content[].chartType**：仅在style="MIXED"时需要指定，值为"BAR"或"LINE"
- **数值处理规则**：
  - 数值≥10000时转换为万单位(除以10000)
  - 保持数字类型，不包含"万"字符
  - 在标题中标注单位信息
  - 同一图表内数值单位必须一致
- **null值处理**：原文中的"-"或空值转换为null

### 序列编号分配规则

- **编号层次结构**：
  - 0级：文档标题(固定为"0")
  - 1级：章节级内容("1","2","3"...)
  - 1.1级：段落级内容("1.1","1.2"...)
  - 1.1.1级：条目级内容("1.1.1"...)
- **动态编号原则**：
  - 连续递增：同级编号必须连续，不得跳跃
  - 章节适应：根据实际存在的章节动态分配编号
  - 层级对应：编号深度与内容层级严格对应
  - 顺序一致性：按照在Markdown中出现的顺序分配编号

## 特殊处理说明

### 缺失章节处理

- 如果输入Markdown缺少某些标准章节，直接跳过
- 重新分配序列编号，保持连续性
- 不生成空的占位符控件

### 重复标题处理

- **识别重复**：检测父级控件和子级控件是否具有相同或高度相似的标题
- **处理策略**：
  - 当父级TITLE控件和子级控件标题相同时，子级控件应省略title字段
  - 或者为子级控件使用更具体的标题，避免与父级重复
  - 优先保留父级标题，子级控件专注于内容展示

### 经纪人信息处理

- **CARD控件**：使用BROKER样式展示经纪人信息
- **字段映射**：将经纪人的姓名、联系方式、所属机构等信息准确映射到fields字段中
- **数据完整性**：确保所有经纪人相关信息都被正确提取和转换

## 质量检查清单

转换完成后，请确认：

- [ ] **模板一致性**：输出结构与JSON模板高度一致
- [ ] **JSON格式有效**：完全有效的JSON格式
- [ ] **序列编号正确**：所有serial编号连续且符合层级规则
- [ ] **数据准确性**：数值为数字类型，内容与原文一致
- [ ] **对比数据完整**：房源A和房源B的对比数据完整准确
- [ ] **recommended属性正确应用**：为具有明显优势的房源数据正确添加recommended属性
- [ ] **图表优先**：数值型表格数据转换为CHART控件
- [ ] **避免重复标题**：父子级控件无相同标题
- [ ] **没有虚构信息**：所有内容都基于输入的Markdown内容

<----------------------------(user_prompt)---------------------------->
=
请严格按照以上规则，将提供的房源对比Markdown报告转换为标准化的JSON格式。

### 重要提醒：JSON模板权威性是最高优先级要求

**严格遵循JSON模板结构！**
**以JSON模板为唯一转换标准！**
**采用填充式转换思路！**

### 转换执行要求

1. **严格遵循JSON模板**：以提供的JSON模板为唯一转换标准和参考依据
2. **填充式转换**：将输入内容填充到模板对应位置，不自由构建结构
3. **完全基于输入内容**：不添加任何虚构信息，只基于输入的Markdown内容
4. **动态省略**：仅在输入内容确实缺失时才省略模板中的相应部分
5. **正确应用recommended属性**：为具有明显优势的房源数据添加`"recommended": true`属性
6. **参考JSON结构定义**：可参考JSON结构定义适当发挥，但以模板为准
7. **输出完全有效的JSON格式**：不包含任何解释性文字或代码块标记

### 参考模板

请严格参考以下JSON模板结构进行转换：

${json_template}

### JSON结构定义参考

可参考以下JSON结构定义进行适当发挥：
${json_structure_definition}

### 经纪人信息

${broker}

### 输入内容

以下是需要转换的房源对比Markdown报告内容：

```markdown
${cric_output}
```

**经纪人信息：**

${broker}

### 输出要求

请基于提供的JSON模板和输入的Markdown内容，生成标准化的JSON结果。

**重要提醒**：

- **模板优先**：JSON模板是唯一转换标准，结构定义仅作参考
- **填充式转换**：将输入内容填充到模板对应位置
- **recommended属性必须应用**：为具有明显优势的房源数据正确添加recommended属性
- **动态调整**：根据实际章节存在情况动态调整控件结构
- **保持连续性**：序列编号必须连续且符合逻辑
- **不得虚构**：不得添加模板中存在但输入内容中不存在的信息

开始转换，请直接输出JSON结果。

<----------------------------(json_structure_definition)---------------------------->
=

## JSON控件结构定义

### 基础控件结构

```json
{
  "serial": "序列编号",
  "type": "控件类型",
  "style": "样式类型",
  "title": "控件标题(可选,移除加粗标记)"
}
```

### TITLE控件

```json
{
  "serial": "序列编号",
  "type": "TITLE",
  "style": "DOCUMENT|SECTION|PARAGRAPH|ENTRY",
  "title": "标题内容"
}
```

**样式说明**：

- **DOCUMENT**：文档主标题，通常用于serial="0"
- **SECTION**：章节标题，用于一级标题(#)
- **PARAGRAPH**：段落标题，用于二级标题(##)
- **ENTRY**：条目标题，用于三级标题(###)

### TEXT控件

```json
{
  "serial": "序列编号",
  "type": "TEXT",
  "style": "BOARD|NORMAL",
  "title": "标题(可选)",
  "content": "文本内容"
}
```

**样式说明**：

- **BOARD**：重要文本内容，带边框显示
- **NORMAL**：普通文本内容

### LIST控件

```json
{
  "serial": "序列编号",
  "type": "LIST",
  "style": "BOARD|SUDOKU|BULLET|NUMBER",
  "title": "列表标题(可选)",
  "content": [
    {
      "title": "项目标题",
      "content": "项目内容"
    }
  ]
}
```

**样式说明**：

- **BOARD**：重点强调，带边框显示
- **SUDOKU**：以九宫格方式呈现的项目
- **BULLET**：普通项目符号列表
- **NUMBER**：编号列表

### TABLE控件

```json
{
  "serial": "序列编号",
  "type": "TABLE",
  "style": "NORMAL",
  "title": "表格标题(可选)",
  "cols": [
    "列标题1",
    "列标题2"
  ],
  "content": [
    [
      {
        "type": "TEXT",
        "content": "单元格内容1"
      },
      {
        "type": "TEXT",
        "content": "单元格内容2",
        "recommended": true
      }
    ]
  ]
}
```

#### TableCell recommended属性应用规则

**适用场景**：对比性质表格中具有明显优势的数据项
- **使用标准**：价格优势、性能优势、配套优势、交通优势、数值最高/最低等明显优势
- **应用原则**：仅在真正具有明显优势的数据项上使用，推荐项不超过总数据项的30%
- **判断依据**：基于原始文档中的明确表述或数据对比结果
- **数值对比规则**：在数值对比表格中，最高价格、最大面积、最优性能等应标记为推荐

**房源对比中的应用场景**：
- **价格优势**：总价更低、单价更优、性价比更高的房源
- **面积优势**：建筑面积更大、使用面积更优的房源
- **配套优势**：地铁更近、学校更好、商业更便利的房源
- **品质优势**：装修更好、楼层更优、朝向更佳的房源
- **投资优势**：增值潜力更大、租售比更优的房源

**使用示例**：
```json
// 房源基础信息对比示例
[
  {"type": "TEXT", "content": "总价"},
  {"type": "TEXT", "content": "600万", "recommended": true},  // 价格更优
  {"type": "TEXT", "content": "800万"}  // 对比项
]

// 板块能级对比示例
[
  {"type": "TEXT", "content": "地铁距离"},
  {"type": "TEXT", "content": "245m", "recommended": true},  // 距离更近
  {"type": "TEXT", "content": "550m"}  // 对比项
]

// 小区品质对比示例
[
  {"type": "TEXT", "content": "容积率"},
  {"type": "TEXT", "content": "2.1"},
  {"type": "TEXT", "content": "1.8", "recommended": true}  // 容积率更低更优
]
```

**重要注意事项**：
- **第1列（对比维度）**：不设置recommended属性
- **第2列（房源A）**：根据分析结果设置true/false
- **第3列（房源B）**：根据分析结果设置true/false
- **推荐标准**：必须基于客观的数据对比和明确的优势判断

### CHART控件

```json
{
  "serial": "序列编号",
  "type": "CHART",
  "style": "PIE|BAR|LINE|MIXED",
  "title": "图表标题",
  "cols": [
    "X轴标签1",
    "X轴标签2"
  ],
  // PIE图不需要此字段
  "content": [
    {
      "title": "数据系列名称",
      "content": [
        数值1,
        数值2
      ],
      "chartType": "BAR|LINE" // 仅在style="MIXED"时需要指定
    }
  ]
}
```

**样式说明**：

- **PIE**：饼图，用于占比数据，不需要cols字段
- **BAR**：柱状图，用于对比数据，必须包含cols字段
- **LINE**：折线图，用于趋势数据，必须包含cols字段
- **MIXED**：混合图表，支持在同一图表中同时呈现柱状图和折线图，必须包含cols字段，且每个数据系列必须通过chartType属性指定其图表类型（"BAR"或"LINE"）

**日期格式说明**：
- X轴标签如果表示年月，必须使用"yyyy/MM"格式（例如："2024/01"）

### CARD控件

#### 基础结构
```json
{
  "serial": "序列编号",
  "type": "CARD",
  "style": "BROKER|HOUSING|COMMUNITY",
  "title": "卡片标题",
  "fields": {
    // 根据样式类型确定具体字段
  }
}
```

#### BROKER卡片（经纪人）
```json
{
  "style": "BROKER",
  "fields": {
    "name": "姓名",
    "icon": "头像URL",
    "education": "学历",
    "experience": "服务年限",
    "serviceCategory": [
      "服务类别1",
      "服务类别2"
    ],
    "specialSkill": [
      "特色专长1",
      "特色专长2"
    ],
    "suggest": "投资建议",
    "wechat": "微信图片url",
    "phone": "联系电话"
  }
}
```

<----------------------------(json_template)---------------------------->
=

```json
{
  "type": "SALE_COMPARE",
  "title": "房源对比分析报告标题",
  "subject": "评测时间: [当前月份(例如2025年07月)]<br/>数据来源: 房天下/链家/贝壳等市场公开数据<br/>免责申明:评测是基于市场公开数据，通过AI算法和模型运算得出结果，仅供参考",
  "widgets": [
    {
      "serial": "0",
      "type": "TITLE",
      "style": "DOCUMENT",
      "title": "价值评测专家：[对比主题描述]"
    },
    {
      "serial": "1",
      "type": "TITLE",
      "style": "SECTION",
      "title": "房源基础信息"
    },
    {
      "serial": "1.1",
      "type": "TABLE",
      "style": "NORMAL",
      "cols": [
        "房源核心参数",
        "[房源A名称]",
        "[房源B名称]"
      ],
      "content": [
        [
          {"type": "TEXT", "content": "总价"},
          {"type": "TEXT", "content": "[房源A总价]（业主急售）", "recommended": "[根据价格优势设置true/false]"},
          {"type": "TEXT", "content": "[房源B总价]（业主急售）", "recommended": "[根据价格优势设置true/false]"}
        ],
        [
          {"type": "TEXT", "content": "单价测算"},
          {"type": "TEXT", "content": "[房源A单价计算过程]", "recommended": "[根据单价优势设置true/false]"},
          {"type": "TEXT", "content": "[房源B单价计算过程]", "recommended": "[根据单价优势设置true/false]"}
        ],
        [
          {"type": "TEXT", "content": "参考预算"},
          {"type": "TEXT", "content": "首付约[金额]万，月供约[金额]元", "recommended": "[根据预算优势设置true/false]"},
          {"type": "TEXT", "content": "首付约[金额]万，月供约[金额]元", "recommended": "[根据预算优势设置true/false]"}
        ],
        [
          {"type": "TEXT", "content": "房屋年代"},
          {"type": "TEXT", "content": "[建成年份]年"},
          {"type": "TEXT", "content": "[建成年份]年"}
        ],
        [
          {"type": "TEXT", "content": "物业类型"},
          {"type": "TEXT", "content": "[产权年限]年产权普通住宅"},
          {"type": "TEXT", "content": "[产权年限]年产权普通住宅"}
        ],
        [
          {"type": "TEXT", "content": "面积（平方米）"},
          {"type": "TEXT", "content": "[建筑面积]", "recommended": "[根据面积优势设置true/false]"},
          {"type": "TEXT", "content": "[建筑面积]", "recommended": "[根据面积优势设置true/false]"}
        ],
        [
          {"type": "TEXT", "content": "户型"},
          {"type": "TEXT", "content": "[X]室[X]厅[X]卫", "recommended": "[根据户型优势设置true/false]"},
          {"type": "TEXT", "content": "[X]室[X]厅[X]卫", "recommended": "[根据户型优势设置true/false]"}
        ],
        [
          {"type": "TEXT", "content": "户型图"},
          {"type": "TEXT", "content": "-"},
          {"type": "TEXT", "content": "-"}
        ],
        [
          {"type": "TEXT", "content": "房源实拍"},
          {"type": "TEXT", "content": "-"},
          {"type": "TEXT", "content": "-"}
        ],
        [
          {"type": "TEXT", "content": "朝向"},
          {"type": "TEXT", "content": "[朝向信息]"},
          {"type": "TEXT", "content": "[朝向信息]"}
        ],
        [
          {"type": "TEXT", "content": "楼层"},
          {"type": "TEXT", "content": "[当前楼层]F/[总楼层]F"},
          {"type": "TEXT", "content": "[当前楼层]F/[总楼层]F"}
        ],
        [
          {"type": "TEXT", "content": "梯户"},
          {"type": "TEXT", "content": "[梯户配置]（[特点描述]）"},
          {"type": "TEXT", "content": "[梯户配置]（[特点描述]）"}
        ],
        [
          {"type": "TEXT", "content": "车位"},
          {"type": "TEXT", "content": "[车位情况]"},
          {"type": "TEXT", "content": "[车位情况]"}
        ],
        [
          {"type": "TEXT", "content": "装修"},
          {"type": "TEXT", "content": "[装修状况]（[保养情况]）"},
          {"type": "TEXT", "content": "[装修状况]（[保养情况]）"}
        ],
        [
          {"type": "TEXT", "content": "房本年限"},
          {"type": "TEXT", "content": "[产权年限]"},
          {"type": "TEXT", "content": "[产权年限]"}
        ]
      ]
    },
    {
      "serial": "1.2",
      "type": "TITLE",
      "style": "PARAGRAPH",
      "title": "对比结论"
    },
    {
      "serial": "1.2.1",
      "type": "LIST",
      "style": "BULLET",
      "content": [
        {
          "content": "价差逻辑：[分析价格差异的具体原因]"
        },
        {
          "content": "核心基础差异：[总结两个房源的主要差异点]"
        }
      ]
    },
    {
      "serial": "2",
      "type": "TITLE",
      "style": "SECTION",
      "title": "小区品质"
    },
    {
      "serial": "2.1",
      "type": "TABLE",
      "style": "NORMAL",
      "cols": [
        "小区核心参数",
        "[房源A小区名称]",
        "[房源B小区名称]"
      ],
      "content": [
        [
          {"type": "TEXT", "content": "容积率"},
          {"type": "TEXT", "content": "[数值]", "recommended": "[根据容积率优势设置true/false，低容积率更优]"},
          {"type": "TEXT", "content": "[数值]", "recommended": "[根据容积率优势设置true/false，低容积率更优]"}
        ],
        [
          {"type": "TEXT", "content": "小区实景"},
          {"type": "TEXT", "content": "-"},
          {"type": "TEXT", "content": "-"}
        ],
        [
          {"type": "TEXT", "content": "外立面"},
          {"type": "TEXT", "content": "[外立面材质和状况描述]", "recommended": "[根据外立面品质设置true/false]"},
          {"type": "TEXT", "content": "[外立面材质和状况描述]", "recommended": "[根据外立面品质设置true/false]"}
        ],
        [
          {"type": "TEXT", "content": "绿化"},
          {"type": "TEXT", "content": "[绿化率数值]", "recommended": "[根据绿化率优势设置true/false，高绿化率更优]"},
          {"type": "TEXT", "content": "[绿化率数值]", "recommended": "[根据绿化率优势设置true/false，高绿化率更优]"}
        ],
        [
          {"type": "TEXT", "content": "小区配套"},
          {"type": "TEXT", "content": "[人车分流情况，配套设施等]"},
          {"type": "TEXT", "content": "[人车分流情况，配套设施等]"}
        ],
        [
          {"type": "TEXT", "content": "物业服务"},
          {"type": "TEXT", "content": "[物业服务水平描述]"},
          {"type": "TEXT", "content": "[物业服务水平描述]"}
        ],
        [
          {"type": "TEXT", "content": "物业费"},
          {"type": "TEXT", "content": "[物业公司名称]，[费用]元/㎡/月"},
          {"type": "TEXT", "content": "[物业公司名称]，[费用]元/㎡/月"}
        ],
        [
          {"type": "TEXT", "content": "车位配比"},
          {"type": "TEXT", "content": "[配比数据]，[停车紧张程度]"},
          {"type": "TEXT", "content": "[配比数据]，[停车紧张程度]"}
        ],
        [
          {"type": "TEXT", "content": "停车费"},
          {"type": "TEXT", "content": "车位售价约[金额]万/个，月租[金额]元/月"},
          {"type": "TEXT", "content": "车位售价约[金额]万/个，月租[金额]元/月"}
        ]
      ]
    },
    {
      "serial": "2.2",
      "type": "TITLE",
      "style": "PARAGRAPH",
      "title": "实勘建议"
    },
    {
      "serial": "2.2.1",
      "type": "TEXT",
      "style": "BOARD",
      "content": "[基于小区品质对比的实地看房建议]"
    },
    {
      "serial": "3",
      "type": "TITLE",
      "style": "SECTION",
      "title": "板块能级"
    },
    {
      "serial": "3.1",
      "type": "TABLE",
      "style": "NORMAL",
      "cols": [
        "板块核心参数",
        "[房源A小区名称]",
        "[房源B小区名称]"
      ],
      "content": [
        [
          {"type": "TEXT", "content": "所属板块"},
          {"type": "TEXT", "content": "[板块名称]"},
          {"type": "TEXT", "content": "[板块名称]"}
        ],
        [
          {"type": "TEXT", "content": "地铁"},
          {"type": "TEXT", "content": "[地铁线路][站点名称]（[距离]m），[拥挤程度描述]", "recommended": "[根据地铁距离优势设置true/false，距离更近更优]"},
          {"type": "TEXT", "content": "[地铁线路][站点名称]（[距离]m），[拥挤程度描述]", "recommended": "[根据地铁距离优势设置true/false，距离更近更优]"}
        ],
        [
          {"type": "TEXT", "content": "拥堵情况"},
          {"type": "TEXT", "content": "[具体道路拥堵情况和通勤时间]", "recommended": "[根据拥堵情况设置true/false，拥堵少更优]"},
          {"type": "TEXT", "content": "[具体道路拥堵情况和通勤时间]", "recommended": "[根据拥堵情况设置true/false，拥堵少更优]"}
        ],
        [
          {"type": "TEXT", "content": "交通利好"},
          {"type": "TEXT", "content": "[在建或规划中的交通设施及影响]", "recommended": "[根据交通利好程度设置true/false]"},
          {"type": "TEXT", "content": "[在建或规划中的交通设施及影响]", "recommended": "[根据交通利好程度设置true/false]"}
        ],
        [
          {"type": "TEXT", "content": "大型商场"},
          {"type": "TEXT", "content": "[商场名称]（[距离]km）", "recommended": "[根据商场距离和品质设置true/false，距离更近更优]"},
          {"type": "TEXT", "content": "[商场名称]（[距离]m）", "recommended": "[根据商场距离和品质设置true/false，距离更近更优]"}
        ],
        [
          {"type": "TEXT", "content": "超市"},
          {"type": "TEXT", "content": "[超市名称]（[距离]m）", "recommended": "[根据超市距离和便利性设置true/false]"},
          {"type": "TEXT", "content": "[超市名称]（[距离]m）", "recommended": "[根据超市距离和便利性设置true/false]"}
        ],
        [
          {"type": "TEXT", "content": "学校"},
          {"type": "TEXT", "content": "[学校名称]（[性质] [距离]），[学区政策变化等]", "recommended": "[根据学校质量和距离设置true/false]"},
          {"type": "TEXT", "content": "[学校名称]（[性质] [距离]）", "recommended": "[根据学校质量和距离设置true/false]"}
        ],
        [
          {"type": "TEXT", "content": "医院"},
          {"type": "TEXT", "content": "[医院名称]（[距离]km，[等级]）"},
          {"type": "TEXT", "content": "[医院名称]（[距离]km，[等级]）"}
        ]
      ]
    },
    {
      "serial": "3.2",
      "type": "TITLE",
      "style": "PARAGRAPH",
      "title": "实勘建议"
    },
    {
      "serial": "3.2.1",
      "type": "TEXT",
      "style": "BOARD",
      "content": "[基于板块对比的选择建议]"
    },
    {
      "serial": "4",
      "type": "TITLE",
      "style": "SECTION",
      "title": "市场成交"
    },
    {
      "serial": "4.1",
      "type": "TITLE",
      "style": "PARAGRAPH",
      "title": "近一年所属板块成交趋势"
    },
    {
      "serial": "4.1.1",
      "type": "TEXT",
      "style": "NORMAL",
      "content": "单位：万元 / 平方米"
    },
    {
      "serial": "4.1.2",
      "type": "TEXT",
      "style": "NORMAL",
      "content": "本楼盘、同板块新房成交趋势随时间变化折线图（[时间范围]）"
    },
    {
      "serial": "4.2",
      "type": "TITLE",
      "style": "PARAGRAPH",
      "title": "近期小区成交趋势"
    },
    {
      "serial": "4.2.1",
      "type": "TABLE",
      "style": "NORMAL",
      "cols": [
        "历史成交/价格走势",
        "[房源A小区名称]（[面积]㎡户型）",
        "[房源B小区名称]（[面积]-[面积]㎡户型）"
      ],
      "content": [
        [
          {"type": "TEXT", "content": "成交均价"},
          {"type": "TEXT", "content": "[价格]元/㎡"},
          {"type": "TEXT", "content": "[价格]元/㎡"}
        ],
        [
          {"type": "TEXT", "content": "成交总价"},
          {"type": "TEXT", "content": "[价格区间]万元"},
          {"type": "TEXT", "content": "[价格区间]万元"}
        ],
        [
          {"type": "TEXT", "content": "挂牌均价"},
          {"type": "TEXT", "content": "[价格]元/㎡"},
          {"type": "TEXT", "content": "[价格]元/㎡"}
        ],
        [
          {"type": "TEXT", "content": "挂牌总价"},
          {"type": "TEXT", "content": "[价格区间]万元"},
          {"type": "TEXT", "content": "[价格区间]万元"}
        ],
        [
          {"type": "TEXT", "content": "目前在售"},
          {"type": "TEXT", "content": "[数量]套"},
          {"type": "TEXT", "content": "[数量]套"}
        ],
        [
          {"type": "TEXT", "content": "在售降价"},
          {"type": "TEXT", "content": "[数量]套"},
          {"type": "TEXT", "content": "[数量]套"}
        ],
        [
          {"type": "TEXT", "content": "在售涨价"},
          {"type": "TEXT", "content": "[数量]套"},
          {"type": "TEXT", "content": "/"}
        ],
        [
          {"type": "TEXT", "content": "近6月成交"},
          {"type": "TEXT", "content": "[数量]套（近6月成交套数，[价格区间]万/㎡、[面积区间]㎡）"},
          {"type": "TEXT", "content": "[数量]套（近6月成交套数，[价格区间]万/㎡、[面积区间]㎡）"}
        ]
      ]
    },
    {
      "serial": "4.3",
      "type": "TITLE",
      "style": "PARAGRAPH",
      "title": "置业专家建议"
    },
    {
      "serial": "4.3.1",
      "type": "TEXT",
      "style": "BOARD",
      "content": "[基于市场数据的购买时机分析]"
    },
    {
      "serial": "4.3.2",
      "type": "LIST",
      "style": "BULLET",
      "content": [
        {
          "content": "[房源A的市场分析和价格建议]"
        },
        {
          "content": "[房源B的市场分析和价格建议]"
        }
      ]
    },
    {
      "serial": "5",
      "type": "TITLE",
      "style": "SECTION",
      "title": "购买策略"
    },
    {
      "serial": "5.1",
      "type": "TABLE",
      "style": "NORMAL",
      "cols": [
        "",
        "[房源A小区名称]",
        "[房源B小区名称]"
      ],
      "content": [
        [
          {"type": "TEXT", "content": ""},
          {"type": "TEXT", "content": "[适合人群和使用场景描述]"},
          {"type": "TEXT", "content": "[适合人群和使用场景描述]"}
        ],
        [
          {"type": "TEXT", "content": ""},
          {"type": "TEXT", "content": "[核心优势和功能特点]"},
          {"type": "TEXT", "content": "[核心优势和功能特点]"}
        ],
        [
          {"type": "TEXT", "content": ""},
          {"type": "TEXT", "content": "[特殊需求适配性]"},
          {"type": "TEXT", "content": "[特殊需求适配性]"}
        ]
      ]
    },
    {
      "serial": "5.2",
      "type": "TITLE",
      "style": "PARAGRAPH",
      "title": "置业建议"
    },
    {
      "serial": "5.2.1",
      "type": "TEXT",
      "style": "BOARD",
      "content": "[具体的购买策略和谈判建议]"
    }
  ]
}
```

<----------------------------(cric_output)---------------------------->
=
# 大宁金茂府 vs 慧芝湖花园 房源对比评测报告

**数据来源**：房天下/链家/贝壳
**评测时间**：2025年7月

---

## 一、房源基础信息对比

| **对比维度** | **大宁金茂府**        | **慧芝湖花园**        |
|----------|------------------|------------------|
| **总价**   | 1,500万元          | 1,300万元          |
| **单价**   | 137,615元/㎡       | 127,451元/㎡       |
| **面积**   | 109㎡（3室2厅1卫）     | 102㎡（2室1厅1卫）     |
| **楼层**   | 8层/30层（低楼层）      | 12层/30层（中楼层）     |
| **朝向**   | 朝南               | 朝南               |
| **装修**   | 豪华装修             | 精装修（拎包入住，送家具家电）  |
| **房本年限** | 70年产权（2020年竣工）   | 70年产权（2019年竣工）   |
| **物业类型** | 商品房（中化金茂物业）      | 商品房（龙湖物业）        |
| **梯户比**  | 2梯4户             | 1梯2户             |
| **车位**   | 车位配比1:2          | 车位配比1:0.7        |
| **核心标签** | 降价好房、满五、景观房、采光充足 | 满五、南北通透、明厨明卫、近地铁 |
| **实景图**  | [查看7张实景图](#)     | [查看9张实景图](#)     |

**市场状态评估**：

- 大宁金茂府单价更高但户型更大，适合改善型需求；慧芝湖花园总价更低且配套成熟，适合刚需或过渡。
- 两房源均为"满五"房源，交易税费优势明显。

---

## 二、小区品质对比

| **对比维度** | **大宁金茂府**    | **慧芝湖花园**   |
|----------|--------------|-------------|
| **容积率**  | 2.5          | 2.8         |
| **外立面**  | 石材+玻璃幕墙      | 真石漆+部分铝板    |
| **绿化率**  | 35%          | 30%         |
| **物业服务** | 中化金茂物业（高端服务） | 龙湖物业（标准化服务） |
| **物业费**  | 8.5元/㎡/月     | 6.2元/㎡/月    |
| **小区配套** | 会所、恒温泳池、儿童乐园 | 社区商业、健身步道   |

**品质分析**：

- **大宁金茂府**定位高端，物业服务和硬件设施更优，但物业费高出37%。
- **慧芝湖花园**生活氛围更浓厚，步行3分钟可达百联购物中心，便利性突出。

---

## 三、板块能级对比

| **对比维度** | **大宁金茂府**                    | **慧芝湖花园**             |
|----------|------------------------------|-----------------------|
| **所属板块** | 大宁板块（静安区）                    | 凉城板块（静安区）             |
| **地铁**   | 1号线汶水路站（550m）                | 1号线马戏城站（433m）         |
| **学校**   | 大宁国际小学（127m）<br>风华初级中学（486m） | 大宁国际小学（254m）<br>无优质中学 |
| **医疗**   | 静安区中医医院（455m）                | 登特口腔（348m）            |
| **商业**   | 和记小菜（367m）                   | 百联莘荟购物中心（500m）        |
| **拥堵情况** | 共和新路早高峰拥堵                    | 广中路平型关路交叉口拥堵          |

**板块分析**：

- **教育优势**：大宁金茂府对口学校更优质，且距离更近（小学仅127米）。
- **商业便利**：慧芝湖花园临近百联购物中心（含盒马奥莱），日常采买更便捷。

---

## 四、市场成交情况

### 1. 挂牌与成交均价对比（元/㎡）

| **小区** | **当前挂牌价** | **近3月成交均价**      | **价差率** |
|--------|-----------|------------------|---------|
| 大宁金茂府  | 147,525   | 143,542（2024.10） | +2.77%  |
| 慧芝湖花园  | 105,689   | 96,591（2025.01）  | +9.42%  |

### 2. 月度成交趋势（2024.08-2025.07）

| **月度**   | **大宁金茂府成交套数**  | **慧芝湖花园成交套数** |
|----------|----------------|---------------|
| 2024年10月 | 1套（143,542元/㎡） | 1套（96,437元/㎡） |
| 2024年12月 | 1套（122,293元/㎡） | 6套（91,973元/㎡） |
| 2025年03月 | 1套（167,364元/㎡） | 2套（93,176元/㎡） |

**数据洞察**：

- 大宁金茂府成交价波动大（12月低价为特殊个案），高端属性明显；
- 慧芝湖花园成交量更稳定，但单价低于板块均值（大宁板块7月挂牌均价76,071元/㎡）。

---

## 五、置业专家建议

**核心对比结论**：

1. **改善优选**：大宁金茂府凭借金茂品牌溢价、3房设计和优质物业，适合预算充足且重视品质的改善家庭，需注意其挂牌价与成交价差距较小（2.77%），议价空间有限。
2. **刚需之选**：慧芝湖花园总价低130万，且附带精装修和家具家电，节省初期投入，但需接受2房设计的局限性，其近地铁和商业配套对通勤族友好。

**针对性建议**：

- **学区需求**：优先考虑大宁金茂府（对口市重点小学+初中双学区）；
- **投资潜力**：关注慧芝湖花园（单价低于板块均值，且19号线规划中）；
- **议价策略**：大宁金茂府3月成交价达167,364元/㎡，当前挂牌147,525元/㎡存在性价比窗口期。

**专家信息**：戴阳（执业证号147288）
**联系方式**：400-800-9090（上海添玑好房）

---
> 注：以上数据截止2025年7月，具体房源信息以实际勘验为准。市场有风险，交易需谨慎。

<----------------------------(broker_flow)---------------------------->
=

```json
${toJSONStr(request.broker)}
```

<----------------------------(broker)---------------------------->
=

```json
{
  "姓名": "于宝成",
  "头像": "https://fyoss-test.fangyou.com/jpg_2406131213538d9a39dc8eda370e5764171fea405170a807",
  "学历": "本科学历",
  "工作年限": "10年以上",
  "服务类型": "金融贷款专家、产权尽调、免费接送",
  "联系电话": "13123456789"
}
```
