<----------------------------(system_prompt)---------------------------->
=
你是一个专业的文档结构化转换专家，需要将房产评测类Markdown报告转换为标准化的JSON格式。转换过程必须严格遵循以下规则和约束。

**关键要求：输出必须是完全有效的JSON格式，不能包含任何JSON语法错误。**

## 核心转换原则

### 1. JSON模板权威性（最高优先级）

- **严格遵循JSON模板**：以提供的完整JSON模板为唯一转换标准和参考依据
- **模板优先原则**：当存在任何结构疑问时，严格按照JSON模板的结构和格式执行
- **填充式转换**：采用"填充模板"而非"构建结构"的转换思路
- **最小化偏离**：只在输入内容确实缺失对应章节时才省略模板中的相应部分

### 2. 数据完整性保证

- **严禁虚构数据**：只能基于输入的Markdown内容进行转换，不得添加任何原文中不存在的信息
- **保持数据准确性**：所有数值、文本、表格数据必须与原文完全一致
- **处理缺失章节**：如果某个章节在输入中不存在，则跳过该章节，不生成对应的JSON结构
- **禁止占位符内容**：严禁输出任何占位符性质的内容，如"XXX米"、"某某地铁站"、"相关信息"等模糊表述
- **禁止模板化表述**：不得使用模棱两可、含糊其辞的描述，所有内容必须基于实际输入数据的具体信息
- **宁缺毋滥原则**：如果输入信息源中没有相关具体内容，直接省略相应部分，不要生成空泛的描述

### 3. 动态章节适应

- **智能识别章节**：根据输入Markdown的实际章节结构进行转换
- **灵活序列编号**：根据实际存在的章节动态分配序列编号，保持连续性
- **章节映射规则**：
- 一级标题(#) → SECTION级别控件
- 二级标题(##) → PARAGRAPH级别控件
- 三级标题(###) → ENTRY级别控件
- 四级标题(####) → 更深层级控件

## 转换规则详解

### 控件类型选择规则

- **TITLE控件**：用于各级标题结构
- **TEXT控件**：用于段落文本内容
- **LIST控件**：用于列表结构
- **TABLE控件**：用于表格数据
- **CHART控件**：用于图表数据（优先于TABLE）
- **CARD控件**：用于结构化信息卡片

*具体的控件样式和字段定义请参考JSON结构定义部分*

### 图表数据处理规则

- **优先识别图表数据**：当表格包含数值型数据且适合可视化展示时，优先转换为CHART控件而非TABLE控件
- **CHART控件结构规则**：
- **BAR/LINE/MIXED图必须包含cols字段**
- **cols数组**：表示X轴标签（如时间点、分类名称），时间格式必须使用"yyyy/MM"格式
- **content[].title**：表示数据系列名称（如指标名称）
- **content[].content**：表示对应的数值数组
- **content[].chartType**：仅在style="MIXED"时需要指定，值为"BAR"或"LINE"
- **多维度图表优先**：当原始数据包含多个相关维度时，应将它们整合到同一个CHART控件中，而非拆分为多个简单图表
- **混合图表使用**：当需要在同一图表中同时展示柱状图和折线图数据时，使用MIXED样式
- **数据系列丰富性**：参考json报告_id12852.json的图表结构，尽可能包含更多相关数据系列以提供全面的数据视角
- **数值处理规则**：
- 数值≥10000时转换为万单位(除以10000)
- 保持数字类型，不包含"万"字符
- **强制单位标注**：所有数值相关的标题、字段名称必须明确标注单位信息
- **图表标题单位要求**：图表标题必须包含数值单位，如"成交均价(元/㎡)"、"成交套数(套)"、"成交面积(㎡)"
- **表格列标题单位要求**：表格列标题必须包含单位信息，如"挂牌均价(元/㎡)"、"新增套数(套)"
- **数据系列单位标注**：图表中每个数据系列的title必须包含单位，如"成交均价(元/㎡)"、"成交套数(套)"
- 同一图表内数值单位必须一致
- **null值处理**：原文中的"-"或空值转换为null

### 图片数据处理规则（重要）

**图片完整性要求**：
- **必须识别所有图片类别**：严格扫描输入数据中的所有图片分类，不得遗漏任何图片类别
- **必须输出所有图片**：对于每个识别到的图片类别，必须输出该类别下的所有图片，不得遗漏单个图片
- **完整扫描原则**：在处理图片数据前，必须完整扫描输入数据中的所有图片信息，包括但不限于：
  - layoutsSummaryMap中各户型的所有picUrl图片
  - picturesMap中各类别的所有图片
  - 房源实景图片等其他图片数据

**户型图片处理规则**：
- **识别所有户型类别**：必须识别layoutsSummaryMap中的所有户型类别（如1室1厅、1室2厅、2室1厅、2室2厅、3室1厅、3室2厅、4室1厅、4室2厅、4室3厅等）
- **输出所有户型图片**：对于每个户型类别，必须将该类别picUrl数组中的所有图片URL都输出到对应的images数组中
- **不得截取或省略**：严禁只输出每个户型类别的第一张图片，必须输出完整的图片列表

**小区景观图片处理规则**：
- **识别所有景观类别**：必须识别picturesMap中的所有图片类别（如景观带、远景、楼栋、出入口、入户门、分布图、其他、道路、停车场等）
- **输出所有景观图片**：对于每个景观类别，必须将该类别下的所有图片URL都输出到对应的images数组中
- **类别映射规则**：将原始数据中的图片类别合理映射到JSON模板的图片组中，确保不遗漏任何类别

**图片验证步骤**：
- **输出前验证**：在生成最终JSON前，必须验证是否已包含输入数据中的所有图片类别和所有图片
- **数量核对**：确认输出的图片总数与输入数据中的图片总数一致
- **类别核对**：确认输出的图片类别覆盖了输入数据中的所有图片类别

### 序列编号分配规则

- **编号层次结构**：
- 0级：文档标题(固定为"0")
- 1级：章节级内容("1","2","3"...)
- 1.1级：段落级内容("1.1","1.2"...)
- 1.1.1级：条目级内容("1.1.1"...)
- **动态编号原则**：
- 连续递增：同级编号必须连续，不得跳跃
- 章节适应：根据实际存在的章节动态分配编号
- 层级对应：编号深度与内容层级严格对应
- 顺序一致性：按照在Markdown中出现的顺序分配编号

## JSON结构定义参考

转换时请参考提供的JSON结构定义，根据实际输入内容动态生成对应的控件。

*具体的JSON结构定义将在用户提示词部分提供*

## 输出格式要求

### JSON结构模板参考

转换时请严格参考提供的标准JSON模板结构，根据实际输入内容动态生成对应的控件。

*具体的JSON模板将在用户提示词部分提供*

### 数据验证要求

- 所有必需字段必须存在
- 数值字段必须为纯数字类型，不能包含单位文字
- 枚举字段必须使用预定义值
- **JSON格式必须完全有效**：不能有语法错误、多余逗号、未转义字符、注释等
- 字符串必须用双引号包围，特殊字符必须正确转义
- 数组和对象的最后一个元素后不能有逗号
- 严格遵循模板结构，但根据实际内容动态调整

## 特殊处理说明

### 缺失章节处理

- 如果输入Markdown缺少某些标准章节，直接跳过
- 重新分配序列编号，保持连续性
- 不生成空的占位符控件
- **严禁生成模糊内容**：不得生成"距地铁站约XXX米"、"某某商场"、"相关配套"等占位符或模糊表述
- **基于实际数据**：只有当输入数据中包含具体、明确的信息时才生成对应内容
- **完全省略原则**：宁可完全省略某个控件，也不要生成含糊不清或占位符性质的内容

### 重复标题处理

- **识别重复**：检测父级控件和子级控件是否具有相同或高度相似的标题
- **处理策略**：
- 当父级TITLE控件和子级控件标题相同时，子级控件应省略title字段
- 或者为子级控件使用更具体的标题，避免与父级重复
- 优先保留父级标题，子级控件专注于内容展示

### 数据提取优先级

1. **图表数据优先**：数值型表格数据 → CHART控件（优先于TABLE控件）
2. 非数值表格数据 → TABLE控件
3. 列表结构 → LIST控件
4. 段落文本 → TEXT控件
5. 标题结构 → TITLE控件

## 质量检查清单

转换完成后，请确认：

- [ ] **模板一致性**：输出结构与JSON模板高度一致
- [ ] **JSON格式完全有效**：
  - 所有字符串用双引号包围
  - 数值为纯数字类型，不包含单位文字
  - 数组和对象最后一个元素后无多余逗号
  - 特殊字符正确转义
  - 无任何注释或非JSON内容
  - 能通过标准JSON解析器验证
- [ ] **序列编号正确**：所有serial编号连续且符合层级规则
- [ ] **数据准确性**：数值为数字类型，内容与原文一致
- [ ] **单位信息完整**：所有数值相关的标题、字段名称、数据系列名称都明确标注单位信息
- [ ] **图表单位标注**：图表标题和数据系列标题包含完整单位信息
- [ ] **表格单位标注**：表格列标题包含完整单位信息
- [ ] **图表优先**：数值型表格数据转换为CHART控件
- [ ] **避免重复标题**：父子级控件无相同标题
- [ ] **没有虚构信息**：所有内容都基于输入的Markdown内容
- [ ] **无占位符内容**：不包含"XXX米"、"某某地铁站"、"相关信息"等占位符
- [ ] **内容具体化**：所有描述都基于实际输入数据，无模糊或模板化表述
- [ ] **省略胜于模糊**：缺乏具体数据的部分已完全省略，未生成空泛内容
- [ ] **图片数据完整性**（重要检查项）：
  - **所有图片类别已识别**：确认已识别输入数据中的所有图片分类
  - **户型图片完整**：每个户型类别的所有picUrl图片都已输出
  - **景观图片完整**：picturesMap中所有类别的所有图片都已输出
  - **图片数量核对**：输出的图片总数与输入数据中的图片总数一致
  - **类别映射正确**：所有原始图片类别都已合理映射到输出结构中
  - **无图片遗漏**：没有遗漏任何单个图片或图片类别

<----------------------------(user_prompt)---------------------------->
=
请严格按照以上规则，将提供的Markdown房产评测报告转换为标准化的JSON格式。

### 重要提醒：JSON模板权威性是最高优先级要求

**严格遵循JSON模板结构！**
**以JSON模板为唯一转换标准！**
**采用填充式转换思路！**

### 转换执行要求

1. **严格遵循JSON模板**：以提供的JSON模板为唯一转换标准和参考依据
2. **填充式转换**：将输入内容填充到模板对应位置，不自由构建结构
3. **完全基于输入内容**：不添加任何虚构信息，只基于输入的Markdown内容
4. **动态省略**：仅在输入内容确实缺失时才省略模板中的相应部分
5. **参考JSON结构定义**：可参考JSON结构定义适当发挥，但以模板为准
6. **输出完全有效的JSON格式**：不包含任何解释性文字或代码块标记
7. **严禁占位符和模糊表述**：绝对不能输出"XXX米"、"某某地铁站"、"相关配套"等占位符内容
8. **具体数据优先**：只有当输入数据包含具体、明确信息时才生成对应内容，否则直接省略
9. **图片数据完整性要求**（关键要求）：
   - **必须完整扫描所有图片数据**：在开始转换前，完整扫描输入数据中的所有图片信息
   - **必须识别所有图片类别**：不得遗漏任何图片分类，包括户型图片和景观图片的所有类别
   - **必须输出所有图片**：对于每个图片类别，必须输出该类别下的所有图片，不得只输出部分图片
   - **严禁图片截取**：绝对不能只输出每个类别的第一张图片，必须输出完整的图片列表
   - **验证图片完整性**：输出前必须验证图片数据的完整性，确保无遗漏

### 参考模板

请严格参考以下JSON模板结构进行转换：

${json_template}

### JSON结构定义参考

可参考以下JSON结构定义进行适当发挥：
${json_structure_definition}

### 输入内容

以下是需要转换的Markdown报告内容：

```markdown
${cric_output}
```

**房源及其所属小区信息：**

${house}


### 输出要求

请基于提供的JSON模板和输入的Markdown内容，生成标准化的JSON结果。

**重要提醒**：

- **模板优先**：JSON模板是唯一转换标准，结构定义仅作参考
- **填充式转换**：将输入内容填充到模板对应位置
- **单位信息强制要求**：所有数值相关的标题、字段名称、数据系列名称必须明确包含单位信息
- **图表单位标注**：图表标题和数据系列标题必须包含完整单位，如"成交均价(元/㎡)"、"成交套数(套)"
- **表格单位标注**：表格列标题必须包含单位信息，如"挂牌均价(元/㎡)"、"新增套数(套)"
- **动态调整**：根据实际章节存在情况动态调整控件结构
- **保持连续性**：序列编号必须连续且符合逻辑
- **不得虚构**：不得添加模板中存在但输入内容中不存在的信息
- **严禁占位符**：绝对不能出现"XXX米"、"某某地铁站"、"相关信息"等占位符内容
- **具体化要求**：所有描述必须基于输入数据的具体信息，避免模棱两可的表述
- **省略优于模糊**：如果没有具体数据支撑，直接省略相应控件，不要生成空泛描述
- **图片数据完整性（最高优先级）**：
  - **完整扫描**：必须完整扫描输入数据中的layoutsSummaryMap和picturesMap，识别所有图片类别
  - **全量输出**：每个图片类别的所有图片URL都必须输出，不得遗漏任何图片
  - **户型图片**：layoutsSummaryMap中每个户型的picUrl数组中的所有图片都必须输出
  - **景观图片**：picturesMap中每个类别的所有图片都必须输出，合理映射到相册组中
  - **验证完整性**：输出前验证图片总数和类别数是否与输入数据一致

### JSON格式严格要求

**必须严格遵守以下JSON语法规则，确保输出结果是完全有效的JSON格式：**

1. **字符串格式**：
   - 所有字符串必须用双引号包围，不能使用单引号
   - 字符串内的双引号必须转义为 \"
   - 字符串内的反斜杠必须转义为 \\
   - 字符串内的换行符必须转义为 \n

2. **数值格式**：
   - 数值字段必须是纯数字，不能包含单位文字
   - 不能有前导零（除了0.xxx格式）
   - 使用null表示空值，不能使用undefined或空字符串

3. **数组和对象**：
   - 数组最后一个元素后不能有逗号
   - 对象最后一个属性后不能有逗号
   - 数组和对象的嵌套必须正确匹配

4. **注释禁止**：
   - JSON中不能包含任何形式的注释（//或/* */）
   - 不能包含JavaScript代码

5. **特殊字符处理**：
   - 文本中的特殊字符必须正确转义
   - HTML标签如<br/>在JSON字符串中是合法的，无需转义

6. **内容质量要求**：
   - 严禁任何占位符内容（如"XXX米"、"某某地铁站"、"相关信息"等）
   - 所有文本内容必须基于实际输入数据，具体明确
   - 不得使用模糊、含糊其辞的表述
   - 缺乏具体数据时直接省略相应字段或控件

7. **图片数据处理要求**：
   - **完整性检查**：处理图片数据前，必须完整扫描输入数据中的所有图片信息
   - **户型图片处理**：layoutsSummaryMap中每个户型类别的picUrl数组必须完整输出
   - **景观图片处理**：picturesMap中每个图片类别的所有图片必须完整输出
   - **类别映射**：将原始图片类别合理映射到JSON模板的相册组结构中
   - **数量验证**：确保输出的图片数量与输入数据中的图片数量一致

**输出验证要求**：
- 输出的JSON必须能通过标准JSON解析器验证
- 不得包含任何非JSON内容（如解释文字、代码块标记等）
- 确保所有括号、引号、逗号正确匹配
- 所有内容必须具体化，无占位符或模糊表述
- **图片完整性验证**：确认所有输入图片都已正确输出，无遗漏

### 图片处理执行指导

**在开始转换前，请按以下步骤处理图片数据：**

1. **第一步：完整扫描图片数据**
   - 扫描输入数据中的layoutsSummaryMap，识别所有户型类别及其picUrl数组
   - 扫描输入数据中的picturesMap，识别所有景观图片类别及其图片数组
   - 统计图片总数和类别总数

2. **第二步：户型图片处理**
   - 对于每个户型类别（如1室1厅、1室2厅、2室1厅等），将其picUrl数组中的所有图片URL输出到对应的户型相册组中
   - 确保每个户型类别的所有图片都被包含，不得只输出第一张图片

3. **第三步：景观图片处理**
   - 将picturesMap中的所有图片类别合理映射到小区景观相册的不同组中
   - 建议映射规则：
     - "景观带" → "园林景观"组
     - "楼栋"、"远景" → "建筑外观"组
     - "停车场"、"道路"、"出入口"、"入户门" → "公共设施"组
     - "分布图"、"其他" → 可单独成组或合并到相关组中
   - 确保每个类别的所有图片都被输出

4. **第四步：验证完整性**
   - 检查输出的图片总数是否与输入数据中的图片总数一致
   - 检查是否遗漏了任何图片类别
   - 确认每个类别的图片数量是否完整

**重要提醒：图片数据的完整性是本次修复的核心目标，必须确保无任何遗漏！**

开始转换，请直接输出完全有效的JSON结果。

<----------------------------(json_structure_definition)---------------------------->
=

## JSON控件结构定义

### 基础控件结构

```json
{
  "serial": "序列编号",
  "type": "控件类型",
  "style": "样式类型",
  "title": "控件标题(可选,移除加粗标记)"
}
```

### TITLE控件

```json
{
  "serial": "序列编号",
  "type": "TITLE",
  "style": "DOCUMENT|SECTION|PARAGRAPH|ENTRY",
  "title": "标题内容"
}
```

**样式说明**：

- **DOCUMENT**：文档主标题，通常用于serial="0"
- **SECTION**：章节标题，用于一级标题(#)
- **PARAGRAPH**：段落标题，用于二级标题(##)
- **ENTRY**：条目标题，用于三级标题(###)

### TEXT控件

```json
{
  "serial": "序列编号",
  "type": "TEXT",
  "style": "BOARD|NORMAL|WEAKEN",
  "title": "标题(可选)",
  "content": "文本内容"
}
```

**样式说明**：

- **BOARD**：重要文本内容，带边框显示
- **NORMAL**：普通文本内容
- **WEAKEN**：弱化文本内容，用于次要信息或补充说明的呈现

### LIST控件

```json
{
  "serial": "序列编号",
  "type": "LIST",
  "style": "BOARD|SUDOKU|BULLET|NUMBER",
  "title": "列表标题(可选)",
  "content": [
    {
      "title": "项目标题",
      "content": "项目内容",
      "emphasize": true|false
    }
  ]
}
```

**样式说明**：

- **BOARD**：重点强调，带边框显示
- **SUDOKU**：以九宫格方式呈现的项目
- **BULLET**：普通项目符号列表
- **NUMBER**：编号列表

**字段说明**：

- **title**：项目标题（可选）
- **content**：项目内容（必需）
- **emphasize**：高亮显示标识（可选），true表示需要高亮显示该项内容，false或不设置表示正常显示

### TABLE控件

```json
{
  "serial": "序列编号",
  "type": "TABLE",
  "style": "NORMAL",
  "title": "表格标题(可选)",
  "cols": [
    "列标题1",
    "列标题2"
  ],
  "content": [
    [
      {
        "type": "TEXT",
        "content": "单元格内容1"
      },
      {
        "type": "TEXT",
        "content": "单元格内容2",
        "recommended": true
      }
    ]
  ]
}
```

#### TableCell recommended属性应用规则

**适用场景**：对比性质表格中具有明显优势的数据项
- **使用标准**：价格优势、性能优势、配套优势、交通优势、数值最高/最低等明显优势
- **应用原则**：仅在真正具有明显优势的数据项上使用，推荐项不超过总数据项的30%
- **判断依据**：基于原始文档中的明确表述或数据对比结果
- **数值对比规则**：在数值对比表格中，最高价格、最大面积、最优性能等应标记为推荐

**房源对比中的应用场景**：
- **价格优势**：总价更低、单价更优、性价比更高的房源
- **面积优势**：建筑面积更大、使用面积更优的房源
- **配套优势**：地铁更近、学校更好、商业更便利的房源
- **品质优势**：装修更好、楼层更优、朝向更佳的房源
- **投资优势**：增值潜力更大、租售比更优的房源

**使用示例**：
```json
// 房源基础信息对比示例
[
  {"type": "TEXT", "content": "总价"},
  {"type": "TEXT", "content": "600万", "recommended": true},  // 价格更优
  {"type": "TEXT", "content": "800万"}  // 对比项
]

// 板块能级对比示例
[
  {"type": "TEXT", "content": "地铁距离"},
  {"type": "TEXT", "content": "245m", "recommended": true},  // 距离更近
  {"type": "TEXT", "content": "550m"}  // 对比项
]

// 小区品质对比示例
[
  {"type": "TEXT", "content": "容积率"},
  {"type": "TEXT", "content": "2.1"},
  {"type": "TEXT", "content": "1.8", "recommended": true}  // 容积率更低更优
]
```

**重要注意事项**：
- **第1列（对比维度）**：不设置recommended属性
- **第2列（房源A）**：根据分析结果设置true/false
- **第3列（房源B）**：根据分析结果设置true/false
- **推荐标准**：必须基于客观的数据对比和明确的优势判断

### CHART控件

```json
{
  "serial": "序列编号",
  "type": "CHART",
  "style": "PIE|BAR|LINE|MIXED",
  "title": "图表标题",
  "cols": [
    "X轴标签1",
    "X轴标签2"
  ],
  // PIE图不需要此字段
  "content": [
    {
      "title": "数据系列名称",
      "content": [
        数值1,
        数值2
      ],
      "chartType": "BAR|LINE" // 仅在style="MIXED"时需要指定
    }
  ]
}
```

**样式说明**：

- **PIE**：饼图，用于占比数据，不需要cols字段
- **BAR**：柱状图，用于对比数据，必须包含cols字段
- **LINE**：折线图，用于趋势数据，必须包含cols字段
- **MIXED**：混合图表，支持在同一图表中同时呈现柱状图和折线图，必须包含cols字段，且每个数据系列必须通过chartType属性指定其图表类型（"BAR"或"LINE"）

**日期格式说明**：
- X轴标签如果表示年月，必须使用"yyyy/MM"格式（例如："2024/01"）

### IMAGE控件

```json
{
  "serial": "序列编号",
  "type": "IMAGE",
  "style": "SINGLE|ALBUM",
  "title": "图片标题"
}
```

#### 单张图片模式（SINGLE）

```json
{
  "serial": "序列编号",
  "type": "IMAGE",
  "style": "SINGLE",
  "title": "图片标题",
  "content": {
    "url": "图片URL地址",
    "description": "图片描述(可选)"
  }
}
```

#### 相册模式（ALBUM）

```json
{
  "serial": "序列编号",
  "type": "IMAGE",
  "style": "ALBUM",
  "title": "相册标题",
  "content": {
    "defaultGroup": "默认展示的图片组名称(可选)",
    "groups": [
      {
        "title": "图片组标题",
        "images": [
          {
            "url": "图片URL地址",
            "description": "图片描述(可选)"
          }
        ]
      }
    ]
  }
}
```

**样式说明**：

- **SINGLE**：单张图片模式，直接展示一张图片
- **ALBUM**：相册模式，包含多个图片组，每个图片组具有标题和多张图片列表

**字段说明**：

- **url**：图片URL地址（必需）
- **description**：图片描述信息（可选）
- **defaultGroup**：默认展示的图片组名称（可选），用于指定相册模式下默认显示的图片组
- **groups**：图片组数组（相册模式必需）
- **title**：图片组标题（必需）
- **images**：图片列表（必需）
#### 基础结构
```json
{
  "serial": "3.1",
  "type": "CARD",
  "style": "BROKER|HOUSING|COMMUNITY",
  "title": "卡片标题",
  "fields": {
    // 根据样式类型确定具体字段
  }
}
```

#### BROKER卡片（经纪人）
```json
{
  "style": "BROKER",
  "fields": {
    "name": "姓名",
    "icon": "头像URL",
    "education": "学历",
    "experience": "服务年限",
    "serviceCategory": [
      "服务类别1",
      "服务类别2"
    ],
    "specialSkill": [
      "特色专长1",
      "特色专长1"
    ],
    "suggest": "投资建议",
    "wechat": "微信图片url",
    "phone": "联系电话"
  }
}
```

#### HOUSING卡片（房源）
```json
{
  "style": "HOUSING",
  "fields": {
    "layout": "户型",
    "area": "建筑面积",
    "floor": "楼层信息",
    "orientation": "朝向",
    "decoration": "装修状况",
    "totalPrice": "总价",
    "unitPrice": "单价",
    "propertyType": "房产类型"
  }
}
```

#### COMMUNITY卡片（小区）
```json
{
  "style": "COMMUNITY",
  "fields": {
    "buildYear": "建筑年代",
    "propertyCompany": "物业公司",
    "propertyFee": "物业费",
    "greenRate": "绿化率",
    "plotRatio": "容积率",
    "parkingSpaces": "停车位信息",
    "facilities": "主要配套设施"
  }
}
```

<----------------------------(json_template)---------------------------->
=

```json
{
  "type": "VALUE_EVALUATION",
  "title": "房产评测报告标题",
  "subject": "评测时间: [当前月份(例如2025年07月)]<br/>数据来源: 中国房地产决策咨询系统(CRIC)、市场公开数据<br/>免责申明:评测是基于CRIC中国房地产决策咨询系统和市场公开数据，通过AI算法和模型运算得出结果，仅供参考",
  "widgets": [
    {
      "serial": "1",
      "type": "TITLE",
      "style": "SECTION",
      "title": "报告基本信息"
    },
    {
      "serial": "1.1",
      "type": "LIST",
      "style": "SUDOKU",
      "content": [
        {
          "title": "城市",
          "content": "城市名称(如:上海市)"
        },
        {
          "title": "板块位置",
          "content": "[使用挂牌板块,不要带括号输出]"
        },
        {
          "title": "小区名称",
          "content": "小区名称(如:慧芝湖花园)"
        },
        {
          "title": "户型",
          "content": "户型信息(如:3室2厅2卫)"
        },
        {
          "title": "建筑面积",
          "content": "面积信息(如:110㎡)"
        },
        {
          "title": "朝向",
          "content": "朝向信息(如:朝南)"
        },
        {
          "title": "单价",
          "content": "单价信息(如:12.7万元/㎡)",
          "emphasize": true
        },
        {
          "title": "预估总价",
          "content": "[预估单价*建筑面积]万元",
          "emphasize": true
        },
        {
          "title": "物业公司",
          "content": "物业公司名称(如:龙湖物业)"
        }
      ]
    },
    {
      "serial": "1.2",
      "type": "TEXT",
      "style": "WEAKEN",
      "content": "*本估值不包含装修价值" //免责声明需固定内容输出,请勿修改提炼
    },
    {
      "serial": "1.3",
      "type": "IMAGE",
      "style": "ALBUM",
      "title": "房源图片相册",
      "content": {
        "groups": [
          {
            "title": "实景图",
            "images": [
              {
                "url": "房源实景图片URL",
                "description": "房源实景展示"
              }
            ]
          },
          {
            "title": "室内图",
            "images": [
              {
                "url": "室内图片URL",
                "description": "室内空间展示"
              }
            ]
          }
        ]
      }
    },
    {
      "serial": "2",
      "type": "TITLE",
      "style": "SECTION",
      "title": "小区基本信息分析"
    },
    {
      "serial": "2.1",
      "type": "TITLE",
      "style": "PARAGRAPH",
      "title": "小区户型分析(如:小区户型分析)"
    },
    {
      "serial": "2.1.0",
      "type": "IMAGE",
      "style": "ALBUM",
      "title": "小区户型相册",
      "content": {
        "defaultGroup": "2室户型",
        "groups": [
          {
            "title": "1室户型",
            "images": [
              {
                "url": "1室户型图片URL",
                "description": "1室户型平面图"
              }
            ]
          },
          {
            "title": "2室户型",
            "images": [
              {
                "url": "2室户型图片URL",
                "description": "2室户型平面图"
              }
            ]
          },
          {
            "title": "3室户型",
            "images": [
              {
                "url": "3室户型图片URL",
                "description": "3室户型平面图"
              }
            ]
          },
          {
            "title": "4室户型",
            "images": [
              {
                "url": "4室户型图片URL",
                "description": "4室户型平面图"
              }
            ]
          }
        ]
      }
    },
    {
      "serial": "2.1.1",
      "type": "TEXT",
      "style": "BOARD",
      "content": "户型分析文本内容(如:小区在售房源以3室户型为主，占比42.86%)"
    },
    {
      "serial": "2.1.2",
      "type": "CHART",
      "style": "MIXED",
      "title": "小区近12个月价格走势(如:小区近12个月价格走势)",
      "cols": ["2024/08", "2024/09", "2024/10", "2024/11", "2024/12", "2025/01", "2025/02", "2025/03", "2025/04", "2025/05", "2025/06", "2025/07"],
      "content": [
        {
          "title": "成交均价(元/㎡)",
          "content": ["数值1", "数值2", "数值3", "数值4", "数值5", "数值6", "数值7", "数值8", "数值9", "数值10", "数值11", "数值12"],
          "chartType": "BAR"
        },
        {
          "title": "挂牌均价(元/㎡)",
          "content": ["数值1", "数值2", "数值3", "数值4", "数值5", "数值6", "数值7", "数值8", "数值9", "数值10", "数值11", "数值12"],
          "chartType": "BAR"
        },
        {
          "title": "成交均价环比(%)",
          "content": ["数值1", "数值2", "数值3", "数值4", "数值5", "数值6", "数值7", "数值8", "数值9", "数值10", "数值11", "数值12"],
          "chartType": "LINE"
        }
      ]
    },
    {
      "serial": "2.1.3",
      "type": "CHART",
      "style": "BAR",
      "title": "小区近12个月成交量走势(如:小区近12个月成交量走势)",
      "cols": ["2024/08", "2024/09", "2024/10", "2024/11", "2024/12", "2025/01", "2025/02", "2025/03", "2025/04", "2025/05", "2025/06", "2025/07"],
      "content": [
        {
          "title": "成交套数(套)",
          "content": ["数值1", "数值2", "数值3", "数值4", "数值5", "数值6", "数值7", "数值8", "数值9", "数值10", "数值11", "数值12"]
        },
        {
          "title": "新增挂牌(套)",
          "content": ["数值1", "数值2", "数值3", "数值4", "数值5", "数值6", "数值7", "数值8", "数值9", "数值10", "数值11", "数值12"]
        }
      ]
    },
    {
      "serial": "2.1.4",
      "type": "LIST",
      "style": "BULLET",
      "title": "趋势分析",
      "content": [
        {
          "content": "趋势分析要点1(如:挂牌均价呈现波动上升趋势)"
        },
        {
          "content": "趋势分析要点2(如:2025年3月达到挂牌均价峰值)"
        },
        {
          "content": "趋势分析要点3(如:成交活跃期集中在某个时间段)"
        }
      ]
    },
    {
      "serial": "2.2",
      "type": "TITLE",
      "style": "PARAGRAPH",
      "title": "板块市场对比分析(如:板块市场对比分析)"
    },
    {
      "serial": "2.2.1",
      "type": "CHART",
      "style": "MIXED",
      "title": "板块近12个月价格走势(如:板块近12个月价格走势)",
      "cols": ["2024/08", "2024/09", "2024/10", "2024/11", "2024/12", "2025/01", "2025/02", "2025/03", "2025/04", "2025/05", "2025/06", "2025/07"],
      "content": [
        {
          "title": "成交均价(元/㎡)",
          "content": ["数值1", "数值2", "数值3", "数值4", "数值5", "数值6", "数值7", "数值8", "数值9", "数值10", "数值11", "数值12"],
          "chartType": "BAR"
        },
        {
          "title": "挂牌均价(元/㎡)",
          "content": ["数值1", "数值2", "数值3", "数值4", "数值5", "数值6", "数值7", "数值8", "数值9", "数值10", "数值11", "数值12"],
          "chartType": "BAR"
        },
        {
          "title": "成交均价环比(%)",
          "content": ["数值1", "数值2", "数值3", "数值4", "数值5", "数值6", "数值7", "数值8", "数值9", "数值10", "数值11", "数值12"],
          "chartType": "LINE"
        }
      ]
    },
    {
      "serial": "2.2.2",
      "type": "CHART",
      "style": "BAR",
      "title": "板块近12个月成交量走势(如:板块近12个月成交量走势)",
      "cols": ["2024/08", "2024/09", "2024/10", "2024/11", "2024/12", "2025/01", "2025/02", "2025/03", "2025/04", "2025/05", "2025/06", "2025/07"],
      "content": [
        {
          "title": "成交套数(套)",
          "content": ["数值1", "数值2", "数值3", "数值4", "数值5", "数值6", "数值7", "数值8", "数值9", "数值10", "数值11", "数值12"]
        },
        {
          "title": "新增挂牌(套)",
          "content": ["数值1", "数值2", "数值3", "数值4", "数值5", "数值6", "数值7", "数值8", "数值9", "数值10", "数值11", "数值12"]
        }
      ]
    },
    {
      "serial": "2.2.3",
      "type": "LIST",
      "style": "BOARD",
      "title": "板块对比分析",
      "content": [
        {
          "content": "板块对比分析要点1(如:小区挂牌均价显著高于板块平均水平)"
        },
        {
          "content": "板块对比分析要点2(如:小区成交均价波动情况分析)"
        },
        {
          "content": "板块对比分析要点3(如:板块成交高峰期分析)"
        }
      ]
    },
    {
      "serial": "3",
      "type": "TITLE",
      "style": "SECTION",
      "title": "区域价值"
    },
    {
      "serial": "3.1",
      "type": "TEXT",
      "style": "BOARD",
      "content": "区域价值描述文本(如:作为核心居住板块，坐拥优质资源，高绿化率营造宜居环境)"
    },
    {
      "serial": "3.2",
      "type": "LIST",
      "style": "BOARD",
      "title": "区域核心价值体现",
      "content": [
        {
          "title": "价值优势标题1(如:交通枢纽优势)",
          "content": "价值优势描述1(如:步行范围内覆盖地铁站与公交干线)"
        },
        {
          "title": "价值优势标题2(如:教育资源矩阵)",
          "content": "价值优势描述2(如:覆盖幼儿园至小学优质教育机构)"
        },
        {
          "title": "价值优势标题3(如:商业配套集群)",
          "content": "价值优势描述3(如:购物中心等商业体形成生活圈)"
        },
        {
          "title": "价值优势标题4(如:生态宜居品质)",
          "content": "价值优势描述4(如:低容积率与优质设计保障居住舒适度)"
        }
      ]
    },
    {
      "serial": "4",
      "type": "TITLE",
      "style": "SECTION",
      "title": "交通网络"
    },
    {
      "serial": "4.1",
      "type": "LIST",
      "style": "BULLET",
      "content": [
        {
          "content": "交通方式1描述(如:轨交动脉：距地铁站约XXX米，快速连接核心商圈)"
        },
        {
          "content": "交通方式2描述(如:公交覆盖：汇集多条公交线路，形成交通网络)"
        },
        {
          "content": "交通方式3描述(如:路网体系：多条主干道构成路网，便捷出行)"
        }
      ]
    },
    {
      "serial": "5",
      "type": "TITLE",
      "style": "SECTION",
      "title": "生活配套"
    },
    {
      "serial": "5.1",
      "type": "TITLE",
      "style": "PARAGRAPH",
      "title": "医疗旗舰(如:医疗旗舰)"
    },
    {
      "serial": "5.1.1",
      "type": "LIST",
      "style": "BULLET",
      "content": [
        {
          "content": "医疗机构1(如:登特口腔（348米）：专业口腔医疗机构)"
        },
        {
          "content": "医疗机构2(如:益丰大药房（166米）：24小时便民药房)"
        },
        {
          "content": "医疗机构3(如:赞瞳眼科诊所（500米）：专科眼科服务)"
        }
      ]
    },
    {
      "serial": "5.2",
      "type": "TITLE",
      "style": "PARAGRAPH",
      "title": "商业矩阵(如:商业矩阵)"
    },
    {
      "serial": "5.2.1",
      "type": "LIST",
      "style": "BULLET",
      "content": [
        {
          "content": "商业设施1(如:百联莘荟购物中心（500米）：4.5星评级综合体，内含知名品牌)"
        },
        {
          "content": "商业设施2(如:宝华现代城商业街（489米）：特色餐饮聚集地)"
        },
        {
          "content": "商业设施3(如:百果园（56米）：社区生鲜便利站)"
        }
      ]
    },
    {
      "serial": "5.3",
      "type": "TITLE",
      "style": "PARAGRAPH",
      "title": "休闲图鉴(如:休闲图鉴)"
    },
    {
      "serial": "5.3.1",
      "type": "LIST",
      "style": "BULLET",
      "content": [
        {
          "content": "休闲设施1(如:自然运动·普拉提（433米）：高端健身会所)"
        },
        {
          "content": "休闲设施2(如:星巴克（199米）：社区咖啡社交空间)"
        },
        {
          "content": "休闲设施3(如:和记小菜（308米）：4.6分评价的本帮菜餐厅)"
        }
      ]
    },
    {
      "serial": "6",
      "type": "TITLE",
      "style": "SECTION",
      "title": "教育资源"
    },
    {
      "serial": "6.1",
      "type": "TITLE",
      "style": "PARAGRAPH",
      "title": "全龄教育链(如:全龄教育链)"
    },
    {
      "serial": "6.1.1",
      "type": "LIST",
      "style": "BOARD",
      "content": [
        {
          "content": "教育机构1(如:大宁国际第二幼儿园（355米）：区级示范园)"
        },
        {
          "content": "教育机构2(如:上海市大宁国际小学（254米）：优质公办教育)"
        },
        {
          "content": "教育机构3(如:静安区大宁路小学（518米）：历史悠久的重点小学)"
        }
      ]
    },
    {
      "serial": "6.2",
      "type": "TITLE",
      "style": "PARAGRAPH",
      "title": "特色优势(如:特色优势)"
    },
    {
      "serial": "6.2.1",
      "type": "TEXT",
      "style": "BOARD",
      "content": "教育优势描述(如:形成优质教育圈，实现便利教育体验，适合家庭置业需求)"
    },
    {
      "serial": "7",
      "type": "TITLE",
      "style": "SECTION",
      "title": "小区品质"
    },
    {
      "serial": "7.0",
      "type": "IMAGE",
      "style": "ALBUM",
      "title": "小区景观相册",
      "content": {
        "groups": [
          {
            "title": "园林景观",
            "images": [
              {
                "url": "园林景观图片URL",
                "description": "小区园林绿化景观"
              }
            ]
          },
          {
            "title": "建筑外观",
            "images": [
              {
                "url": "建筑外观图片URL",
                "description": "小区建筑外观展示"
              }
            ]
          },
          {
            "title": "公共设施",
            "images": [
              {
                "url": "公共设施图片URL",
                "description": "小区公共设施展示"
              }
            ]
          }
        ]
      }
    },
    {
      "serial": "7.1",
      "type": "TITLE",
      "style": "PARAGRAPH",
      "title": "生态美学"
    },
    {
      "serial": "7.1.1",
      "type": "TEXT",
      "style": "BOARD",
      "content": "品质描述文本"
    },
    {
      "serial": "7.2",
      "type": "TITLE",
      "style": "PARAGRAPH",
      "title": "建筑特色"
    },
    {
      "serial": "7.2.1",
      "type": "LIST",
      "style": "BOARD",
      "content": [
        {
          "title": "",
          "content": "建筑特色1"
        },
        {
          "title": "",
          "content": "建筑特色2"
        }
      ]
    },
    {
      "serial": "7.3",
      "type": "TITLE",
      "style": "PARAGRAPH",
      "title": "服务标准"
    },
    {
      "serial": "7.3.1",
      "type": "TEXT",
      "style": "BOARD",
      "content": "龙湖物业提供星级服务，配备3494个停车位（车位比1:0.7），实行人车分流管理"
    },
    {
      "serial": "7.4",
      "type": "TITLE",
      "style": "PARAGRAPH",
      "title": "生活场景"
    },
    {
      "serial": "7.4.1",
      "type": "TEXT",
      "style": "BOARD",
      "content": "晨间可步行至星巴克享用早餐，下午在社区园林散步，晚间步行5分钟即达购物中心，完美演绎静安高品质生活范式"
    },
    {
      "serial": "7.4.2",
      "type": "CARD",
      "style": "BROKER",
      "title": "置业顾问推荐",
      "fields": {
        "name": "姓名",
        "icon": "头像URL",
        "education": "学历",
        "experience": "服务年限",
        "serviceCategory": [
          "服务类别1",
          "服务类别2"
        ],
        "specialSkill": [
          "特色专长1",
          "特色专长1"
        ],
        "suggest": "[根据报告信息,从经纪人视角给出消费者投资建议,限制50字以内,要专业/客观/中肯/有参考价值]",
        "wechat": "微信图片url",
        "phone": "联系电话"
      }
    }
  ]
}

```

<----------------------------(house_flow)---------------------------->
=
${toJSONStr(request.houseInfo)}

<----------------------------(house)---------------------------->
=
```json
{
  "buildingArea": 102.00,
  "type": "SALE",
  "id": 53504,
  "roomCount": 2,
  "priceUnit": 127450.98,
  "townName": "大宁路街道",
  "boutique": false,
  "community": {
    "busiName": "凉城",
    "cityCode": "310100",
    "latitude": 31.281544,
    "typeName": "商务住宅;住宅区;住宅小区",
    "cityId": 310100,
    "around":{
      "count": "450",
      "info": "OK",
      "infocode": "10000",
      "pois": [
        {
          "address": "平型关路1063号-2",
          "adname": "静安区",
          "biz_ext": {
            "cost": [],
            "rating": "4.4"
          },
          "biz_type": [],
          "childtype": [],
          "cityname": "上海市",
          "distance": "56",
          "id": "B0FFGHKE9V",
          "importance": [],
          "location": "121.458218,31.281372",
          "name": "百果园(慧芝湖花园店)",
          "parent": [],
          "photos": [
            {
              "title": [],
              "url": "http://store.is.autonavi.com/showpic/0f0c80b1cdb58c164714ea8bd4706b6b"
            },
            {
              "title": [],
              "url": "http://store.is.autonavi.com/showpic/db4c34e9dc606b7a1a30f4bfe16ff848"
            },
            {
              "title": [],
              "url": "http://store.is.autonavi.com/showpic/da9db36646b91f7989c3a57b5a71e06f"
            }
          ],
          "pname": "上海市",
          "poiweight": [],
          "shopid": [],
          "shopinfo": "0",
          "tel": "021-56426132",
          "type": "购物服务;综合市场;果品市场",
          "typecode": "060704"
        },
        {
          "address": "平型关路992-996号",
          "adname": "静安区",
          "biz_ext": {
            "cost": [],
            "rating": []
          },
          "biz_type": [],
          "childtype": [],
          "cityname": "上海市",
          "distance": "166",
          "id": "B0FFJACO2B",
          "importance": [],
          "location": "121.457970,31.280217",
          "name": "益丰大药房(平型关路店)",
          "parent": [],
          "photos": [
            {
              "title": [],
              "url": "http://store.is.autonavi.com/showpic/c4b5d5ee5ff78b6c9c20df2feb6ad3ee"
            }
          ],
          "pname": "上海市",
          "poiweight": [],
          "shopid": [],
          "shopinfo": "1",
          "tel": [],
          "type": "医疗保健服务;医药保健销售店;药房",
          "typecode": "090601"
        },
        {
          "address": "广中路765号771号嘉悦会1层",
          "adname": "静安区",
          "biz_ext": {
            "cost": [],
            "meal_ordering": "0",
            "open_time": "07:00-22:00",
            "opentime2": "07:00-22:00",
            "rating": "4.7"
          },
          "biz_type": "diner",
          "childtype": "320",
          "cityname": "上海市",
          "distance": "199",
          "id": "B0FFM45HIF",
          "importance": [],
          "location": "121.457972,31.279887",
          "name": "星巴克(上海慧芝湖店)",
          "parent": "B00157HWK9",
          "photos": [
            {
              "title": [],
              "url": "http://store.is.autonavi.com/showpic/ec3440dbf3d8f9de23da5c7a0cf58ad7"
            },
            {
              "title": [],
              "url": "http://store.is.autonavi.com/showpic/95ae8b820c5da7a43d84a54e82b099e1"
            },
            {
              "title": [],
              "url": "https://aos-comment.amap.com/B0FFM45HIF/comment/295fd814e58a83ea08948f05a8ea7a4d_2048_2048_80.jpg"
            }
          ],
          "pname": "上海市",
          "poiweight": [],
          "shopid": [],
          "shopinfo": "1",
          "tel": "021-60782939;021-66270020;4008206998",
          "type": "餐饮服务;咖啡厅;星巴克咖啡",
          "typecode": "050501"
        },
        {
          "address": "北宝兴路900号(北宝兴路灵石路)",
          "adname": "静安区",
          "biz_ext": {
            "cost": [],
            "rating": []
          },
          "biz_type": [],
          "childtype": [],
          "cityname": "上海市",
          "distance": "254",
          "id": "B00155G7IZ",
          "importance": [],
          "location": "121.460633,31.283192",
          "name": "上海市大宁国际小学",
          "parent": [],
          "photos": [
            {
              "title": [],
              "url": "http://store.is.autonavi.com/showpic/42e97b3c189b949ff531f968fc36907d"
            },
            {
              "title": [],
              "url": "http://store.is.autonavi.com/showpic/02aff77941b47d489145056d1508ffd6"
            }
          ],
          "pname": "上海市",
          "poiweight": [],
          "shopid": [],
          "shopinfo": "0",
          "tel": "021-66520303",
          "type": "科教文化服务;学校;小学",
          "typecode": "141203"
        },
        {
          "address": "107路;547路;767路;79路;858路;862路;912路;944路",
          "adname": "静安区",
          "biz_ext": {
            "cost": [],
            "rating": []
          },
          "biz_type": [],
          "childtype": [],
          "cityname": "上海市",
          "distance": "305",
          "id": "BV10025188",
          "importance": [],
          "location": "121.456897,31.279319",
          "name": "广中路平型关路(公交站)",
          "parent": [],
          "photos": [],
          "pname": "上海市",
          "poiweight": [],
          "shopid": [],
          "shopinfo": "2",
          "tel": [],
          "type": "交通设施服务;公交车站;公交车站相关",
          "typecode": "150700"
        },
        {
          "address": "灵石路217号5楼1500室,6楼1600-1613室",
          "adname": "静安区",
          "biz_ext": {
            "cost": "127.00",
            "meal_ordering": "0",
            "open_time": "11:00-14:00 17:00-21:00",
            "opentime2": "11:00-14:00；17:00-21:00",
            "rating": "4.6"
          },
          "biz_type": "diner",
          "childtype": "320",
          "cityname": "上海市",
          "distance": "308",
          "id": "B0FFGDLJ3K",
          "importance": [],
          "location": "121.456549,31.283560",
          "name": "和记小菜(大宁店)",
          "parent": "B0IKV5HDJV",
          "photos": [
            {
              "title": [],
              "url": "http://store.is.autonavi.com/showpic/511666e7f72267e245ab319cfc6abca0"
            },
            {
              "title": [],
              "url": "http://store.is.autonavi.com/showpic/63290b1c1d58c68d152099d2a2eda095"
            },
            {
              "title": [],
              "url": "http://store.is.autonavi.com/showpic/6b07de206e28b7ddf8bcedca17712c77"
            }
          ],
          "pname": "上海市",
          "poiweight": [],
          "shopid": [],
          "shopinfo": "0",
          "tel": "15800478409",
          "type": "餐饮服务;中餐厅;中餐厅",
          "typecode": "050100"
        },
        {
          "address": "广中路865号(近马戏城地铁站)",
          "adname": "静安区",
          "biz_ext": {
            "cost": [],
            "rating": []
          },
          "biz_type": [],
          "childtype": [],
          "cityname": "上海市",
          "distance": "348",
          "id": "B00156NTH9",
          "importance": [],
          "location": "121.455707,31.279827",
          "name": "登特口腔",
          "parent": [],
          "photos": [
            {
              "title": [],
              "url": "https://aos-comment.amap.com/B00156NTH9/comment/835abbe1349a47cdf4a4d4e69aa4e4f3_2048_2048_80.jpg"
            },
            {
              "title": [],
              "url": "https://aos-comment.amap.com/B00156NTH9/comment/8d9edc21dffef4328c9571949c75abcd_2048_2048_80.jpg"
            },
            {
              "title": [],
              "url": "http://store.is.autonavi.com/showpic/9a92ef1454da4616487f1e354dd76943"
            }
          ],
          "pname": "上海市",
          "poiweight": [],
          "shopid": [],
          "shopinfo": "2",
          "tel": "021-56350183;021-56350520",
          "type": "医疗保健服务;专科医院;口腔医院",
          "typecode": "090202"
        },
        {
          "address": "灵石路236号",
          "adname": "静安区",
          "biz_ext": {
            "cost": [],
            "rating": []
          },
          "biz_type": [],
          "childtype": [],
          "cityname": "上海市",
          "distance": "355",
          "id": "B00156NTHD",
          "importance": [],
          "location": "121.455405,31.282937",
          "name": "大宁国际第二幼儿园(灵石路)",
          "parent": [],
          "photos": [
            {
              "title": [],
              "url": "https://aos-comment.amap.com/B00156NTHD/comment/b420cb975b439f172603a3c403b0848b_2048_2048_80.jpg"
            },
            {
              "title": [],
              "url": "https://aos-comment.amap.com/B00156NTHD/comment/ae4225933fb11bd16e24f904eabf4cea_2048_2048_80.jpg"
            },
            {
              "title": [],
              "url": "https://aos-comment.amap.com/B00156NTHD/comment/2d1e89d173ed4eefd9a6b87dc4857d52_2048_2048_80.jpg"
            }
          ],
          "pname": "上海市",
          "poiweight": [],
          "shopid": [],
          "shopinfo": "0",
          "tel": "021-53931500",
          "type": "科教文化服务;学校;幼儿园",
          "typecode": "141204"
        },
        {
          "address": "广中路657号8幢",
          "adname": "虹口区",
          "biz_ext": {
            "cost": "94.00",
            "meal_ordering": "0",
            "open_time": "10:00-22:00",
            "opentime2": "【正常】全年10:00-22:00",
            "rating": "4.5"
          },
          "biz_type": "diner",
          "childtype": [],
          "cityname": "上海市",
          "distance": "376",
          "id": "B0FFK8AQY7",
          "importance": [],
          "location": "121.462425,31.280244",
          "name": "有家川菜(广中店)",
          "parent": [],
          "photos": [
            {
              "title": [],
              "url": "http://store.is.autonavi.com/showpic/3e655832f626597a566b1a8646de0f13"
            },
            {
              "title": [],
              "url": "http://store.is.autonavi.com/showpic/756c7e32cebbe47d8a2f467f4cb0a12b"
            },
            {
              "title": [],
              "url": "http://store.is.autonavi.com/showpic/686029845d4f21e7461e0d5596ea96e3"
            }
          ],
          "pname": "上海市",
          "poiweight": [],
          "shopid": [],
          "shopinfo": "0",
          "tel": "13691333783;16602161298",
          "type": "餐饮服务;餐饮相关场所;餐饮相关",
          "typecode": "050000"
        },
        {
          "address": "广中路909号(上海马戏城地铁站1号口步行220米)",
          "adname": "静安区",
          "biz_ext": {
            "cost": [],
            "rating": "4.5"
          },
          "biz_type": [],
          "childtype": [],
          "cityname": "上海市",
          "distance": "433",
          "id": "B0JACLVBU1",
          "importance": [],
          "location": "121.454650,31.279880",
          "name": "自然运动·普拉提(宝华店)",
          "parent": [],
          "photos": [
            {
              "title": [],
              "url": "http://store.is.autonavi.com/showpic/015f02ddfe5fcf032b46d9a20e790107"
            },
            {
              "title": [],
              "url": "http://store.is.autonavi.com/showpic/e665c12777211c760b2a4873a3123401"
            }
          ],
          "pname": "上海市",
          "poiweight": [],
          "shopid": [],
          "shopinfo": "1",
          "tel": "15021099828;15821160914",
          "type": "购物服务;体育用品店;体育用品店",
          "typecode": "060900"
        },
        {
          "address": "共和新路2395弄3-5号",
          "adname": "静安区",
          "biz_ext": {
            "cost": [],
            "rating": "4.7"
          },
          "biz_type": [],
          "childtype": "320",
          "cityname": "上海市",
          "distance": "489",
          "id": "B0FFLEVH1V",
          "importance": [],
          "location": "121.453994,31.279904",
          "name": "宝华现代城商业街(宝华现代城1期店)",
          "parent": "B0FFH1CPLA",
          "photos": [
            {
              "title": [],
              "url": "http://store.is.autonavi.com/showpic/42b6eeb7f855819469f745b4f98a2df6"
            }
          ],
          "pname": "上海市",
          "poiweight": [],
          "shopid": [],
          "shopinfo": "0",
          "tel": "021-66308108",
          "type": "购物服务;特色商业街;特色商业街",
          "typecode": "061000"
        },
        {
          "address": "北宝兴路624号17栋F201-202室",
          "adname": "静安区",
          "biz_ext": {
            "cost": [],
            "rating": []
          },
          "biz_type": [],
          "childtype": "202",
          "cityname": "上海市",
          "distance": "500",
          "id": "B0JB4HZSHP",
          "importance": [],
          "location": "121.461121,31.277523",
          "name": "上海赞瞳眼科诊所",
          "parent": "B0FFMGE5SZ",
          "photos": [
            {
              "title": [],
              "url": "http://store.is.autonavi.com/showpic/1e579fba8a44c7ebbc0529ab1c0f7fa2"
            },
            {
              "title": [],
              "url": "http://store.is.autonavi.com/showpic/aafbcb46fca9cbeef596047d9ded8552"
            }
          ],
          "pname": "上海市",
          "poiweight": [],
          "shopid": [],
          "shopinfo": "0",
          "tel": [],
          "type": "医疗保健服务;医疗保健服务场所;医疗保健服务场所",
          "typecode": "090000"
        },
        {
          "address": "北宝兴路624号百联莘荟购物中心F1层",
          "adname": "静安区",
          "biz_ext": {
            "cost": [],
            "rating": "4.6"
          },
          "biz_type": [],
          "childtype": "202",
          "cityname": "上海市",
          "distance": "500",
          "id": "B0JKZHNEJC",
          "importance": [],
          "location": "121.460966,31.277459",
          "name": "盒马奥莱(百联莘荟购物中心店)",
          "parent": "B0FFMGE5SZ",
          "photos": [
            {
              "title": [],
              "url": "https://aos-comment.amap.com/B0JKZHNEJC/comment/fd4883feb53d40a5b71ee093dddb93d7_2048_2048_80.jpg"
            },
            {
              "title": [],
              "url": "https://aos-comment.amap.com/B0JKZHNEJC/comment/caff43eb0910cf20ec94f8b7501f1b7a_2048_2048_80.jpg"
            },
            {
              "title": [],
              "url": "https://aos-comment.amap.com/B0JKZHNEJC/comment/0e574a3df537a5ecbf05bd2b3e0a2235_2048_2048_80.jpg"
            }
          ],
          "pname": "上海市",
          "poiweight": [],
          "shopid": [],
          "shopinfo": "0",
          "tel": [],
          "type": "购物服务;购物相关场所;购物相关场所",
          "typecode": "060000"
        },
        {
          "address": "北宝兴路624号",
          "adname": "静安区",
          "biz_ext": {
            "cost": [],
            "rating": "4.5"
          },
          "biz_type": [],
          "childtype": [],
          "cityname": "上海市",
          "distance": "500",
          "id": "B0FFMGE5SZ",
          "importance": [],
          "location": "121.460966,31.277459",
          "name": "百联莘荟购物中心",
          "parent": [],
          "photos": [
            {
              "title": [],
              "url": "https://aos-comment.amap.com/B0FFMGE5SZ/comment/c1bf91c4adc30b3db5fda16d9fd152b7_2048_2048_80.jpg"
            },
            {
              "title": [],
              "url": "https://aos-comment.amap.com/B0FFMGE5SZ/comment/c320b3ada7f185c3ae5558a4c23acbfb_2048_2048_80.jpg"
            },
            {
              "title": [],
              "url": "https://aos-comment.amap.com/B0FFMGE5SZ/comment/c9d5381a9fbe416154763a9cbb417fde_2048_2048_80.jpg"
            }
          ],
          "pname": "上海市",
          "poiweight": [],
          "shopid": [],
          "shopinfo": "0",
          "tel": [],
          "type": "购物服务;商场;购物中心|购物服务;商场;普通商场",
          "typecode": "060101|060102"
        },
        {
          "address": "北宝兴路624号",
          "adname": "静安区",
          "biz_ext": {
            "cost": [],
            "rating": "4.7"
          },
          "biz_type": [],
          "childtype": "202",
          "cityname": "上海市",
          "distance": "505",
          "id": "B0KGK5YS57",
          "importance": [],
          "location": "121.461010,31.277428",
          "name": "盒马NB(静安百联莘荟店)",
          "parent": "B0FFMGE5SZ",
          "photos": [
            {
              "title": [],
              "url": "https://aos-comment.amap.com/B0KGK5YS57/comment/3b80846cceedb9d47474f6144030792d_2048_2048_80.jpg"
            },
            {
              "title": [],
              "url": "https://aos-comment.amap.com/B0KGK5YS57/comment/a8a60569e6bda30f5d10da6ee6381e6d_2048_2048_80.jpg"
            },
            {
              "title": [],
              "url": "https://aos-comment.amap.com/B0KGK5YS57/comment/f55fbd54ea9c62c78accdf7c829c4a11_2048_2048_80.jpg"
            }
          ],
          "pname": "上海市",
          "poiweight": [],
          "shopid": [],
          "shopinfo": "1",
          "tel": [],
          "type": "购物服务;服装鞋帽皮具店;品牌服装店",
          "typecode": "061101"
        },
        {
          "address": "大宁路181弄15号(上海马戏城地铁站2号口步行380米)",
          "adname": "静安区",
          "biz_ext": {
            "cost": [],
            "rating": []
          },
          "biz_type": [],
          "childtype": [],
          "cityname": "上海市",
          "distance": "518",
          "id": "B00156OCZV",
          "importance": [],
          "location": "121.455184,31.278038",
          "name": "上海市静安区大宁路小学",
          "parent": [],
          "photos": [
            {
              "title": [],
              "url": "http://store.is.autonavi.com/showpic/8b66a1b7f0a8b5ccb51a9acad8132cc6"
            }
          ],
          "pname": "上海市",
          "poiweight": [],
          "shopid": [],
          "shopinfo": "0",
          "tel": "021-56383173",
          "type": "科教文化服务;学校;小学",
          "typecode": "141203"
        },
        {
          "address": "北宝兴路624号百联莘荟购物中心F1层",
          "adname": "静安区",
          "biz_ext": {
            "cost": "28.00",
            "meal_ordering": "0",
            "open_time": "09:00-22:30",
            "opentime2": "【正常】全年09:00-22:30",
            "rating": "4.6"
          },
          "biz_type": "diner",
          "childtype": "202",
          "cityname": "上海市",
          "distance": "532",
          "id": "B0H3LDJL0J",
          "importance": [],
          "location": "121.461607,31.277424",
          "name": "德克士(百联莘荟购物中心店)",
          "parent": "B0FFMGE5SZ",
          "photos": [
            {
              "title": [],
              "url": "http://store.is.autonavi.com/showpic/536aa53d85646ee609fac9d17cc0082d"
            },
            {
              "title": [],
              "url": "http://store.is.autonavi.com/showpic/dfcb84669e20877c8d4fe909bfe62ab3"
            },
            {
              "title": [],
              "url": "http://store.is.autonavi.com/showpic/90f7706a8c003224549f3756e5d3a263"
            }
          ],
          "pname": "上海市",
          "poiweight": [],
          "shopid": [],
          "shopinfo": "0",
          "tel": "15000773727;17269519800;17521702825",
          "type": "餐饮服务;快餐厅;快餐厅",
          "typecode": "050300"
        },
        {
          "address": "北宝兴路624号F101+F102室",
          "adname": "静安区",
          "biz_ext": {
            "cost": "28.00",
            "meal_ordering": "0",
            "open_time": "07:30-18:00 07:30-22:00",
            "opentime2": "【特殊】2022-01-31至2022-01-3107:30-18:00；【正常】全年07:30-22:00",
            "rating": "4.5"
          },
          "biz_type": "diner",
          "childtype": "202",
          "cityname": "上海市",
          "distance": "532",
          "id": "B0HA9N7DGB",
          "importance": [],
          "location": "121.461579,31.277400",
          "name": "Tims天好咖啡(百联莘荟购物中心店)",
          "parent": "B0FFMGE5SZ",
          "photos": [
            {
              "title": [],
              "url": "http://store.is.autonavi.com/showpic/4ea8efe554bcc09a280ed7b75ca97731"
            },
            {
              "title": [],
              "url": "http://store.is.autonavi.com/showpic/767b9cadc406ec79b618c89c1dffdcee"
            },
            {
              "title": [],
              "url": "http://store.is.autonavi.com/showpic/238545db89dbd650bc08e11306e86901"
            }
          ],
          "pname": "上海市",
          "poiweight": [],
          "shopid": [],
          "shopinfo": "1",
          "tel": "021-51556131",
          "type": "餐饮服务;咖啡厅;咖啡厅",
          "typecode": "050500"
        },
        {
          "address": "北宝兴路624号院内F独栋",
          "adname": "静安区",
          "biz_ext": {
            "cost": [],
            "meal_ordering": "0",
            "open_time": "11:00-24:00",
            "opentime2": "11:00-24:00",
            "rating": "4.7"
          },
          "biz_type": "diner",
          "childtype": "202",
          "cityname": "上海市",
          "distance": "542",
          "id": "B0I215OZEX",
          "importance": [],
          "location": "121.460847,31.277002",
          "name": "颐和国际公馆.江浙精致私房菜(百联莘荟购物中心店)",
          "parent": "B0FFMGE5SZ",
          "photos": [
            {
              "title": [],
              "url": "http://store.is.autonavi.com/showpic/894acb9f8ba29d52c8099a0164f55ea0"
            },
            {
              "title": [],
              "url": "https://aos-comment.amap.com/B0I215OZEX/comment/213c23ec727a85426072ebcf5be110da_2048_2048_80.jpg"
            },
            {
              "title": [],
              "url": "https://aos-comment.amap.com/B0I215OZEX/comment/c585d53850b8395a1949ecabd212fdc5_2048_2048_80.jpg"
            }
          ],
          "pname": "上海市",
          "poiweight": [],
          "shopid": [],
          "shopinfo": "1",
          "tel": "19370792117",
          "type": "餐饮服务;餐饮相关场所;餐饮相关",
          "typecode": "050000"
        },
        {
          "address": "共和新路2449号泛欧现代大厦1413室",
          "adname": "静安区",
          "biz_ext": {
            "cost": "3568.00",
            "rating": "4.8"
          },
          "biz_type": [],
          "childtype": "202",
          "cityname": "上海市",
          "distance": "587",
          "id": "B00156ZA8K",
          "importance": [],
          "location": "121.452704,31.280586",
          "name": "宫品海参(静安大宁专卖店)",
          "parent": "B00155FHSP",
          "photos": [
            {
              "title": [],
              "url": "http://store.is.autonavi.com/showpic/f9cbf1f4e501916d242c38f5fdd87624"
            },
            {
              "title": [],
              "url": "http://store.is.autonavi.com/showpic/784fb569005d8a738575fc03952cb953"
            },
            {
              "title": [],
              "url": "http://store.is.autonavi.com/showpic/33501e3683d5921147122a20d212fa96"
            }
          ],
          "pname": "上海市",
          "poiweight": [],
          "shopid": [],
          "shopinfo": "1",
          "tel": "021-61395665;15000731240",
          "type": "购物服务;综合市场;水产海鲜市场",
          "typecode": "060706"
        }
      ],
      "status": "1",
      "suggestion": {
        "cities": [],
        "keywords": []
      }
    },
    "cityName": "上海市",
    "formattedAddress": "上海市静安区大宁路街道慧芝湖花园",
    "detail": {
      "buildNum": 9,
      "blockName": "大宁",
      "powerdDesc": "民电",
      "setParkingFee": "300",
      "parkingNum": 3494,
      "gasDesc": "3元/m³",
      "parkingRate": "1.00:0.70",
      "propertyType": "住宅",
      "id": 129712,
      "propertyFee": "2.7元/月/㎡",
      "upParkingNum": 0,
      "propertyYears": "50/70",
      "heatingDesc": "自采暖",
      "downParkingNum": 0,
      "updateTime": "2025-01-09 15:11:45",
      "version": 2,
      "volumeRate": 2.50,
      "developerCorp": "嘉华(中国)投资有限公司",
      "greenRate": 0.45,
      "createTime": "2024-12-31 15:00:49",
      "propertyName": "龙湖物业",
      "blockCd": "310106106",
      "buildMaxYear": "2009",
      "commBelong": "商品房/使用权",
      "waterDesc": "民水",
      "propertyPhone": "021-66525123",
      "houseNum": 3526,
      "buildMinYear": "2004",
      "buildingType": "板楼"
    },
    "id": 1,
    "layoutsSummaryMap" : {
      "1室2厅" : {
        "roomCount" : 1,
        "balconyCount" : 2,
        "picUrl" : [ "https://oss-consumer.ebaas.com/community_layout/129712/09513af2-c135-45ea-b8d4-144c78d94c9e.jpg", "https://oss-consumer.ebaas.com/community_layout/129712/5f9ce615-2fd3-4d01-b95a-1be41ddcb495.jpg", "https://oss-consumer.ebaas.com/community_layout/129712/d697791a-32be-461f-97bf-807266a1b262.jpg", "https://oss-consumer.ebaas.com/community_layout/129712/d2276afc-e980-468c-bdd8-e3af192b18dd.jpg", "https://oss-consumer.ebaas.com/community_layout/129712/a33c37f1-25c1-4e45-bdce-790ca28dd702.jpg", "https://oss-consumer.ebaas.com/community_layout/129712/22630147-9fef-4447-87a8-411177125b75.jpg", "https://oss-consumer.ebaas.com/community_layout/129712/55743f0d-c688-4400-8797-32363b386391.jpg", "https://oss-consumer.ebaas.com/community_layout/129712/5eb5260e-c2a5-4808-b7d0-13695a0e774e.jpg", "https://oss-consumer.ebaas.com/community_layout/129712/cf69a04e-9234-4675-8434-87f8dde4fa5f.jpg", "https://oss-consumer.ebaas.com/community_layout/129712/e2dfc091-472f-464c-84be-3075377c4a33.jpg", "https://oss-consumer.ebaas.com/community_layout/129712/37779854-64b7-42e6-bcd7-458656857ecf.jpg", "https://oss-consumer.ebaas.com/community_layout/129712/ab966172-90cb-4116-b5f5-7af48e78fdc1.jpg" ],
        "areaMin" : 70.29,
        "areaMax" : 88.56,
        "setNum" : 12,
        "toiletCount" : 1,
        "name" : "1室2厅",
        "hallCount" : 2,
        "kitchenCount" : 1
      },
      "1室1厅" : {
        "roomCount" : 1,
        "balconyCount" : 2,
        "picUrl" : [ "https://oss-consumer.ebaas.com/community_layout/129712/c5abe500-df5f-43ae-9810-9a99cbf3931c.jpg", "https://oss-consumer.ebaas.com/community_layout/129712/90f2a195-1269-48a8-bdb7-3276dae69864.jpg", "https://oss-consumer.ebaas.com/community_layout/129712/2bedf1cb-7343-4a68-bb71-4f66c0a5fb72.jpg", "https://oss-consumer.ebaas.com/community_layout/129712/16021c5f-9c3b-4487-a118-be073e4fc6ec.jpg", "https://oss-consumer.ebaas.com/community_layout/129712/87e03e8a-9a87-41f4-b848-82626865b3ba.jpg", "https://oss-consumer.ebaas.com/community_layout/129712/994500f1-63b5-495c-9cdd-acca6fb8eff4.jpg" ],
        "areaMin" : 72.44,
        "areaMax" : 79.00,
        "setNum" : 6,
        "toiletCount" : 1,
        "name" : "1室1厅",
        "hallCount" : 1,
        "kitchenCount" : 1
      },
      "2室1厅" : {
        "roomCount" : 2,
        "balconyCount" : 0,
        "picUrl" : [ "https://oss-consumer.ebaas.com/community_layout/129712/baec009d-03fa-47df-8b06-f3c00871c0c9.jpg", "https://oss-consumer.ebaas.com/community_layout/129712/31900555-ea7f-4632-a826-be9c3b950e60.jpg", "https://oss-consumer.ebaas.com/community_layout/129712/704b445c-54a3-4bdf-8a89-e26d968f2d6b.jpg", "https://oss-consumer.ebaas.com/community_layout/129712/54a82bf9-c9b8-47c7-aa78-4f894631dac9.jpg", "https://oss-consumer.ebaas.com/community_layout/129712/d022237d-525d-4bfe-8e8e-a989c5edb8f8.jpg", "https://oss-consumer.ebaas.com/community_layout/129712/d33c326b-dbbb-45b4-852c-43c97c6b26c0.jpg", "https://oss-consumer.ebaas.com/community_layout/129712/c8845078-8090-47ee-8c6d-28c748e289c3.jpg", "https://oss-consumer.ebaas.com/community_layout/129712/54bc33a0-21f4-44fc-b8b3-7ccd77d9220f.jpg", "https://oss-consumer.ebaas.com/community_layout/129712/a4e35d74-ea48-4c10-a61e-ddb897fac06c.jpg", "https://oss-consumer.ebaas.com/community_layout/129712/93d825f5-b603-4f2d-9bb2-ad84a6a9b5c8.jpg", "https://oss-consumer.ebaas.com/community_layout/129712/1688059e-43f9-4b36-bb4d-97ccb459b0f9.jpg", "https://oss-consumer.ebaas.com/community_layout/129712/6188fcaa-165f-4252-af0d-2337b5e676cb.jpg", "https://oss-consumer.ebaas.com/community_layout/129712/ba8df067-ecab-4879-95be-1454375f57c9.jpg", "https://oss-consumer.ebaas.com/community_layout/129712/4aa5dc32-b24d-4307-8fe5-e59c5f251d05.jpg", "https://oss-consumer.ebaas.com/community_layout/129712/c17813b5-4d95-4887-a0fe-53bf0169e52b.jpg", "https://oss-consumer.ebaas.com/community_layout/129712/b6f77457-ba09-443a-97a0-7a4dc63292e0.jpg", "https://oss-consumer.ebaas.com/community_layout/129712/86227575-e2a6-4a7b-b2b0-5edb6f09aac1.jpg" ],
        "areaMin" : 73.04,
        "areaMax" : 107.00,
        "setNum" : 34,
        "toiletCount" : 1,
        "name" : "2室1厅",
        "hallCount" : 1,
        "kitchenCount" : 1
      },
      "2室2厅" : {
        "roomCount" : 2,
        "picUrl" : [ "https://oss-consumer.ebaas.com/community_layout/129712/5c25520e-a9ba-4fea-ba50-f114b0045eec.jpg", "https://oss-consumer.ebaas.com/community_layout/129712/c007286a-ee45-49b8-bec5-c55b067ff2f1.jpg", "https://oss-consumer.ebaas.com/community_layout/129712/4c184efa-17c4-41c5-95b1-f850a5c84b24.jpg", "https://oss-consumer.ebaas.com/community_layout/129712/224c4898-d821-4a39-a0b6-ec37f974bf57.jpg", "https://oss-consumer.ebaas.com/community_layout/129712/b37361d4-e2e2-45c0-abb7-53aaec0cea64.jpg", "https://oss-consumer.ebaas.com/community_layout/129712/a128c2d5-af73-42af-a4ed-962ea5a575dd.jpg", "https://oss-consumer.ebaas.com/community_layout/129712/17a654c7-516a-45f7-ba77-b7f6d7635a59.jpg", "https://oss-consumer.ebaas.com/community_layout/129712/68d77614-ca26-4246-b02b-afd4d3856ba6.jpg", "https://oss-consumer.ebaas.com/community_layout/129712/7ea5d175-4464-49a2-95d8-d81c71490b4f.jpg", "https://oss-consumer.ebaas.com/community_layout/129712/b90b8f74-2fcc-472c-95a1-cf1a3b2d3fd4.jpg", "https://oss-consumer.ebaas.com/community_layout/129712/f6343115-21bc-4860-a9fb-2a56bb8e9b8b.jpg", "https://oss-consumer.ebaas.com/community_layout/129712/9f4b5bf1-2897-4fc2-a0c0-1671664132ad.jpg", "https://oss-consumer.ebaas.com/community_layout/129712/e428e6d2-0f55-46dd-b11a-f5d2992a8b12.jpg", "https://oss-consumer.ebaas.com/community_layout/129712/5bb9938d-20d6-42dd-8626-535f98142a92.jpg", "https://oss-consumer.ebaas.com/community_layout/129712/d4cfc4ae-8577-4ded-b89e-d4395c19bdff.jpg", "https://oss-consumer.ebaas.com/community_layout/129712/cdc71dbf-85f2-436f-8d24-aa2039c46c91.jpg", "https://oss-consumer.ebaas.com/community_layout/129712/de847ef6-1f9e-45d9-a926-2993d69c231b.jpg", "https://oss-consumer.ebaas.com/community_layout/129712/d46e126c-d2fc-4c77-98d7-78011227e0a8.jpg", "https://oss-consumer.ebaas.com/community_layout/129712/1a83bf40-7ebb-4175-905f-e05ab13cc08d.jpg", "https://oss-consumer.ebaas.com/community_layout/129712/5de50209-8735-4378-8a2f-1fdd4d1664af.jpg", "https://oss-consumer.ebaas.com/community_layout/129712/116bc488-be3c-4869-bde9-6a3d92a818dc.jpg", "https://oss-consumer.ebaas.com/community_layout/129712/7e85149e-ae3e-43fa-ad7e-e66891b96feb.jpg", "https://oss-consumer.ebaas.com/community_layout/129712/29a1b504-85f4-4816-8d96-e1c45e4ef62f.jpg", "https://oss-consumer.ebaas.com/community_layout/129712/f8a34698-460e-4081-be76-ac4f6358783f.jpg", "https://oss-consumer.ebaas.com/community_layout/129712/fec5dc11-fbfb-48ae-bc7c-84c2739a5a4d.jpg", "https://oss-consumer.ebaas.com/community_layout/129712/9686fde7-ec68-4967-b3ed-13035342c4db.jpg" ],
        "areaMin" : 72.44,
        "areaMax" : 107.69,
        "setNum" : 52,
        "name" : "2室2厅",
        "hallCount" : 2
      },
      "3室1厅" : {
        "roomCount" : 3,
        "balconyCount" : 2,
        "picUrl" : [ "https://oss-consumer.ebaas.com/community_layout/129712/575d21f1-a006-4a94-a7c6-580e44526012.jpg", "https://oss-consumer.ebaas.com/community_layout/129712/78ca22c9-c7e0-458a-ad76-a26709cc5279.jpg", "https://oss-consumer.ebaas.com/community_layout/129712/9ea813cb-cb29-489e-b706-9ea009769041.jpg", "https://oss-consumer.ebaas.com/community_layout/129712/0cb94bb2-f993-42cc-835b-eaaf34a80b98.jpg", "https://oss-consumer.ebaas.com/community_layout/129712/f4710f84-e12b-4f0e-a7dc-47964d08a5c2.jpg", "https://oss-consumer.ebaas.com/community_layout/129712/d4dccfb1-4a01-4d40-b049-078cd495872e.jpg", "https://oss-consumer.ebaas.com/community_layout/129712/a50a3d97-1d9a-4d75-b723-66b868508064.jpg", "https://oss-consumer.ebaas.com/community_layout/129712/0399cf1d-a125-48c7-9786-09cc0b001644.jpg" ],
        "areaMin" : 80.98,
        "areaMax" : 154.10,
        "setNum" : 24,
        "toiletCount" : 2,
        "name" : "3室1厅",
        "hallCount" : 1,
        "kitchenCount" : 1
      },
      "3室2厅" : {
        "roomCount" : 3,
        "picUrl" : [ "https://oss-consumer.ebaas.com/community_layout/129712/75b1a0bc-0002-42e2-8f74-4444388048e2.jpg", "https://oss-consumer.ebaas.com/community_layout/129712/274d65d5-c83c-4c18-940b-3aa8274a889a.jpg", "https://oss-consumer.ebaas.com/community_layout/129712/9be33f94-6fc7-4e25-938d-f48ceabede2f.jpg", "https://oss-consumer.ebaas.com/community_layout/129712/08041c30-6243-4a2a-a074-edc7a491bd83.jpg", "https://oss-consumer.ebaas.com/community_layout/129712/35bd1042-5590-40e4-be80-121200c606fd.jpg", "https://oss-consumer.ebaas.com/community_layout/129712/8fa57921-6ae9-40ec-b908-1354ec8c96a1.jpg" ],
        "areaMin" : 88.35,
        "areaMax" : 109.77,
        "setNum" : 18,
        "name" : "3室2厅",
        "hallCount" : 2
      },
      "4室1厅" : {
        "roomCount" : 4,
        "balconyCount" : 1,
        "picUrl" : [ "https://oss-consumer.ebaas.com/community_layout/129712/63882253-ae12-4104-bcbc-9ec2b2568366.jpg" ],
        "areaMin" : 193.24,
        "areaMax" : 193.24,
        "setNum" : 4,
        "toiletCount" : 3,
        "name" : "4室1厅",
        "hallCount" : 1,
        "kitchenCount" : 1
      },
      "4室2厅" : {
        "roomCount" : 4,
        "picUrl" : [ "https://oss-consumer.ebaas.com/community_layout/129712/fddab51e-fdb1-42f8-9707-3bfab6830d8e.jpg", "https://oss-consumer.ebaas.com/community_layout/129712/69713ad8-471c-446b-82f0-d23a70dfb323.jpg", "https://oss-consumer.ebaas.com/community_layout/129712/4ac8187e-a9fc-480a-998c-1f5fee6af129.jpg", "https://oss-consumer.ebaas.com/community_layout/129712/c64aee10-36b7-441f-8fe9-9b658f6b411a.jpg", "https://oss-consumer.ebaas.com/community_layout/129712/b07ed0cd-fc36-40df-ad78-143ccc8684a9.jpg", "https://oss-consumer.ebaas.com/community_layout/129712/106eef48-0888-4f07-b308-895e92ad2e86.jpg", "https://oss-consumer.ebaas.com/community_layout/129712/ef87c00b-dbb8-422f-ad49-dca8bc7b43eb.jpg", "https://oss-consumer.ebaas.com/community_layout/129712/6c658490-a3c6-4998-9384-d5bb0e8f5192.jpg", "https://oss-consumer.ebaas.com/community_layout/129712/4dd843f6-7dfe-4d75-8fd1-9c87461fe2dd.jpg", "https://oss-consumer.ebaas.com/community_layout/129712/24d32dab-003c-4c45-873b-82c1753ff122.jpg" ],
        "areaMin" : 95.16,
        "areaMax" : 173.82,
        "setNum" : 40,
        "name" : "4室2厅",
        "hallCount" : 2
      },
      "4室3厅" : {
        "roomCount" : 4,
        "balconyCount" : 1,
        "picUrl" : [ "https://oss-consumer.ebaas.com/community_layout/129712/8e3ff767-b10e-441b-acd5-3cbd71781875.jpg" ],
        "areaMin" : 184.21,
        "areaMax" : 184.21,
        "setNum" : 4,
        "toiletCount" : 3,
        "name" : "4室3厅",
        "hallCount" : 3,
        "kitchenCount" : 1
      }
    },
    "picturesMap" : {
      "景观带" : [ "https://oss-consumer.ebaas.com/community_picture/129712/9a6a9e4c-910a-4c59-a23d-c7139c65d977.jpg" ],
      "远景" : [ "https://oss-consumer.ebaas.com/community_picture/129712/669a8c30-dd3e-47ec-8d84-8eb1270af886.jpg", "https://oss-consumer.ebaas.com/community_picture/129712/6e8cdf8e-0f8d-4ff3-ac90-5e2b43cd6993.jpg", "https://oss-consumer.ebaas.com/community_picture/129712/7568bea0-8562-46e7-918c-4030112f554b.jpg" ],
      "楼栋" : [ "https://oss-consumer.ebaas.com/community_picture/129712/61b50115-7f66-407e-84cc-4fe411328ba4.jpg", "https://oss-consumer.ebaas.com/community_picture/129712/41998c0d-0c8b-4166-b9ac-b88e5fabbec1.jpg", "https://oss-consumer.ebaas.com/community_picture/129712/8b1ebf63-1bd2-43cd-bef4-19be36028123.jpg", "https://oss-consumer.ebaas.com/community_picture/129712/84d3ed2d-cd25-4234-b216-712b8a55d9e9.jpg" ],
      "出入口" : [ "https://oss-consumer.ebaas.com/community_picture/129712/ffa6345b-9491-44d5-b89f-85030a77db01.jpg", "https://oss-consumer.ebaas.com/community_picture/129712/4e5ccd03-e53a-4947-8fa5-92377fb667de.jpg", "https://oss-consumer.ebaas.com/community_picture/129712/bf36c1dc-1d96-4b92-8e0a-9557faa8256a.jpg" ],
      "入户门" : [ "https://oss-consumer.ebaas.com/community_picture/129712/5002fc3d-6ac5-4159-a7ee-6462ab03ae0b.jpg", "https://oss-consumer.ebaas.com/community_picture/129712/21dcdab4-096a-4017-bdda-e70909762b09.jpg", "https://oss-consumer.ebaas.com/community_picture/129712/2b86547c-0a1e-4966-8420-b282d98c6c23.jpg" ],
      "分布图    " : [ "https://oss-consumer.ebaas.com/community_picture/129712/83c455ea-11dc-47d1-8202-5221a4406aa1.jpg" ],
      "其他" : [ "https://oss-consumer.ebaas.com/community_picture/129712/9374425c-dde1-4af3-8dcb-7fbaa8036bed.jpg" ],
      "道路" : [ "https://oss-consumer.ebaas.com/community_picture/129712/737a59b1-2db1-4297-8f58-e9573233aeee.jpg", "https://oss-consumer.ebaas.com/community_picture/129712/ff213483-ee1e-4e92-9c24-2c12820d4bb6.jpg", "https://oss-consumer.ebaas.com/community_picture/129712/1fe2ba85-8cf6-4db8-8516-609d0c0014ff.jpg", "https://oss-consumer.ebaas.com/community_picture/129712/e8a6de00-afa1-4ee0-b58c-a2e42f7f0b09.jpg", "https://oss-consumer.ebaas.com/community_picture/129712/e2644c9f-e70a-4cd0-a07b-cba60cc878b6.jpg" ],
      "停车场" : [ "https://oss-consumer.ebaas.com/community_picture/129712/6ffd978c-36a0-47db-895a-dd56938174f7.jpg", "https://oss-consumer.ebaas.com/community_picture/129712/fcd4718b-9600-4ecd-9b7c-298e472fe9d7.jpg" ]
    },
    "longitude": 121.458773,
    "townName": "大宁路街道",
    "address": "平型关路1083弄",
    "districtName": "静安区",
    "districtId": 310106,
    "name": "慧芝湖花园",
    "location": "121.458773,31.281544",
    "provinceName": "上海市"
  },
  "labels": [
    "满五",
    "精装修",
    "拎包入住",
    "南北通透",
    "明厨明卫",
    "送家具家电",
    "近地铁"
  ],
  "coverUrl": "https://etc-consumer.oss-cn-shanghai.aliyuncs.com/delegation-media/9094662523348608768/202507251030/c285b8de-2e63-49e8-afd6-71310753b216tmp_890416670c21d2d9f423e23c8f2a9d78.jpg",
  "lookType": "随时可看",
  "channelDelegationMap": {
    "ALIPAY": {
      "businessQuantity": 100,
      "outNickName": "特务小鹤",
      "freeze": false,
      "remainQuantity": 49,
      "status": "WITHOUT"
    },
    "XIANYU": {
      "businessQuantity": 0,
      "outNickName": "特务小鹤",
      "freeze": false,
      "remainQuantity": 0,
      "status": "WITHOUT"
    },
    "PRIVATE": {
      "code": "0f3efb16b60c472a9c34a884b8aa6602",
      "freeze": false,
      "remark": "",
      "id": 53505,
      "status": "UP_SUCC"
    }
  },
  "metro": "1",
  "toiletCount": 1,
  "hallCount": 1,
  "subType": "SALE_FULL",
  "currentFloor": 12,
  "brokerName": "戴阳",
  "status": "INIT",
  "listDate": "2025-07-25",
  "subTypeName": "二手整售",
  "typeName": "二手房",
  "communityAddress": "上海市静安区平型关路1083弄",
  "floorCategory": "中楼层",
  "priceTotal": 1300.00,
  "deadline": "2025-10-25 10:32:10",
  "parkingRatio": "1.00:0.70",
  "buildName": "1栋",
  "districtName": "静安区",
  "companyIdStr": "9095074090737248256",
  "broker": {
    "professionInformationCardUrl": "https://fyoss-test.fangyou.com/24122310acd217f1fc132c40bbc5be433a5d66620f019954.jpg",
    "code": "9094662523348608768",
    "name": "戴阳",
    "icon": "https://fyoss-test.fangyou.com/jpg_24110814a0ec060e52b6dcbdd8efa91ca2bbb35fee07fb54",
    "company": "esigntest上海添玑好房网络服务有限公司PABK",
    "storeName": "测试",
    "id": "9094662523348608768",
    "professionInformationCardNumber": "147288"
  },
  "mediasMap": {
    "实景图": [
      "https://etc-consumer.oss-cn-shanghai.aliyuncs.com/delegation-media/9094662523348608768/202507251030/c285b8de-2e63-49e8-afd6-71310753b216tmp_890416670c21d2d9f423e23c8f2a9d78.jpg",
      "https://etc-consumer.oss-cn-shanghai.aliyuncs.com/delegation-media/9094662523348608768/202507251030/182dc8d1-c7bb-4604-9aa4-7ebe48af61eetmp_347cc8a3fafd19b3c2a08d8124b9afca.jpg",
      "https://etc-consumer.oss-cn-shanghai.aliyuncs.com/delegation-media/9094662523348608768/202507251030/7f6356b5-5017-4ac3-9733-75308e93d42atmp_5c7324a937586c5db39763530c46a5c6.jpg",
      "https://etc-consumer.oss-cn-shanghai.aliyuncs.com/delegation-media/9094662523348608768/202507251030/38d5b074-34b5-44c5-b3b6-9c5813fd893atmp_b3e93d94bf03bc2c3f52b80a50828e33.jpg",
      "https://etc-consumer.oss-cn-shanghai.aliyuncs.com/delegation-media/9094662523348608768/202507251030/4057dbef-f90c-4821-a61c-e196b6985dd1tmp_f6d1fb7bde64cfd5b783dfb21c134821.jpg",
      "https://etc-consumer.oss-cn-shanghai.aliyuncs.com/delegation-media/9094662523348608768/202507251030/526454a8-24a6-4f3f-bd0d-eb052982f544tmp_6ced99dd822fbb1dee67600a68201618.jpg",
      "https://etc-consumer.oss-cn-shanghai.aliyuncs.com/delegation-media/9094662523348608768/202507251030/aff42de7-e290-4c53-8f02-e71e96dbc0cctmp_61949b2846a6249b154a88ccc7dbdcac.jpg",
      "https://etc-consumer.oss-cn-shanghai.aliyuncs.com/delegation-media/9094662523348608768/202507251030/6f7d3fd1-b3d7-4fb4-84d0-3b2a44a01631tmp_a801533d3cc9a6e219a6ea4ddcc19458.jpg",
      "https://etc-consumer.oss-cn-shanghai.aliyuncs.com/delegation-media/9094662523348608768/202507251030/1772536a-599e-451b-9a1f-7fca9e5754b0tmp_6daa74e6d9a0e679a31404a61369ef34.jpg"
    ]
  },
  "equipments": [],
  "houseCertVerify": true,
  "tagElevator": true,
  "brokerService": [],
  "busiName": "凉城",
  "layoutName": "2室1厅1卫",
  "completionTime": "2019",
  "roomNum": "101",
  "school": "有",
  "housePlanPurpose": "住宅",
  "communityName": "慧芝湖花园",
  "unitName": "1单元",
  "orient": "朝南",
  "level": "BROKER",
  "medias": [
    {
      "subtype": "实景图",
      "type": "IMAGE",
      "url": "https://etc-consumer.oss-cn-shanghai.aliyuncs.com/delegation-media/9094662523348608768/202507251030/c285b8de-2e63-49e8-afd6-71310753b216tmp_890416670c21d2d9f423e23c8f2a9d78.jpg"
    },
    {
      "subtype": "实景图",
      "type": "IMAGE",
      "url": "https://etc-consumer.oss-cn-shanghai.aliyuncs.com/delegation-media/9094662523348608768/202507251030/182dc8d1-c7bb-4604-9aa4-7ebe48af61eetmp_347cc8a3fafd19b3c2a08d8124b9afca.jpg"
    },
    {
      "subtype": "实景图",
      "type": "IMAGE",
      "url": "https://etc-consumer.oss-cn-shanghai.aliyuncs.com/delegation-media/9094662523348608768/202507251030/7f6356b5-5017-4ac3-9733-75308e93d42atmp_5c7324a937586c5db39763530c46a5c6.jpg"
    },
    {
      "subtype": "实景图",
      "type": "IMAGE",
      "url": "https://etc-consumer.oss-cn-shanghai.aliyuncs.com/delegation-media/9094662523348608768/202507251030/38d5b074-34b5-44c5-b3b6-9c5813fd893atmp_b3e93d94bf03bc2c3f52b80a50828e33.jpg"
    },
    {
      "subtype": "实景图",
      "type": "IMAGE",
      "url": "https://etc-consumer.oss-cn-shanghai.aliyuncs.com/delegation-media/9094662523348608768/202507251030/4057dbef-f90c-4821-a61c-e196b6985dd1tmp_f6d1fb7bde64cfd5b783dfb21c134821.jpg"
    },
    {
      "subtype": "实景图",
      "type": "IMAGE",
      "url": "https://etc-consumer.oss-cn-shanghai.aliyuncs.com/delegation-media/9094662523348608768/202507251030/526454a8-24a6-4f3f-bd0d-eb052982f544tmp_6ced99dd822fbb1dee67600a68201618.jpg"
    },
    {
      "subtype": "实景图",
      "type": "IMAGE",
      "url": "https://etc-consumer.oss-cn-shanghai.aliyuncs.com/delegation-media/9094662523348608768/202507251030/aff42de7-e290-4c53-8f02-e71e96dbc0cctmp_61949b2846a6249b154a88ccc7dbdcac.jpg"
    },
    {
      "subtype": "实景图",
      "type": "IMAGE",
      "url": "https://etc-consumer.oss-cn-shanghai.aliyuncs.com/delegation-media/9094662523348608768/202507251030/6f7d3fd1-b3d7-4fb4-84d0-3b2a44a01631tmp_a801533d3cc9a6e219a6ea4ddcc19458.jpg"
    },
    {
      "subtype": "实景图",
      "type": "IMAGE",
      "url": "https://etc-consumer.oss-cn-shanghai.aliyuncs.com/delegation-media/9094662523348608768/202507251030/1772536a-599e-451b-9a1f-7fca9e5754b0tmp_6daa74e6d9a0e679a31404a61369ef34.jpg"
    }
  ],
  "companyId": 9095074090737248256,
  "totalFloor": 30,
  "code": "07d081bd168945a5a1664e53fba04426",
  "remark": "",
  "redo": "豪华",
  "title": "慧芝湖花园 2室1厅1卫 102平 豪华",
  "around": [
    "公交",
    "学校",
    "医院",
    "购物",
    "餐饮"
  ],
  "cityName": "上海市",
  "propertyType": "住宅",
  "propertyYears": "70",
  "houseType": "商品房",
  "propertyManagementCompany": "龙湖物业"
}
```

<----------------------------(cric_output_flow)---------------------------->
=
${prevGenerateResponse}

<----------------------------(cric_output)---------------------------->
=
```json
{
  "avgPrice": null,
  "sellingData": {
    "在售房源户型占比": [
      {
        "新增挂牌套数(套)": 2,
        "挂牌均价(元/m²)": 108031,
        "新增挂牌面积(m²)": 259,
        "户型": "3室"
      },
      {
        "新增挂牌套数(套)": 2,
        "挂牌均价(元/m²)": 103667,
        "新增挂牌面积(m²)": 300,
        "户型": "4室"
      },
      {
        "新增挂牌套数(套)": 1,
        "挂牌均价(元/m²)": 104270,
        "新增挂牌面积(m²)": 89,
        "户型": "2室"
      }
    ],
    "所在小区近12个月走势": [
      {
        "项目所在板块": "大宁路街道",
        "新增挂牌套数(套)": 0,
        "成交均价环比(%)": -84.58,
        "月度": "2024年08月",
        "挂牌均价(元/m²)": 0,
        "新增挂牌面积(m²)": 0,
        "成交套数(套)": 1,
        "挂牌均价环比(%)": 0,
        "挂牌所在板块": "大宁板块",
        "成交面积(m²)": 34,
        "交易所在板块": "大宁板块",
        "成交均价(元/m²)": 17059
      },
      {
        "项目所在板块": "大宁路街道",
        "新增挂牌套数(套)": 0,
        "成交均价环比(%)": 0,
        "月度": "2024年09月",
        "挂牌均价(元/m²)": 0,
        "新增挂牌面积(m²)": 0,
        "成交套数(套)": 0,
        "挂牌均价环比(%)": 0,
        "挂牌所在板块": "大宁板块",
        "成交面积(m²)": 0,
        "交易所在板块": "大宁板块",
        "成交均价(元/m²)": 0
      },
      {
        "项目所在板块": "大宁路街道",
        "新增挂牌套数(套)": 3,
        "成交均价环比(%)": 0,
        "月度": "2024年10月",
        "挂牌均价(元/m²)": 100000,
        "新增挂牌面积(m²)": 417,
        "成交套数(套)": 1,
        "挂牌均价环比(%)": 0,
        "挂牌所在板块": "大宁板块",
        "成交面积(m²)": 87,
        "交易所在板块": "大宁板块",
        "成交均价(元/m²)": 96437
      },
      {
        "项目所在板块": "大宁路街道",
        "新增挂牌套数(套)": 5,
        "成交均价环比(%)": -5.51,
        "月度": "2024年11月",
        "挂牌均价(元/m²)": 106473,
        "新增挂牌面积(m²)": 482,
        "成交套数(套)": 3,
        "挂牌均价环比(%)": 6.47,
        "挂牌所在板块": "大宁板块",
        "成交面积(m²)": 357,
        "交易所在板块": "大宁板块",
        "成交均价(元/m²)": 91120
      },
      {
        "项目所在板块": "大宁路街道",
        "新增挂牌套数(套)": 7,
        "成交均价环比(%)": 0.94,
        "月度": "2024年12月",
        "挂牌均价(元/m²)": 105950,
        "新增挂牌面积(m²)": 763,
        "成交套数(套)": 6,
        "挂牌均价环比(%)": -0.49,
        "挂牌所在板块": "大宁板块",
        "成交面积(m²)": 556,
        "交易所在板块": "大宁板块",
        "成交均价(元/m²)": 91973
      },
      {
        "项目所在板块": "大宁路街道",
        "新增挂牌套数(套)": 2,
        "成交均价环比(%)": 5.02,
        "月度": "2025年01月",
        "挂牌均价(元/m²)": 102416,
        "新增挂牌面积(m²)": 178,
        "成交套数(套)": 1,
        "挂牌均价环比(%)": -3.34,
        "挂牌所在板块": "大宁板块",
        "成交面积(m²)": 88,
        "交易所在板块": "大宁板块",
        "成交均价(元/m²)": 96591
      },
      {
        "项目所在板块": "大宁路街道",
        "新增挂牌套数(套)": 7,
        "成交均价环比(%)": -23.49,
        "月度": "2025年02月",
        "挂牌均价(元/m²)": 101960,
        "新增挂牌面积(m²)": 903,
        "成交套数(套)": 2,
        "挂牌均价环比(%)": -0.45,
        "挂牌所在板块": "大宁板块",
        "成交面积(m²)": 123,
        "交易所在板块": "大宁板块",
        "成交均价(元/m²)": 73902
      },
      {
        "项目所在板块": "大宁路街道",
        "新增挂牌套数(套)": 10,
        "成交均价环比(%)": 26.08,
        "月度": "2025年03月",
        "挂牌均价(元/m²)": 109001,
        "新增挂牌面积(m²)": 1201,
        "成交套数(套)": 2,
        "挂牌均价环比(%)": 6.91,
        "挂牌所在板块": "大宁板块",
        "成交面积(m²)": 296,
        "交易所在板块": "大宁板块",
        "成交均价(元/m²)": 93176
      },
      {
        "项目所在板块": "大宁路街道",
        "新增挂牌套数(套)": 2,
        "成交均价环比(%)": 1.15,
        "月度": "2025年04月",
        "挂牌均价(元/m²)": 108324,
        "新增挂牌面积(m²)": 179,
        "成交套数(套)": 1,
        "挂牌均价环比(%)": -0.62,
        "挂牌所在板块": "大宁板块",
        "成交面积(m²)": 73,
        "交易所在板块": "大宁板块",
        "成交均价(元/m²)": 94247
      },
      {
        "项目所在板块": "大宁路街道",
        "新增挂牌套数(套)": 4,
        "成交均价环比(%)": -8.88,
        "月度": "2025年05月",
        "挂牌均价(元/m²)": 107222,
        "新增挂牌面积(m²)": 468,
        "成交套数(套)": 3,
        "挂牌均价环比(%)": -1.02,
        "挂牌所在板块": "大宁板块",
        "成交面积(m²)": 238,
        "交易所在板块": "大宁板块",
        "成交均价(元/m²)": 85882
      },
      {
        "项目所在板块": "大宁路街道",
        "新增挂牌套数(套)": 6,
        "成交均价环比(%)": 0,
        "月度": "2025年06月",
        "挂牌均价(元/m²)": 103070,
        "新增挂牌面积(m²)": 645,
        "成交套数(套)": 0,
        "挂牌均价环比(%)": -3.87,
        "挂牌所在板块": "大宁板块",
        "成交面积(m²)": 0,
        "交易所在板块": "大宁板块",
        "成交均价(元/m²)": 0
      },
      {
        "项目所在板块": "大宁路街道",
        "新增挂牌套数(套)": 5,
        "成交均价环比(%)": 0,
        "月度": "2025年07月",
        "挂牌均价(元/m²)": 105494,
        "新增挂牌面积(m²)": 648,
        "成交套数(套)": 0,
        "挂牌均价环比(%)": 0,
        "挂牌所在板块": "大宁板块",
        "成交面积(m²)": 0,
        "交易所在板块": "大宁板块",
        "成交均价(元/m²)": 0
      }
    ],
    "所在板块近12个月走势": [
      {
        "项目所在板块": "大宁路街道",
        "新增挂牌套数(套)": 153,
        "成交均价环比(%)": -13.15,
        "月度": "2024年08月",
        "挂牌均价(元/m²)": 78913,
        "新增挂牌面积(m²)": 12084,
        "成交套数(套)": 43,
        "挂牌均价环比(%)": -0.22,
        "挂牌所在板块": "大宁板块",
        "成交面积(m²)": 2909,
        "交易所在板块": "大宁板块",
        "成交均价(元/m²)": 63307
      },
      {
        "项目所在板块": "大宁路街道",
        "新增挂牌套数(套)": 173,
        "成交均价环比(%)": 5.99,
        "月度": "2024年09月",
        "挂牌均价(元/m²)": 82594,
        "新增挂牌面积(m²)": 14040,
        "成交套数(套)": 40,
        "挂牌均价环比(%)": 4.66,
        "挂牌所在板块": "大宁板块",
        "成交面积(m²)": 2737,
        "交易所在板块": "大宁板块",
        "成交均价(元/m²)": 67100
      },
      {
        "项目所在板块": "大宁路街道",
        "新增挂牌套数(套)": 203,
        "成交均价环比(%)": 3.93,
        "月度": "2024年10月",
        "挂牌均价(元/m²)": 82346,
        "新增挂牌面积(m²)": 17548,
        "成交套数(套)": 61,
        "挂牌均价环比(%)": -0.3,
        "挂牌所在板块": "大宁板块",
        "成交面积(m²)": 4517,
        "交易所在板块": "大宁板块",
        "成交均价(元/m²)": 69739
      },
      {
        "项目所在板块": "大宁路街道",
        "新增挂牌套数(套)": 191,
        "成交均价环比(%)": 1.14,
        "月度": "2024年11月",
        "挂牌均价(元/m²)": 82061,
        "新增挂牌面积(m²)": 16101,
        "成交套数(套)": 87,
        "挂牌均价环比(%)": -0.35,
        "挂牌所在板块": "大宁板块",
        "成交面积(m²)": 6471,
        "交易所在板块": "大宁板块",
        "成交均价(元/m²)": 70531
      },
      {
        "项目所在板块": "大宁路街道",
        "新增挂牌套数(套)": 175,
        "成交均价环比(%)": 1.06,
        "月度": "2024年12月",
        "挂牌均价(元/m²)": 80577,
        "新增挂牌面积(m²)": 13939,
        "成交套数(套)": 91,
        "挂牌均价环比(%)": -1.81,
        "挂牌所在板块": "大宁板块",
        "成交面积(m²)": 6903,
        "交易所在板块": "大宁板块",
        "成交均价(元/m²)": 71278
      },
      {
        "项目所在板块": "大宁路街道",
        "新增挂牌套数(套)": 90,
        "成交均价环比(%)": -1.04,
        "月度": "2025年01月",
        "挂牌均价(元/m²)": 77387,
        "新增挂牌面积(m²)": 7322,
        "成交套数(套)": 42,
        "挂牌均价环比(%)": -3.96,
        "挂牌所在板块": "大宁板块",
        "成交面积(m²)": 3211,
        "交易所在板块": "大宁板块",
        "成交均价(元/m²)": 70537
      },
      {
        "项目所在板块": "大宁路街道",
        "新增挂牌套数(套)": 217,
        "成交均价环比(%)": -9.88,
        "月度": "2025年02月",
        "挂牌均价(元/m²)": 80282,
        "新增挂牌面积(m²)": 18538,
        "成交套数(套)": 40,
        "挂牌均价环比(%)": 3.74,
        "挂牌所在板块": "大宁板块",
        "成交面积(m²)": 2543,
        "交易所在板块": "大宁板块",
        "成交均价(元/m²)": 63568
      },
      {
        "项目所在板块": "大宁路街道",
        "新增挂牌套数(套)": 227,
        "成交均价环比(%)": 10.2,
        "月度": "2025年03月",
        "挂牌均价(元/m²)": 81896,
        "新增挂牌面积(m²)": 19237,
        "成交套数(套)": 107,
        "挂牌均价环比(%)": 2.01,
        "挂牌所在板块": "大宁板块",
        "成交面积(m²)": 8221,
        "交易所在板块": "大宁板块",
        "成交均价(元/m²)": 70054
      },
      {
        "项目所在板块": "大宁路街道",
        "新增挂牌套数(套)": 173,
        "成交均价环比(%)": -9.11,
        "月度": "2025年04月",
        "挂牌均价(元/m²)": 78560,
        "新增挂牌面积(m²)": 14109,
        "成交套数(套)": 65,
        "挂牌均价环比(%)": -4.07,
        "挂牌所在板块": "大宁板块",
        "成交面积(m²)": 4236,
        "交易所在板块": "大宁板块",
        "成交均价(元/m²)": 63671
      },
      {
        "项目所在板块": "大宁路街道",
        "新增挂牌套数(套)": 190,
        "成交均价环比(%)": 5.34,
        "月度": "2025年05月",
        "挂牌均价(元/m²)": 79206,
        "新增挂牌面积(m²)": 15946,
        "成交套数(套)": 63,
        "挂牌均价环比(%)": 0.82,
        "挂牌所在板块": "大宁板块",
        "成交面积(m²)": 4421,
        "交易所在板块": "大宁板块",
        "成交均价(元/m²)": 67070
      },
      {
        "项目所在板块": "大宁路街道",
        "新增挂牌套数(套)": 172,
        "成交均价环比(%)": -4.89,
        "月度": "2025年06月",
        "挂牌均价(元/m²)": 78951,
        "新增挂牌面积(m²)": 15655,
        "成交套数(套)": 46,
        "挂牌均价环比(%)": -0.32,
        "挂牌所在板块": "大宁板块",
        "成交面积(m²)": 3379,
        "交易所在板块": "大宁板块",
        "成交均价(元/m²)": 63788
      },
      {
        "项目所在板块": "大宁路街道",
        "新增挂牌套数(套)": 185,
        "成交均价环比(%)": 0,
        "月度": "2025年07月",
        "挂牌均价(元/m²)": 75858,
        "新增挂牌面积(m²)": 16581,
        "成交套数(套)": 25,
        "挂牌均价环比(%)": 0,
        "挂牌所在板块": "大宁板块",
        "成交面积(m²)": 1942,
        "交易所在板块": "大宁板块",
        "成交均价(元/m²)": 56064
      }
    ]
  }
}
```