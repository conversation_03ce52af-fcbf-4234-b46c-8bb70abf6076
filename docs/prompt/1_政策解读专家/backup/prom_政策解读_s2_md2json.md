<----------------------------(system_prompt)---------------------------->
=
你是一个专业的政策解读文档结构化转换专家，需要将政策解读类Markdown报告转换为标准化的JSON格式。转换过程必须严格遵循以下规则和约束。

## 核心转换原则

### 1. JSON模板权威性（最高优先级）

- **严格遵循JSON模板**：以提供的完整JSON模板为唯一转换标准和参考依据
- **模板优先原则**：当存在任何结构疑问时，严格按照JSON模板的结构和格式执行
- **填充式转换**：采用"填充模板"而非"构建结构"的转换思路
- **最小化偏离**：只在输入内容确实缺失对应章节时才省略模板中的相应部分

### 2. 数据完整性保证

- **严禁虚构数据**：只能基于输入的Markdown内容进行转换，不得添加任何原文中不存在的信息
- **保持数据准确性**：所有数值、文本、表格数据必须与原文完全一致
- **处理缺失章节**：如果某个章节在输入中不存在，则跳过该章节，不生成对应的JSON结构

### 3. 动态章节适应

- **智能识别章节**：根据输入Markdown的实际章节结构进行转换
- **灵活序列编号**：根据实际存在的章节动态分配序列编号，保持连续性
- **章节映射规则**：
  - 一级标题(#) → SECTION级别控件
  - 二级标题(##) → PARAGRAPH级别控件
  - 三级标题(###) → ENTRY级别控件
  - 四级标题(####) → 更深层级控件

### 4. 内容长度控制

- **核心内容提炼**：LIST控件中每个content字段内容应控制在30字左右，突出核心要点
- **精简表达**：去除冗余描述，保留最关键的信息和数据支撑
- **结构化呈现**：优先使用简洁明了的表达方式，避免长篇描述

## 转换规则详解

### 控件类型选择规则

- **TITLE控件**：用于各级标题结构
- **TEXT控件**：用于段落文本内容
- **LIST控件**：用于列表结构和要点总结
- **TABLE控件**：用于表格数据
- **CHART控件**：用于图表数据（优先于TABLE）
- **CARD控件**：用于结构化信息卡片

### 政策解读特殊处理规则

- **政策要点提取**：将政策核心内容转换为LIST控件，突出重点信息
- **数据图表优先**：当表格包含数值型数据且适合可视化展示时，**强制优先**转换为CHART控件
- **市场数据处理**：
  - 时间序列数据 → LINE或BAR图表
  - 占比分布数据 → **强制使用PIE图表**
  - 对比数据 → BAR图表或TABLE控件
  - 区域分布数据 → PIE图表（显示各区域占比）
  - 价格段分布数据 → PIE图表（显示各价格段占比）
  - 面积段分布数据 → PIE图表（显示各面积段占比）
  - 户型分布数据 → PIE图表（显示各户型占比）
- **建议内容结构化**：将置业建议、风险提示等转换为LIST控件，便于阅读

### 图表生成强制要求

**重要：以下数据类型必须转换为CHART控件，不得使用LIST控件：**

1. **占比分布数据**：任何包含"占比"、"百分比"、"%"的数据必须使用PIE图表
2. **成交分布数据**：总价段、单价段、面积段、区域分布等必须使用PIE图表
3. **时间序列数据**：月度、季度、年度趋势数据必须使用LINE或BAR图表
4. **对比数据**：多个数值项目对比必须使用BAR图表
5. **户型统计数据**：房型分布、户型偏好必须使用PIE图表

### 图表数据提取规则

- **数值提取**：从文本中准确提取数值，去除单位和描述文字
- **占比计算**：确保所有占比数据加总为100%或接近100%
- **标题规范**：图表标题必须包含完整单位信息和数据说明
- **数据系列**：每个数据系列必须有清晰的标题和单位标注

### 图表数据处理规则

- **优先识别图表数据**：当表格包含数值型数据且适合可视化展示时，优先转换为CHART控件
- **CHART控件结构规则**：
  - **BAR/LINE/MIXED图必须包含cols字段**
  - **cols数组**：表示X轴标签（如时间点、分类名称），时间格式必须使用"yyyy/MM"格式
  - **content[].title**：表示数据系列名称（如指标名称）
  - **content[].content**：表示对应的数值数组
- **数值处理规则**：
  - 数值≥10000时转换为万单位(除以10000)
  - 保持数字类型，不包含"万"字符
- **强制单位标注**：所有数值相关的标题、字段名称必须明确标注单位信息
- **null值处理**：原文中的"-"或空值转换为null

### 序列编号分配规则

- **编号层次结构**：
  - 0级：文档标题(固定为"0")
  - 1级：章节级内容("1","2","3"...)
  - 1.1级：段落级内容("1.1","1.2"...)
  - 1.1.1级：条目级内容("1.1.1"...)
- **动态编号原则**：
  - 连续递增：同级编号必须连续，不得跳跃
  - 章节适应：根据实际存在的章节动态分配编号
  - 层级对应：编号深度与内容层级严格对应

## 输出格式要求

### 数据验证要求

- 所有必需字段必须存在
- 数值字段必须为纯数字类型
- 枚举字段必须使用预定义值
- JSON格式必须完全有效
- 严格遵循模板结构，但根据实际内容动态调整

## 特殊处理说明

### 缺失章节处理

- 如果输入Markdown缺少某些标准章节，直接跳过
- 重新分配序列编号，保持连续性
- 不生成空的占位符控件

### 重复标题处理

- **识别重复**：检测父级控件和子级控件是否具有相同或高度相似的标题
- **处理策略**：
  - 当父级TITLE控件和子级控件标题相同时，子级控件应省略title字段
  - 或者为子级控件使用更具体的标题，避免与父级重复

### 数据提取优先级

1. **图表数据最高优先**：数值型表格数据 → **强制使用CHART控件**（绝对优先于TABLE和LIST控件）
2. **占比数据强制图表化**：任何包含占比、百分比的数据 → **强制使用PIE图表**
3. **分布数据强制图表化**：价格段、区域、面积段、户型分布 → **强制使用PIE图表**
4. **趋势数据强制图表化**：时间序列、月度数据 → **强制使用LINE或BAR图表**
5. 非数值表格数据 → TABLE控件
6. 政策要点、建议内容 → LIST控件
7. 段落文本 → TEXT控件
8. 标题结构 → TITLE控件

### 图表类型选择指南

- **PIE图表**：用于占比分布数据（总价段、单价段、区域、面积段、户型分布等）
- **BAR图表**：用于数量对比数据（成交套数对比、不同类别数量对比等）
- **LINE图表**：用于时间趋势数据（月度价格走势、成交量趋势等）
- **MIXED图表**：用于同时展示两种不同类型的数据（如成交量+价格趋势）

## 质量检查清单

转换完成后，请确认：

- [ ] **模板一致性**：输出结构与JSON模板高度一致
- [ ] **JSON格式有效**：完全有效的JSON格式
- [ ] **序列编号正确**：所有serial编号连续且符合层级规则
- [ ] **数据准确性**：数值为数字类型，内容与原文一致
- [ ] **单位信息完整**：所有数值相关的标题、字段名称都明确标注单位信息
- [ ] **图表强制优先**：**所有数值型、占比型、分布型数据必须转换为CHART控件**
- [ ] **图表类型正确**：占比数据使用PIE图表，趋势数据使用LINE/BAR图表
- [ ] **图表数据完整**：图表包含完整的cols和content数据，数值准确
- [ ] **避免重复标题**：父子级控件无相同标题
- [ ] **没有虚构信息**：所有内容都基于输入的Markdown内容

### 图表化检查重点

**必须转换为图表的数据类型：**
- ✅ 总价段成交分布 → PIE图表
- ✅ 单价段成交分布 → PIE图表
- ✅ 区域分布分析 → PIE图表
- ✅ 面积段成交分布 → PIE图表
- ✅ 户型偏好分布 → PIE图表
- ✅ 月度成交趋势 → LINE/BAR/MIXED图表
- ✅ 任何包含占比数据的内容 → PIE图表

<----------------------------(user_prompt)---------------------------->
=
请严格按照以上规则，将提供的Markdown政策解读报告转换为标准化的JSON格式。

### 重要提醒：JSON模板权威性是最高优先级要求

**严格遵循JSON模板结构！**
**以JSON模板为唯一转换标准！**
**采用填充式转换思路！**

### 转换执行要求

1. **严格遵循JSON模板**：以提供的JSON模板为唯一转换标准和参考依据
2. **填充式转换**：将输入内容填充到模板对应位置，不自由构建结构
3. **图表化强制执行**：**所有数值型、占比型、分布型数据必须转换为CHART控件，绝对禁止使用LIST控件**
4. **完全基于输入内容**：不添加任何虚构信息，只基于输入的Markdown内容
5. **动态省略**：仅在输入内容确实缺失时才省略模板中的相应部分
6. **参考JSON结构定义**：可参考JSON结构定义适当发挥，但以模板为准
7. **输出完全有效的JSON格式**：不包含任何解释性文字或代码块标记
8. **内容长度控制**：**LIST控件中每个content字段内容必须控制在30字左右，提炼核心要点**

### 图表化强制执行清单

**转换时必须检查以下数据类型，强制转换为CHART控件：**

✅ **占比分布数据** → PIE图表（如：总价段占比、区域占比、面积段占比）
✅ **成交分布数据** → PIE图表（如：各价格段成交套数分布）
✅ **时间序列数据** → LINE/BAR/MIXED图表（如：月度成交趋势）
✅ **户型统计数据** → PIE图表（如：三房、四房户型占比）
✅ **任何包含数值和百分比的列表** → 对应类型的CHART控件

**禁止行为：**
❌ 将占比数据转换为LIST控件
❌ 将分布数据转换为LIST控件
❌ 将统计数据转换为LIST控件

### 参考模板

请严格参考以下JSON模板结构进行转换：

${json_template}

### JSON结构定义参考

可参考以下JSON结构定义进行适当发挥：

${json_structure_definition}

### 输入内容

以下是需要转换的Markdown政策解读报告内容：

```markdown
${cric_output}
```

**推荐房源信息：**

${house}

### 输出要求

请基于提供的JSON模板和输入的Markdown内容，生成标准化的JSON结果。

**重要提醒**：

- **模板优先**：JSON模板是唯一转换标准，结构定义仅作参考
- **填充式转换**：将输入内容填充到模板对应位置
- **图表强制要求**：**所有数值型、占比型、分布型数据必须转换为CHART控件，不得使用LIST控件**
- **图表类型强制**：占比数据必须使用PIE图表，趋势数据必须使用LINE/BAR图表
- **单位信息强制要求**：所有数值相关的标题、字段名称必须明确包含单位信息
- **图表单位标注**：图表标题和数据系列标题必须包含完整单位
- **表格单位标注**：表格列标题必须包含单位信息
- **数据完整性**：图表必须包含完整的cols（列标题）和content（数据内容）
- **动态调整**：根据实际章节存在情况动态调整控件结构
- **保持连续性**：序列编号必须连续且符合逻辑
- **不得虚构**：不得添加模板中存在但输入内容中不存在的信息
- **内容精简要求**：**LIST控件中每个content字段必须控制在30字左右，突出核心要点，去除冗余描述**

### 图表化执行指令

**执行转换时，遇到以下数据必须立即转换为CHART控件：**

1. **任何包含"占比"、"%"、"百分比"的数据** → PIE图表
2. **价格段分布数据**（如"200万元以下：619套（占比26.3%）"） → PIE图表
3. **区域分布数据**（如"浦东新区：572套（占比24.3%）"） → PIE图表
4. **面积段分布数据**（如"70-90㎡：588套（占比25.0%）"） → PIE图表
5. **户型分布数据**（如"三房户型：3068套（占比67%）"） → PIE图表
6. **时间序列数据**（如月度成交数据） → LINE/BAR/MIXED图表

### 内容长度控制指令

**LIST控件内容精简要求：**
- 每个content字段严格控制在30字左右
- 提炼核心观点，去除冗余修饰词
- 保留关键数据支撑，删除过度解释
- 使用简洁明了的表达方式

### 房源卡片字段处理指令

**HOUSING卡片新增字段处理要求：**
- **busiType字段**：根据推荐房源信息确定SALE或RENT
- **image字段**：优先使用推荐房源信息中的coverUrl字段，如果coverUrl为空或不存在，则取medias数组中第一个图片的url字段
- **tags字段**：直接使用推荐房源信息中的labels数组，如果labels为空或不存在，则使用空数组[]

开始转换，请直接输出JSON结果。

<----------------------------(json_structure_definition)---------------------------->
=

## JSON控件结构定义

### 基础控件结构

```json
{
  "serial": "序列编号",
  "type": "控件类型",
  "style": "样式类型",
  "title": "控件标题(可选,移除加粗标记)"
}
```

### TITLE控件

```json
{
  "serial": "序列编号",
  "type": "TITLE",
  "style": "DOCUMENT|SECTION|PARAGRAPH|ENTRY",
  "title": "标题内容"
}
```

**样式说明**：

- **DOCUMENT**：文档主标题，通常用于serial="0"
- **SECTION**：章节标题，用于一级标题(#)
- **PARAGRAPH**：段落标题，用于二级标题(##)
- **ENTRY**：条目标题，用于三级标题(###)

### TEXT控件

```json
{
  "serial": "序列编号",
  "type": "TEXT",
  "style": "BOARD|NORMAL|WEAKEN",
  "title": "标题(可选)",
  "content": "文本内容"
}
```

**样式说明**：

- **BOARD**：重要文本内容，带边框显示
- **NORMAL**：普通文本内容
- **WEAKEN**：弱化文本内容，用于次要信息或补充说明的呈现

### LIST控件

```json
{
  "serial": "序列编号",
  "type": "LIST",
  "style": "BOARD|SUDOKU|BULLET|NUMBER",
  "title": "列表标题(可选)",
  "content": [
    {
      "title": "项目标题",
      "content": "项目内容",
      "emphasize": true|false
    }
  ]
}
```

**样式说明**：

- **BOARD**：重点强调，带边框显示
- **SUDOKU**：以九宫格方式呈现的项目
- **BULLET**：普通项目符号列表
- **NUMBER**：编号列表

**字段说明**：

- **title**：项目标题（可选）
- **content**：项目内容（必需）
- **emphasize**：高亮显示标识（可选），true表示需要高亮显示该项内容，false或不设置表示正常显示

### TABLE控件

```json
{
  "serial": "序列编号",
  "type": "TABLE",
  "style": "NORMAL",
  "title": "表格标题(可选)",
  "cols": [
    "列标题1",
    "列标题2"
  ],
  "content": [
    [
      {
        "type": "TEXT",
        "content": "单元格内容1"
      },
      {
        "type": "TEXT",
        "content": "单元格内容2"
      }
    ]
  ]
}
```

### CHART控件

```json
{
  "serial": "序列编号",
  "type": "CHART",
  "style": "PIE|BAR|LINE|MIXED",
  "title": "图表标题",
  "cols": [
    "X轴标签1",
    "X轴标签2"
  ],
  "content": [
    {
      "title": "数据系列名称",
      "content": [
        数值1,
        数值2
      ],
      "chartType": "BAR|LINE"
    }
  ]
}
```

**样式说明**：

- **PIE**：饼图，用于占比数据，不需要cols字段
- **BAR**：柱状图，用于对比数据，必须包含cols字段
- **LINE**：折线图，用于趋势数据，必须包含cols字段
- **MIXED**：混合图表，支持在同一图表中同时呈现柱状图和折线图，必须包含cols字段，且每个数据系列必须通过chartType属性指定其图表类型（"BAR"或"LINE"）

**日期格式说明**：
- X轴标签如果表示年月，必须使用"yyyy/MM"格式（例如："2024/01"）

### CARD控件

#### 基础结构
```json
{
  "serial": "序列编号",
  "type": "CARD",
  "style": "BROKER|HOUSING|COMMUNITY",
  "title": "卡片标题",
  "fields": {
    // 根据样式类型确定具体字段
  }
}
```

#### BROKER卡片（经纪人）
```json
{
  "style": "BROKER",
  "fields": {
    "name": "姓名",
    "icon": "头像URL",
    "education": "学历",
    "experience": "服务年限",
    "serviceCategory": [
      "服务类别1",
      "服务类别2"
    ],
    "specialSkill": [
      "特色专长1",
      "特色专长2"
    ],
    "suggest": "投资建议",
    "wechat": "微信图片url",
    "phone": "联系电话"
  }
}
```

#### HOUSING卡片（房源卡片）
```json
{
  "serial": "3.2",
  "type": "CARD",
  "style": "HOUSING",
  "title": "小区名称",
  "fields": {
    "layout": "户型(例如:1室1厅1卫)",
    "area": "建筑面积(㎡)",
    "floor": "楼层信息(例如:6/6 层)",
    "orientation": "朝向",
    "decoration": "装修状况",
    "totalPrice": "总价(万)",
    "unitPrice": "单价(元/㎡)",
    "block": "所属板块(例如:梅陇镇)",
    "busiType": "交易类型(RENT|SALE)",
    "image": "房源图片URL(多图情况下取第一个)",
    "tags": ["房源标签1", "房源标签2"]
  }
}
```

**HOUSING卡片字段说明**：

- **layout**: 户型信息，如"1室1厅1卫"、"2室1厅1卫"等
- **area**: 建筑面积，单位为㎡
- **floor**: 楼层信息，格式为"当前楼层/总楼层"
- **orientation**: 房屋朝向，如"朝南"、"朝东"等
- **decoration**: 装修状况，如"精装"、"毛坯"、"简装"等
- **totalPrice**: 总价，单位为万元
- **unitPrice**: 单价，单位为元/㎡
- **block**: 所属板块或街道名称
- **busiType**: 交易类型，根据房源数据中的type字段确定，"RENT"对应"租"，"SALE"对应"售"
- **image**: 房源图片URL，优先使用coverUrl字段，如无则取medias数组第一个图片的url
- **tags**: 房源标签数组，来源于房源数据中的labels字段


<----------------------------(json_template)---------------------------->
=

```json
{
  "type": "POLICY_COMMENT",
  "title": "政策解读报告标题",
  "subject": "解读时间: [当前月份(例如2025年07月)]<br/>数据来源: 克而瑞、市场公开数据<br/>免责申明:本报告基于公开数据和政策文件，通过AI算法分析得出结果，仅供参考",
  "widgets": [
    {
      "serial": "0",
      "type": "TITLE",
      "style": "DOCUMENT",
      "title": "政策解读报告标题"
    },
    {
      "serial": "1",
      "type": "TITLE",
      "style": "SECTION",
      "title": "政策核心解读"
    },
    {
      "serial": "1.1",
      "type": "TITLE",
      "style": "PARAGRAPH",
      "title": "政策核心优势"
    },
    {
      "serial": "1.1.1",
      "type": "LIST",
      "style": "BOARD",
      "content": [
        {
          "content": "政策优势要点1"
        },
        {
          "content": "政策优势要点2"
        },
        {
          "content": "政策优势要点3"
        }
      ]
    },
    {
      "serial": "1.2",
      "type": "TITLE",
      "style": "PARAGRAPH",
      "title": "政策要点列表"
    },
    {
      "serial": "1.2.1",
      "type": "LIST",
      "style": "NUMBER",
      "content": [
        {
          "content": "政策要点1"
        },
        {
          "content": "政策要点2"
        },
        {
          "content": "政策要点3"
        },
        {
          "content": "政策要点4"
        },
        {
          "content": "政策要点5"
        }
      ]
    },
    {
      "serial": "2",
      "type": "TITLE",
      "style": "SECTION",
      "title": "市场数据全景分析"
    },
    {
      "serial": "2.1",
      "type": "TITLE",
      "style": "PARAGRAPH",
      "title": "价格分布特征"
    },
    {
      "serial": "2.1.1",
      "type": "TITLE",
      "style": "ENTRY",
      "title": "总价段成交分布"
    },
    {
      "serial": "2.1.1.1",
      "type": "CHART",
      "style": "PIE",
      "title": "总价段成交分布图(单位:万元)",
      "content": [
        {
          "title": "200万元以下",
          "content": [26.3]
        },
        {
          "title": "200-300万元",
          "content": [24.5]
        },
        {
          "title": "300-500万元",
          "content": [22.3]
        },
        {
          "title": "500-700万元",
          "content": [9.3]
        },
        {
          "title": "700-1000万元",
          "content": [8.2]
        },
        {
          "title": "1000万元以上",
          "content": [9.4]
        }
      ]
    },
    {
      "serial": "2.1.1.2",
      "type": "TEXT",
      "style": "BOARD",
      "content": "市场洞察分析内容"
    },
    {
      "serial": "2.1.2",
      "type": "TITLE",
      "style": "ENTRY",
      "title": "单价段成交分布"
    },
    {
      "serial": "2.1.2.1",
      "type": "CHART",
      "style": "PIE",
      "title": "单价段成交分布图(单位:元/㎡)",
      "content": [
        {
          "title": "40000-60000元/㎡",
          "content": [29.1]
        },
        {
          "title": "30000-40000元/㎡",
          "content": [18.6]
        },
        {
          "title": "60000-80000元/㎡",
          "content": [11.8]
        },
        {
          "title": "80000-100000元/㎡",
          "content": [8.5]
        },
        {
          "title": "100000元/㎡以上",
          "content": [9.6]
        },
        {
          "title": "20000-30000元/㎡",
          "content": [16.7]
        },
        {
          "title": "20000元/㎡以下",
          "content": [5.7]
        }
      ]
    },
    {
      "serial": "2.1.2.2",
      "type": "TEXT",
      "style": "BOARD",
      "content": "市场洞察分析内容"
    },
    {
      "serial": "2.2",
      "type": "TITLE",
      "style": "PARAGRAPH",
      "title": "区域分布分析"
    },
    {
      "serial": "2.2.1",
      "type": "TITLE",
      "style": "ENTRY",
      "title": "重点区域成交情况"
    },
    {
      "serial": "2.2.1.1",
      "type": "CHART",
      "style": "PIE",
      "title": "区域成交分布图(单位:%)",
      "content": [
        {
          "title": "浦东新区",
          "content": [24.3]
        },
        {
          "title": "闵行区",
          "content": [10.4]
        },
        {
          "title": "宝山区",
          "content": [10.2]
        },
        {
          "title": "徐汇区",
          "content": [4.8]
        },
        {
          "title": "黄浦区",
          "content": [2.3]
        },
        {
          "title": "其他区域",
          "content": [48.0]
        }
      ]
    },
    {
      "serial": "2.2.1.3",
      "type": "TEXT",
      "style": "BOARD",
      "content": "市场洞察分析内容"
    },
    {
      "serial": "2.3",
      "type": "TITLE",
      "style": "PARAGRAPH",
      "title": "市场趋势分析"
    },
    {
      "serial": "2.3.1",
      "type": "TITLE",
      "style": "ENTRY",
      "title": "月度成交趋势"
    },
    {
      "serial": "2.3.1.1",
      "type": "CHART",
      "style": "BAR",
      "title": "新增挂牌套数月度趋势(套)",
      "cols": ["2024/07", "2024/09", "2024/11", "2025/01", "2025/03", "2025/05", "2025/06"],
      "content": [
        {
          "title": "新增挂牌套数(套)",
          "content": [9033, 12919, 16137, 8361, 19855, 14228, 12723]
        }
      ]
    },
    {
      "serial": "2.3.1.2",
      "type": "CHART",
      "style": "MIXED",
      "title": "月度成交趋势图",
      "cols": ["2024/07", "2024/08", "2024/09", "2024/10", "2024/11", "2024/12", "2025/01", "2025/02", "2025/03", "2025/04", "2025/05", "2025/06"],
      "content": [
        {
          "title": "成交套数(套)",
          "content": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0],
          "chartType": "BAR"
        },
        {
          "title": "成交均价(元/㎡)",
          "content": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0],
          "chartType": "LINE"
        }
      ]
    },
    {
      "serial": "2.3.1.3",
      "type": "TEXT",
      "style": "BOARD",
      "content": "市场洞察分析内容"
    },
    {
      "serial": "2.3.2",
      "type": "TITLE",
      "style": "ENTRY",
      "title": "土地供应数据"
    },
    {
      "serial": "2.3.2.1",
      "type": "LIST",
      "style": "BULLET",
      "content": [
        {
          "content": "土地供应数据1"
        },
        {
          "content": "土地供应数据2"
        },
        {
          "content": "土地供应数据3"
        }
      ]
    },
    {
      "serial": "2.3.2.2",
      "type": "TEXT",
      "style": "BOARD",
      "content": "市场洞察分析内容"
    },
    {
      "serial": "2.4",
      "type": "TITLE",
      "style": "PARAGRAPH",
      "title": "户型面积分析"
    },
    {
      "serial": "2.4.1",
      "type": "TITLE",
      "style": "ENTRY",
      "title": "面积段成交分布"
    },
    {
      "serial": "2.4.1.1",
      "type": "CHART",
      "style": "PIE",
      "title": "面积段成交分布图(单位:㎡)",
      "content": [
        {
          "title": "70-90㎡",
          "content": [25.0]
        },
        {
          "title": "50-70㎡",
          "content": [24.5]
        },
        {
          "title": "90-110㎡",
          "content": [14.2]
        },
        {
          "title": "110-130㎡",
          "content": [12.8]
        },
        {
          "title": "130㎡以上",
          "content": [23.5]
        }
      ]
    },
    {
      "serial": "2.4.1.2",
      "type": "TEXT",
      "style": "BOARD",
      "content": "市场洞察分析内容"
    },
    {
      "serial": "2.4.2",
      "type": "TITLE",
      "style": "ENTRY",
      "title": "新房户型偏好"
    },
    {
      "serial": "2.4.2.1",
      "type": "CHART",
      "style": "PIE",
      "title": "新房户型偏好分布图(单位:%)",
      "content": [
        {
          "title": "三房户型",
          "content": [67.0]
        },
        {
          "title": "四房户型",
          "content": [18.0]
        },
        {
          "title": "二房户型",
          "content": [10.0]
        },
        {
          "title": "五房及以上",
          "content": [5.0]
        }
      ]
    },
    {
      "serial": "2.4.2.2",
      "type": "TEXT",
      "style": "BOARD",
      "content": "市场洞察分析内容"
    },
    {
      "serial": "3",
      "type": "TITLE",
      "style": "SECTION",
      "title": "置业建议"
    },
    {
      "serial": "3.1",
      "type": "TEXT",
      "style": "NORMAL",
      "content": "基于当前政策及市场特征的总体建议描述"
    },
    {
      "serial": "3.2",
      "type": "LIST",
      "style": "BOARD",
      "content": [
        {
          "title": "中老年置业者建议",
          "content": "针对中老年置业者的具体建议内容"
        },
        {
          "title": "高净值家庭建议",
          "content": "针对高净值家庭的具体建议内容"
        },
        {
          "title": "年轻购房者建议",
          "content": "针对年轻购房者的具体建议内容"
        },
        {
          "title": "多套房家庭建议",
          "content": "针对多套房家庭的具体建议内容"
        }
      ]
    },
    {
      "serial": "4",
      "type": "TITLE",
      "style": "SECTION",
      "title": "风险提示与注意事项"
    },
    {
      "serial": "4.1",
      "type": "LIST",
      "style": "BULLET",
      "content": [
        {
          "content": "风险提示1"
        },
        {
          "content": "风险提示2"
        },
        {
          "content": "风险提示3"
        },
        {
          "content": "风险提示4"
        },
        {
          "content": "风险提示5"
        }
      ]
    },
    {
      "serial": "4.2",
      "type": "TEXT",
      "style": "WEAKEN",
      "content": "专业建议：对于复杂情况，建议提前咨询专业律师和税务师，做好全生命周期资产规划。"
    },
    {
      "serial": "4.3",
      "type": "CARD",
      "style": "HOUSING",
      "title": "小区名称",
      "fields": {
        "layout": "户型(例如:1室1厅1卫)",
        "area": "建筑面积(㎡)",
        "floor": "楼层信息(例如:6/6 层)",
        "orientation": "朝向",
        "decoration": "装修状况",
        "totalPrice": "总价(万)",
        "unitPrice": "单价(元/㎡)",
        "block": "所属板块(例如:梅陇镇)",
        "busiType": "交易类型(RENT|SALE)",
        "image": "房源图片URL(多图情况下取第一个)",
        "tags": [
          "房源标签1",
          "房源标签2"
        ]
      }
    }
  ]
}
```
<----------------------------(cric_output_flow)---------------------------->
=
${prevGenerateResponse}

<----------------------------(cric_output)---------------------------->
=
# 2025年上海房屋继承税政策解读与置业策略报告

## 引言

2025年上海市房屋继承税政策整体维持稳定，对法定继承人(配偶、子女、父母等)继承房产，依然免征契税，仅需缴纳按房屋市场价格计算的印花税，税率为万分之五。这一政策延续了上海支持家庭财富合理传承的导向，为居民资产规划提供了稳定预期。

## 1. 政策核心解读

### 政策核心优势

- **契税全免**：法定继承房产免征契税，相比买卖交易节省3%契税成本
- **印花税极低**：仅按房屋市场价万分之五征收，1000万房产仅需5000元
- **政策延续性**：继承后转让个税政策保持20%税率不变，提供稳定预期

### 政策要点列表

1. 适用对象：配偶、子女、父母等法定继承人
2. 免征契税：继承环节不征收契税
3. 印花税率：按房产评估价0.05%征收
4. 后续转让：按(售价-原值)×20%征收个税
5. 不适用新政：继承房产不受2025年房产税调整影响

## 2. 市场数据全景分析

### 价格分布特征

#### 总价段成交分布

- 200万元以下：619套（占比26.3%）
- 200-300万元：577套（占比24.5%）
- 300-500万元：526套（占比22.3%）
- 500-700万元：219套（占比9.3%）

**市场洞察**：上海二手房市场呈现"金字塔"结构，300万以下房源占比超50%，显示刚性需求仍是市场主力。中低价位房产更适合作为家庭基础资产传承。

#### 单价段成交分布

- 40000-60000元/㎡：686套（占比29.1%）
- 30000-40000元/㎡：438套（占比18.6%）
- 60000-80000元/㎡：278套（占比11.8%）

**市场洞察**：4-6万元单价段成交量最大，对应中环附近成熟社区，兼具居住品质和价格优势，是理想的传承资产选择。

### 区域分布分析

#### 重点区域成交情况

- 浦东新区：572套（占比24.3%）
- 闵行区：245套（占比10.4%）
- 宝山区：240套（占比10.2%）
- 徐汇区：114套（占比4.8%）
- 黄浦区：54套（占比2.3%）

**市场洞察**：浦东成交量占比超1/4，显示其作为城市核心发展区的市场活跃度。核心区域如黄浦、徐汇虽然量少但资产保值性强。

### 市场趋势分析

#### 月度成交趋势（2024.07-2025.06）

| 月份      | 成交套数 | 成交均价(元/㎡) |
|---------|------|-----------|
| 2025/06 | 2279 | 45739     |
| 2025/05 | 4780 | 47563     |
| 2025/04 | 4555 | 49617     |
| 2025/03 | 7099 | 49902     |

**市场洞察**：2025年上半年市场呈现"量升价稳"态势，3月出现成交小高峰，均价稳定在4.5-5万元/㎡区间，为资产传承提供稳定环境。

#### 土地供应数据

- 供应峰值：2024年9月（506929㎡）
- 2025年6月：485775㎡
- 楼板价波动区间：29283-84204元/㎡

**市场洞察**：土地市场供应充足但价格波动显著，核心区域地块备受追捧，长期看将支撑优质房产价值。

### 户型面积分析

#### 面积段成交分布

- 70-90㎡：588套（占比25.0%）
- 50-70㎡：576套（占比24.5%）
- 90-110㎡：334套（占比14.2%）

**市场洞察**：70-90㎡中小户型最受欢迎，既满足基本居住需求，又便于后期处置，是"刚需+传承"双重属性的理想选择。

#### 新房户型偏好

- 三房户型：3068套（占比67%）
- 四房户型：824套（占比18%）

**市场洞察**：三房户型占据绝对主流，反映改善型需求主导新房市场，适合多代同堂的家庭资产规划。

## 3. 置业建议

基于当前继承税政策及市场特征，我们提出以下策略建议：

**中老年置业者（50-65岁）**应重点考虑内环内80-100㎡的二居室，如静安寺、徐家汇等成熟板块。这类资产兼具养老自住和传承价值，且流动性好。数据显示70-90㎡户型成交占比达25%，市场接受度高。

**高净值家庭**建议配置核心区域高品质大户型或别墅产品，如浦东前滩、黄浦滨江等板块。虽然200㎡以上房源仅占1.4%，但稀缺性保障长期价值。可充分利用继承免税优势实现家族资产跨代保值。

**年轻购房者（30-40岁）**宜选中环地铁沿线优质学区房，如闵行春申、浦东联洋等板块。数据显示徐汇、静安学区房价格坚挺，4-6万元/㎡单价段成交占比近30%，既有教育功能又具资产传承价值。

**多套房家庭**建议通过合理安排所有权登记，将不同房产分散在家庭成员名下。继承1000万房产仅需5000元印花税，相比买卖节省约30万税费，是优化家庭资产结构的有效方式。

## 4. 风险提示与注意事项

- **继承后转让税负**：再次出售需按差额20%缴个税，务必保留原购房凭证
- **特殊房产限制**：拆迁安置房、共有产权房继承有特殊规定
- **涉外继承**：涉及境外因素需专业法律支持
- **未成年人继承**：处置流程复杂需提前规划
- **债务风险**：继承房产同时继承相关房贷等债务

专业建议：对于复杂继承情况，建议提前咨询专业律师和税务师，做好全生命周期资产规划。


<----------------------------(house_flow)---------------------------->
=
${toJSONStr(request.houseInfo)}

<----------------------------(house)---------------------------->
=

```json
{
  "title": "高兴花园二、三、四街坊",
  "layout": "1室1厅1卫",
  "area": "47㎡",
  "floor": "6/6层",
  "orientation": "朝南",
  "decoration": "精装",
  "totalPrice": "235万",
  "unitPrice": "50000元/㎡",
  "block": "梅陇镇",
  "busiType": "SALE",
  "image": "https://etc-consumer.oss-cn-shanghai.aliyuncs.com/delegation-media/9094662523348608768/202506121632/e79fbf26-89e4-425d-aa22-0072e81e98cdtmp_8af44908650243058b563476f3f5b9d9.png",
  "tags": ["拎包入住", "随时可看"]
}
```
