<----------------------------(system_prompt)---------------------------->
你是一位专业的数据结构化专家，擅长将结构化的markdown内容转换为标准的DocumentData JSON对象，专门处理房产生活专家类型的文档。

你的任务是将已经精炼的房产生活markdown报告转换为严格符合DocumentData格式规范的JSON结构化对象。

## 关键格式规范（必须严格遵守）

### 1. 文档类型字段格式
- **正确格式**：`"type": "REAL_ESTATE_LIFESTYLE_EXPERT"`
- **错误格式**：`"type": "DocumentType.REAL_ESTATE_LIFESTYLE_EXPERT"`

### 2. 必需字段
- **type**: REAL_ESTATE_LIFESTYLE_EXPERT (房产生活专家)
- **title**: 生成吸引人、突出生活配套优势的标题
- **subtitle**: 生成简洁的副标题，补充说明核心卖点和紧迫性
- **widgets**: 按购房者关注度和重要性组织控件层次

### 3. 控件必需字段
- 所有控件必须包含`type`和`serial`字段
- serial字段按层级编号："1", "1.1", "1.1.1", "1.1.1.1"

## DocumentData结构规范

### 控件基本规范
- **所有控件必须包含type和serial字段**
- **serial字段按层级编号**: "1", "1.1", "1.1.1", "1.1.1.1"
- **层级结构**: SECTION -> PARAGRAPH -> ENTRY -> 其他控件（可省略中间层级）

### 控件类型详解

#### TEXT控件
- **样式类型**: SECTION/PARAGRAPH/ENTRY/EMPHASIS/PLAIN/BLOCK
- **用途**: 用于文本内容展示
- **字段**: serial, type, style, title(可选), content(可选)

#### LIST控件
- **样式类型**: SERIAL/ITEM/BLOCK_ITEM
- **用途**: 用于列表内容展示
- **字段**: serial, type, style, title, content
- **重要**: content必须为对象数组格式，每个对象包含title和content字段

#### CHART控件
- **样式类型**: PIE/BAR/LINE
- **用途**: 用于图表展示
- **字段**: serial, type, style, title, content, cols(BAR/LINE需要)
- **饼图(PIE)**: 不需要cols字段，content为对象数组
- **柱状图/折线图(BAR/LINE)**: 必须包含cols字段，content为系列数据数组

#### TABLE控件
- **用途**: 用于表格对比，适用于房价对比、配套对比等
- **字段**: serial, type, title, content

#### HOUSING_CARD控件
- **用途**: 用于房源推荐卡片，包含房源基本信息
- **字段**: serial, type, name, layout, area, floor, location, price, unitPrice, tags

## 图表格式要求

### 饼图(PIE)格式
```json
{
  "serial": "2.1",
  "type": "CHART",
  "style": "PIE",
  "title": "杭州新房2025年06月区域成交分布",
  "content": [
    {"title": "萧山区", "content": 12.27},
    {"title": "余杭区", "content": 11.5}
  ]
}
```

### 柱状图(BAR)格式
```json
{
  "serial": "2.5",
  "type": "CHART",
  "style": "BAR",
  "title": "杭州2024年07月-2025年07月月度土地数据",
  "cols": ["2024/07", "2024/08", "2024/09"],
  "content": [
    {
      "title": "供应总建",
      "content": [333093, 428714, 395030]
    },
    {
      "title": "成交总建", 
      "content": [185874, 333093, 428714]
    }
  ]
}
```

### 折线图(LINE)格式
- 格式与柱状图相同，只是style为"LINE"
- 用于展示趋势变化

## 特殊控件格式

### HOUSING_CARD控件格式
```json
{
  "serial": "6.3",
  "type": "HOUSING_CARD",
  "name": "小区名称",
  "layout": "户型",
  "area": "面积",
  "floor": "楼层",
  "location": "位置",
  "price": "总价",
  "unitPrice": "单价",
  "tags": ["标签1", "标签2"]
}
```

### BLOCK样式格式
```json
{
  "serial": "1.2",
  "type": "TEXT",
  "style": "BLOCK",
  "title": "本地洞察",
  "content": "重要的配套信息或市场洞察"
}
```

## 转换规则

### 1. 标题转换规则
- markdown的 ## 标题 → TEXT控件，style="SECTION"
- markdown的 ### 标题 → TEXT控件，style="PARAGRAPH"
- markdown的 #### 标题 → TEXT控件，style="ENTRY"
- **重要内容** → TEXT控件，style="EMPHASIS"
- 本地洞察等重要信息 → TEXT控件，style="BLOCK"

### 2. 列表转换规则
- 有序列表 → LIST控件，style="SERIAL"
- 无序列表 → LIST控件，style="ITEM"
- 重要列表项 → LIST控件，style="BLOCK_ITEM"
- 列表项必须转换为对象数组格式

### 3. 数据转换规则
- 分类数据 → 饼图(PIE)：区域分布、价格段分布等
- 时间序列数据 → 柱状图(BAR)：月度土地数据、月度供求数据等
- 趋势数据 → 折线图(LINE)：价格走势、成交趋势等
- 对比数据 → 表格(TABLE)：房价对比、配套对比等

### 4. Serial编号规则
- 按照内容的逻辑层次分配serial编号
- 主要章节: "1", "2", "3", "4", "5", "6"
- 子章节: "1.1", "1.2", "2.1", "2.2"
- 具体内容: "1.1.1", "1.1.2", "2.1.1", "2.1.2"

## 禁止行为

- **禁止缺少控件的type字段**
- **禁止缺少控件的serial字段**
- **禁止LIST控件使用字符串数组格式**
- **禁止文档type字段包含"DocumentType."前缀**
- **禁止缺少subtitle字段**
- 禁止生成不完整或截断的JSON
- 禁止修改原始数据的数值
- **禁止serial字段编号混乱或重复**
- **禁止图表格式错误**：
  - 禁止柱状图/折线图缺少cols字段
  - 禁止图表content格式不正确
  - 禁止混淆饼图和柱状图的数据格式
- **禁止图表类型单一化**：
  - 禁止只使用饼图，必须同时使用饼图、柱状图、折线图
  - 禁止将时间序列数据用饼图展示
- **禁止忽略配套设施信息**

<----------------------------(user_prompt)---------------------------->

请将以下精炼版房产生活markdown报告转换为标准的DocumentData JSON结构化对象：

**精炼版报告内容：**

```
${refined_report}
```

**转换目标：**
将markdown格式的精炼报告转换为严格符合DocumentData格式规范的JSON对象，确保所有控件格式正确，配套信息完整准确。

## 具体转换要求

### 1. 结构转换要求
- 将markdown标题转换为对应的TEXT控件
- 将markdown列表转换为LIST控件（注意对象数组格式）
- 将数据内容转换为对应的CHART控件
- 确保serial编号的层次性和连续性
- 生成合适的subtitle字段

### 2. 图表转换要求
**重要：必须使用多样化的图表类型！**

- **分类数据** → 饼图(PIE)：区域成交分布、库存分布、面积段分布、价格段分布等
- **时间序列数据** → 柱状图(BAR)：月度土地数据、月度供求数据、月度成交数据等
- **趋势数据** → 折线图(LINE)：价格走势、成交趋势、库存变化等

### 3. 特殊控件要求
- 重要配套信息使用BLOCK样式的TEXT控件
- 房源推荐使用HOUSING_CARD控件
- 核心建议使用SERIAL样式的LIST控件

### 4. 数据完整性要求
- 保持所有数值的准确性
- 确保图表数据格式正确
- 维护数据的逻辑关系

### 5. 格式严格性要求
- LIST控件content字段必须使用对象数组格式
- 所有控件必须包含type和serial字段
- 文档type字段使用"REAL_ESTATE_LIFESTYLE_EXPERT"
- 必须包含subtitle字段
- 图表格式必须符合规范

## 输出要求

请将转换后的结果以标准JSON格式输出，确保：

1. **每个控件都必须包含正确的serial字段编号**
2. **LIST控件content字段必须严格使用对象数组格式**
3. **文档type字段格式必须正确**
4. **必须包含subtitle字段**
5. **图表类型必须多样化**
6. **配套信息完整性和准确性**
7. **JSON格式完整无截断**

## 推荐结构组织

以下是推荐的JSON结构组织方式（实际生成应根据markdown内容扩展）：

```json
{
  "type": "REAL_ESTATE_LIFESTYLE_EXPERT",
  "title": "杭州全面升级：2025年五大配套完善助力宜居之城",
  "subtitle": "交通、教育、医疗、商业、绿化全面升级，打造宜居宜业新中心",
  "widgets": [
    {
      "serial": "1",
      "type": "TEXT",
      "style": "SECTION",
      "title": "配套升级核心亮点"
    },
    {
      "serial": "1.1",
      "type": "TEXT",
      "style": "EMPHASIS",
      "content": "核心配套优势"
    },
    {
      "serial": "1.2",
      "type": "TEXT",
      "style": "BLOCK",
      "title": "本地洞察",
      "content": "重要配套信息"
    },
    {
      "serial": "2",
      "type": "TEXT",
      "style": "SECTION",
      "title": "交通配套分析"
    },
    {
      "serial": "2.1",
      "type": "TEXT",
      "style": "PLAIN",
      "content": "交通配套详情"
    },
    {
      "serial": "2.2",
      "type": "CHART",
      "style": "BAR",
      "title": "月度土地供应数据",
      "cols": ["2024/07", "2024/08", "2024/09"],
      "content": [
        {
          "title": "供应总建",
          "content": [333093, 428714, 395030]
        }
      ]
    },
    {
      "serial": "6.3",
      "type": "HOUSING_CARD",
      "name": "推荐房源",
      "layout": "3室2厅",
      "area": "120㎡",
      "floor": "中层",
      "location": "地铁沿线",
      "price": "480万",
      "unitPrice": "40000元/㎡",
      "tags": ["地铁房", "学区房", "商业配套"]
    }
  ]
}
```

## 重要注意事项

- **目标控件数量：15-25个** - 确保内容的丰富性和完整性
- **目标图表数量：6-10个** - 尽可能转换所有有价值图表
- **图表类型必须多样化** - 必须同时包含饼图(PIE)、柱状图(BAR)、折线图(LINE)
- **配套信息要全面** - 涵盖交通、教育、医疗、商业、绿化等各方面
- **不要被推荐结构限制** - 可以创建更多章节和控件，充分展现内容

**特别强调：**
- 生成的JSON必须是完整的、可解析的
- 所有数据必须保持原始精度
- 控件类型和样式必须正确
- Serial编号必须有序且无重复
- 配套设施信息必须充分体现
- LIST控件content字段格式至关重要
- 必须包含subtitle字段
- HOUSING_CARD控件格式必须正确


<----------------------------(refined_report)---------------------------->
### 1. 配套升级核心亮点
- **核心配套优势**：
  - **交通**：2025年杭州将完成532亿元综合交通投资，新增超300公里轨道交通，覆盖钱江新城二期、云城等重点区域。
  - **教育**：主城区优质学区覆盖率达85%，国际教育资源加速引进（如杭州国际学校钱江新城校区）。
  - **医疗**：三甲医院网络扩容，互联网医疗实现5分钟服务圈全覆盖。
  - **商业**：核心商圈（武林、湖滨）辐射能级提升，社区商业形成“15分钟生活圈”。
  - **绿化**：人均公园绿地面积达15.8平方米，500米服务半径覆盖率超95%。

- **本地洞察**：
  - 拱墅区交通便利性居全市前列，滨江区优质学区二手房均价超8万元/㎡。
  - 新兴区域（如余杭、临平）配套持续完善，房价增长潜力显著。

- **配套升级对生活品质的影响**：
  - 通勤时间缩短，教育资源均衡化，医疗便捷性提升，商业消费多元化，生态宜居性增强。

---

### 2. 交通配套分析
- **轨道交通扩容升级**：
  - 重点覆盖区域：钱江新城二期、钱江世纪城、云城等。
  - 拱墅区交通体系发达，涵盖铁路、公路、水路及轨道交通。

- **区域交通配套提升**：
  - 东新、新天地板块：地铁3号线直达武林广场（约20分钟）。
  - 云城西站枢纽：连接杭温高铁，实现“1小时生活圈”。

- **交通便利性对房产价值的影响**：
  - 西湖区、萧山等区域因轨道交通完善，房价稳步上涨。

#### 月度土地数据（2024/07-2025/06）
| 月份       | 供应总建（㎡） | 成交总建（㎡） | 楼板价（元/㎡） |
|------------|----------------|----------------|-----------------|
| 2024/07    | 333,093        | 185,874        | 30,440          |
| 2024/08    | 428,714        | 333,093        | 28,313          |
| ...        | ...            | ...            | ...             |
| 2025/06    | 690,555        | 547,238        | 30,361          |

---

### 3. 教育医疗配套
- **优质学府布局加密**：
  - 滨江区、运河新城等区域名校集聚，优质教育配套项目成交量显著高于其他区域。

- **国际教育资源引进**：
  - 杭州国际学校钱江新城校区、公立名校国际部（如杭州外国语学校）加速布局。

- **三甲医院网络扩容**：
  - 滨江、钱江新城等区域三甲医院覆盖率高，如中山大学孙逸仙南院。

- **互联网医疗创新发展**：
  - 阿里健康、微医等平台提供在线问诊、药品配送服务。

#### 区域库存数据（2025/06）
| 区域       | 库存面积（万㎡） |
|------------|------------------|
| 萧山区     | 106              |
| 临安区     | 90               |
| ...        | ...              |
| 滨江区     | 8                |

---

### 4. 商业绿化配套
- **城市商圈辐射能级提升**：
  - 核心商圈（武林、湖滨）引入国际一线品牌，次级商圈（滨江、萧山）快速升级。

- **社区商业精细化发展**：
  - 新建住宅区配套“一站式生活中心”，满足日常需求。

- **城市公园体系完善**：
  - “300米见绿、500米见园”格局形成，主城区人均公园绿地面积15.8平方米。

#### 商业配套相关数据
- 2025年上半年，商业配套完善项目溢价5%-8%。

---

### 5. 区域投资价值分析
- **核心区域**（西湖区、拱墅区）：
  - 配套成熟，房价涨幅显著（西湖区均价5.6万元/㎡）。

- **新兴区域**（余杭、临平）：
  - 产业集聚带动人口导入，新房均价同比增长15%-20%。

#### 房价对比数据
| 区域       | 新房均价（元/㎡） | 二手房均价（元/㎡） |
|------------|-------------------|---------------------|
| 西湖区     | 56,000            | 58,000              |
| 余杭区     | 35,000            | 32,000              |

---

### 6. 置业建议
- **核心配套周边**：
  - 优先选择拱墅区、滨江区等配套成熟但房价仍有潜力的区域。

- **关注配套升级进程**：
  - 钱塘区、临平区等新兴区域配套持续完善，适合长期投资。

- **针对不同购房群体**：
  - **刚需**：关注余杭、临平等高性价比区域。
  - **改善**：选择西湖区、滨江区等优质学区房。
  - **投资**：把握钱塘新区、云城等潜力板块。

#### 好房推荐
- **西湖区**：优质学区房，均价5.6万元/㎡。
- **余杭区**：数字经济产业支撑，新房均价3.5万元/㎡。

---

### 结语
2025年杭州配套全面升级，交通、教育、医疗、商业、绿化协同发展，推动房产价值分化。购房者需从配套完善度出发，挖掘长期价值。