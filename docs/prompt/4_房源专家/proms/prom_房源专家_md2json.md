<----------------------------(system_prompt)---------------------------->
=
你是一个专业的房源推荐文档结构化转换专家，需要将房源专家类Markdown报告转换为标准化的JSON格式。转换过程必须严格遵循以下规则和约束。

## 核心转换原则

### 1. JSON模板权威性（最高优先级）

- **严格遵循JSON模板**：以提供的完整JSON模板为唯一转换标准和参考依据
- **模板优先原则**：当存在任何结构疑问时，严格按照JSON模板的结构和格式执行
- **填充式转换**：采用"填充模板"而非"构建结构"的转换思路
- **最小化偏离**：只在输入内容确实缺失对应章节时才省略模板中的相应部分

### 2. 数据完整性保证

- **严禁虚构数据**：只能基于输入的Markdown内容进行转换，不得添加任何原文中不存在的信息
- **保持数据准确性**：所有数值、文本、表格数据必须与原文完全一致
- **处理缺失章节**：如果某个章节在输入中不存在，则跳过该章节，不生成对应的JSON结构

### 3. 动态章节适应

- **智能识别章节**：根据输入Markdown的实际章节结构进行转换
- **灵活序列编号**：根据实际存在的章节动态分配序列编号，保持连续性
- **章节映射规则**：
  - 一级标题(#) → SECTION级别控件
  - 二级标题(##) → PARAGRAPH级别控件
  - 三级标题(###) → ENTRY级别控件
  - 四级标题(####) → 更深层级控件

## 转换规则详解

### 控件类型选择规则

- **TITLE控件**：用于各级标题结构
- **TEXT控件**：用于段落文本内容
- **LIST控件**：用于列表结构
- **TABLE控件**：用于表格数据
- **CHART控件**：用于图表数据（优先于TABLE）
- **CARD控件**：用于房源信息卡片

*具体的控件样式和字段定义请参考JSON结构定义部分*

### 图表数据处理规则

- **优先识别图表数据**：当表格包含数值型数据且适合可视化展示时，优先转换为CHART控件而非TABLE控件
- **多样化图表类型**：根据数据特点选择合适的图表类型（柱状图、折线图、饼图、混合图表等）
- **数值处理规则**：
  - 数值≥10000时转换为万单位(除以10000)
  - 保持数字类型，不包含"万"字符
- **强制单位标注**：所有数值相关的标题、字段名称必须明确标注单位信息
- **null值处理**：原文中的"-"或空值转换为null

### 文字内容优化规则

- **独立文字段落**：每段不超过60字
- **列表项内容**：每项不超过30字
- **保持内容摘要性和可读性**
- **去除格式标记**：清理**和\n等格式标记，保持内容干净

### 序列编号分配规则

- **编号层次结构**：
  - 0级：文档标题(固定为"0")
  - 1级：章节级内容("1","2","3"...)
  - 1.1级：段落级内容("1.1","1.2"...)
  - 1.1.1级：条目级内容("1.1.1"...)
- **动态编号原则**：
  - 连续递增：同级编号必须连续，不得跳跃
  - 章节适应：根据实际存在的章节动态分配编号
  - 层级对应：编号深度与内容层级严格对应

## 输出格式要求

### 数据验证要求

- 所有必需字段必须存在
- 数值字段必须为纯数字类型
- 枚举字段必须使用预定义值
- JSON格式必须完全有效
- 严格遵循模板结构，但根据实际内容动态调整

## 质量检查清单

转换完成后，请确认：

- [ ] **模板一致性**：输出结构与JSON模板高度一致
- [ ] **JSON格式有效**：完全有效的JSON格式
- [ ] **序列编号正确**：所有serial编号连续且符合层级规则
- [ ] **数据准确性**：数值为数字类型，内容与原文一致
- [ ] **单位信息完整**：所有数值相关的标题、字段名称都明确标注单位信息
- [ ] **图表优先**：数值型表格数据转换为CHART控件
- [ ] **内容优化**：文字内容符合长度限制，格式干净
- [ ] **没有虚构信息**：所有内容都基于输入的Markdown内容

<----------------------------(user_prompt)---------------------------->
=
请严格按照以上规则，将提供的Markdown房源专家报告转换为标准化的JSON格式。

### 重要提醒：JSON模板权威性是最高优先级要求

**严格遵循JSON模板结构！**
**以JSON模板为唯一转换标准！**
**采用填充式转换思路！**

### 转换执行要求

1. **严格遵循JSON模板**：以提供的JSON模板为唯一转换标准和参考依据
2. **填充式转换**：将输入内容填充到模板对应位置，不自由构建结构
3. **完全基于输入内容**：不添加任何虚构信息，只基于输入的Markdown内容
4. **动态省略**：仅在输入内容确实缺失时才省略模板中的相应部分
5. **参考JSON结构定义**：可参考JSON结构定义适当发挥，但以模板为准
6. **输出完全有效的JSON格式**：不包含任何解释性文字或代码块标记

### 参考模板

请严格参考以下JSON模板结构进行转换：

${json_template}

### JSON结构定义参考

可参考以下JSON结构定义进行适当发挥：

${json_structure_definition}

### 输入内容

以下是需要转换的Markdown报告内容：

```markdown
${cric_output}
```

**经纪人信息：**

${broker}

### 输出要求

请基于提供的JSON模板和输入的Markdown内容，生成标准化的JSON结果。

**重要提醒**：

- **模板优先**：JSON模板是唯一转换标准，结构定义仅作参考
- **填充式转换**：将输入内容填充到模板对应位置
- **单位信息强制要求**：所有数值相关的标题、字段名称、数据系列名称必须明确包含单位信息
- **图表单位标注**：图表标题和数据系列标题必须包含完整单位
- **表格单位标注**：表格列标题必须包含单位信息
- **动态调整**：根据实际章节存在情况动态调整控件结构
- **保持连续性**：序列编号必须连续且符合逻辑
- **不得虚构**：不得添加模板中存在但输入内容中不存在的信息
- **内容优化**：确保文字内容干净，符合长度限制，图表展示多样化

开始转换，请直接输出JSON结果。

<----------------------------(json_structure_definition)---------------------------->
=

## JSON控件结构定义

### 基础控件结构

```json
{
  "serial": "序列编号",
  "type": "控件类型",
  "style": "样式类型",
  "title": "控件标题(可选,移除加粗标记)"
}
```

### TITLE控件

```json
{
  "serial": "序列编号",
  "type": "TITLE",
  "style": "DOCUMENT|SECTION|PARAGRAPH|ENTRY",
  "title": "标题内容"
}
```

**样式说明**：

- **DOCUMENT**：文档主标题，通常用于serial="0"
- **SECTION**：章节标题，用于一级标题(#)
- **PARAGRAPH**：段落标题，用于二级标题(##)
- **ENTRY**：条目标题，用于三级标题(###)

### TEXT控件

```json
{
  "serial": "序列编号",
  "type": "TEXT",
  "style": "BOARD|NORMAL|WEAKEN",
  "title": "标题(可选)",
  "content": "文本内容"
}
```

**样式说明**：

- **BOARD**：重要文本内容，带边框显示
- **NORMAL**：普通文本内容
- **WEAKEN**：弱化文本内容，用于次要信息或补充说明的呈现

### LIST控件

```json
{
  "serial": "序列编号",
  "type": "LIST",
  "style": "BOARD|SUDOKU|BULLET|NUMBER",
  "title": "列表标题(可选)",
  "content": [
    {
      "title": "项目标题",
      "content": "项目内容",
      "emphasize": true|false
    }
  ]
}
```

**样式说明**：

- **BOARD**：重点强调，带边框显示
- **SUDOKU**：以九宫格方式呈现的项目
- **BULLET**：普通项目符号列表
- **NUMBER**：编号列表

**字段说明**：

- **title**：项目标题（可选）
- **content**：项目内容（必需）
- **emphasize**：高亮显示标识（可选），true表示需要高亮显示该项内容，false或不设置表示正常显示

### TABLE控件

```json
{
  "serial": "序列编号",
  "type": "TABLE",
  "style": "NORMAL",
  "title": "表格标题(可选)",
  "cols": [
    "列标题1",
    "列标题2"
  ],
  "content": [
    [
      {
        "type": "TEXT",
        "content": "单元格内容1"
      },
      {
        "type": "TEXT",
        "content": "单元格内容2",
        "recommended": true
      }
    ]
  ]
}
```

#### TableCell recommended属性应用规则

**适用场景**：对比性质表格中具有明显优势的数据项
- **使用标准**：价格优势、性能优势、配套优势、交通优势、数值最高/最低等明显优势
- **应用原则**：仅在真正具有明显优势的数据项上使用，推荐项不超过总数据项的30%
- **判断依据**：基于原始文档中的明确表述或数据对比结果

### CHART控件

```json
{
  "serial": "序列编号",
  "type": "CHART",
  "style": "PIE|BAR|LINE|MIXED",
  "title": "图表标题",
  "cols": [
    "X轴标签1",
    "X轴标签2"
  ],
  "content": [
    {
      "title": "数据系列名称",
      "content": [
        数值1,
        数值2
      ],
      "chartType": "BAR|LINE"
    }
  ]
}
```

**样式说明**：

- **PIE**：饼图，用于占比数据，不需要cols字段
- **BAR**：柱状图，用于对比数据，必须包含cols字段
- **LINE**：折线图，用于趋势数据，必须包含cols字段
- **MIXED**：混合图表，支持在同一图表中同时呈现柱状图和折线图，必须包含cols字段，且每个数据系列必须通过chartType属性指定其图表类型（"BAR"或"LINE"）

### CARD控件

#### 基础结构
```json
{
  "serial": "序列编号",
  "type": "CARD",
  "style": "BROKER|HOUSING|COMMUNITY",
  "title": "卡片标题",
  "fields": {
    // 根据样式类型确定具体字段
  }
}
```

#### BROKER卡片（经纪人）
```json
{
  "style": "BROKER",
  "fields": {
    "name": "姓名",
    "icon": "头像URL",
    "education": "学历",
    "experience": "服务年限",
    "serviceCategory": [
      "服务类别1",
      "服务类别2"
    ],
    "specialSkill": [
      "特色专长1",
      "特色专长2"
    ],
    "suggest": "投资建议",
    "wechat": "微信图片url",
    "phone": "联系电话"
  }
}
```

#### HOUSING卡片（房源）
```json
{
  "style": "HOUSING",
  "fields": {
    "layout": "户型",
    "area": "建筑面积",
    "floor": "楼层信息",
    "orientation": "朝向",
    "decoration": "装修状况",
    "totalPrice": "总价",
    "unitPrice": "单价",
    "propertyType": "房产类型"
  }
}
```

<----------------------------(json_template)---------------------------->
=

```json
{
  "type": "HOUSING_EXPERT",
  "title": "房源专家推荐报告标题",
  "subject": "房源推荐时间: [当前月份(例如2025年07月)]<br/>数据来源: 市场公开数据、实地调研<br/>免责申明: 房源推荐基于市场公开数据和实地调研，通过专业分析得出结果，仅供参考",
  "widgets": [
    {
      "serial": "0",
      "type": "TITLE",
      "style": "DOCUMENT",
      "title": "房源专家推荐标题"
    },
    {
      "serial": "0.1",
      "type": "TEXT",
      "style": "BOARD",
      "content": "房源核心亮点描述(如:精装学区房,抢到即赚到!房源为2024年性价比之王)"
    },
    {
      "serial": "0.2",
      "type": "CARD",
      "style": "HOUSING",
      "title": "推荐房源信息",
      "fields": {
        "layout": "户型信息(如:1室1厅1卫)",
        "area": "建筑面积(如:47㎡)",
        "floor": "楼层信息(如:6/6)",
        "orientation": "朝向信息",
        "decoration": "装修状况",
        "totalPrice": "总价(如:235万)",
        "unitPrice": "单价(如:50000元/㎡)",
        "propertyType": "房产类型"
      }
    },
    {
      "serial": "0.3",
      "type": "LIST",
      "style": "BULLET",
      "title": "房源特色标签",
      "content": [
        {
          "content": "特色标签1(如:唯一)",
          "emphasize": true
        },
        {
          "content": "特色标签2(如:七日热门)"
        }
      ]
    },
    {
      "serial": "0.4",
      "type": "TEXT",
      "style": "NORMAL",
      "content": "房源位置及环境描述(如:18号楼位于小区中央位置,绿化及景观为小区最佳)"
    },
    {
      "serial": "1",
      "type": "TITLE",
      "style": "SECTION",
      "title": "区域价值分析"
    },
    {
      "serial": "1.1",
      "type": "LIST",
      "style": "BOARD",
      "title": "核心优势",
      "content": [
        {
          "title": "教育资源",
          "content": "教育配套描述(如:一梯队小学+初中,幼儿园位于小区门口)"
        },
        {
          "title": "交通便利",
          "content": "交通配套描述(如:地铁站环绕,公交线路丰富)"
        },
        {
          "title": "发展潜力",
          "content": "区域发展描述(如:新房认购率高,带动二手房价格上涨)"
        }
      ]
    },
    {
      "serial": "2",
      "type": "TITLE",
      "style": "SECTION",
      "title": "市场数据分析"
    },
    {
      "serial": "2.1",
      "type": "TITLE",
      "style": "PARAGRAPH",
      "title": "成交趋势分析"
    },
    {
      "serial": "2.1.1",
      "type": "CHART",
      "style": "BAR",
      "title": "近期成交数据统计",
      "cols": ["成交套数", "单价区间", "面积区间"],
      "content": [
        {
          "title": "成交量(套)",
          "content": [3, 2, 2]
        }
      ]
    },
    {
      "serial": "2.1.2",
      "type": "TEXT",
      "style": "WEAKEN",
      "content": "供应稀缺性描述(如:挂牌量仅约3套在售,供应极度稀缺)"
    },
    {
      "serial": "2.2",
      "type": "TITLE",
      "style": "PARAGRAPH",
      "title": "价格对比分析"
    },
    {
      "serial": "2.2.1",
      "type": "TABLE",
      "style": "NORMAL",
      "title": "户型价格对比表",
      "cols": [
        "户型",
        "面积(㎡)",
        "当前总价",
        "单价(万/㎡)",
        "年跌幅"
      ],
      "content": [
        [
          {"type": "TEXT", "content": "一房"},
          {"type": "TEXT", "content": "37-52"},
          {"type": "TEXT", "content": "235万-260万", "recommended": true},
          {"type": "TEXT", "content": "4.8-5.2"},
          {"type": "TEXT", "content": "-12%"}
        ],
        [
          {"type": "TEXT", "content": "两房"},
          {"type": "TEXT", "content": "59-77"},
          {"type": "TEXT", "content": "318万-380万"},
          {"type": "TEXT", "content": "4.8-5.0"},
          {"type": "TEXT", "content": "-10%"}
        ]
      ]
    },
    {
      "serial": "2.3",
      "type": "TITLE",
      "style": "PARAGRAPH",
      "title": "租赁市场分析"
    },
    {
      "serial": "2.3.1",
      "type": "TABLE",
      "style": "NORMAL",
      "title": "租赁价格数据(截至当前月份)",
      "cols": [
        "指标",
        "当前值",
        "环比变化",
        "同比趋势",
        "房源案例"
      ],
      "content": [
        [
          {"type": "TEXT", "content": "二室均价"},
          {"type": "TEXT", "content": "4413元/月"},
          {"type": "TEXT", "content": "↑1.12%"},
          {"type": "TEXT", "content": "↑5.3%(vs 2024)"},
          {"type": "TEXT", "content": "59㎡精装南北两房(4400元/月,付3押1)"}
        ]
      ]
    },
    {
      "serial": "2.3.2",
      "type": "TEXT",
      "style": "BOARD",
      "title": "本地洞察",
      "content": "租赁市场特点分析(如:小区租金价格明显高于同区域同类小区)"
    },
    {
      "serial": "3",
      "type": "TITLE",
      "style": "SECTION",
      "title": "同类房源对比"
    },
    {
      "serial": "3.1",
      "type": "TABLE",
      "style": "NORMAL",
      "title": "同学区小区房源横向对比",
      "cols": [
        "小区名称",
        "位置/距离",
        "建筑年代",
        "产品类型",
        "挂牌均价(元/㎡)",
        "总价预估",
        "主要优缺点"
      ],
      "content": [
        [
          {"type": "TEXT", "content": "目标小区名称"},
          {"type": "TEXT", "content": "具体位置"},
          {"type": "TEXT", "content": "建筑年代"},
          {"type": "TEXT", "content": "产品类型"},
          {"type": "TEXT", "content": "挂牌均价", "recommended": true},
          {"type": "TEXT", "content": "总价预估"},
          {"type": "TEXT", "content": "优缺点分析"}
        ]
      ]
    },
    {
      "serial": "3.2",
      "type": "LIST",
      "style": "BOARD",
      "title": "房源核心优势",
      "content": [
        {
          "content": "总价优势描述(如:比同户型成交价低15%-25%)",
          "emphasize": true
        },
        {
          "content": "税费优势描述(如:满五唯一住房,买家仅需1%契税)"
        },
        {
          "content": "装修优势描述(如:当前房源保养良好,省去拆改成本)"
        }
      ]
    },
    {
      "serial": "4",
      "type": "TITLE",
      "style": "SECTION",
      "title": "置业建议"
    },
    {
      "serial": "4.1",
      "type": "TEXT",
      "style": "BOARD",
      "title": "专家建议",
      "content": "置业专家的专业建议(如:本房源适合预算有限且需快速入住的自住家庭)"
    },
    {
      "serial": "4.2",
      "type": "CARD",
      "style": "BROKER",
      "title": "置业顾问推荐",
      "fields": {
        "name": "姓名",
        "icon": "头像URL",
        "education": "学历",
        "experience": "服务年限",
        "serviceCategory": [
          "服务类别1",
          "服务类别2"
        ],
        "specialSkill": [
          "特色专长1",
          "特色专长2"
        ],
        "suggest": "[根据报告信息,从经纪人视角给出消费者投资建议,限制50字以内,要专业/客观/中肯/有参考价值]",
        "wechat": "微信图片url",
        "phone": "联系电话"
      }
    }
  ]
}
```
<----------------------------(cric_output_flow)---------------------------->
=
${prevGenerateResponse}

<----------------------------(cric_output)---------------------------->
=
# 房源专家：业主急售！超低总价抄底闵行学区洼地！250w 得一梯队学区房

## 高兴花园一街坊

精装学区房，抢到即赚到！房源为 2024 年高兴花园一街坊挂牌性价比之王，精装状态 + 双学区 + 满五唯一的组合极具稀缺性！教育、交通、生活配套无短板，业主急售窗口期极短。

### 高性价比急售，自住投资双优

高兴花园二、三、四街坊：1 室 1 厅 1 卫 | 47㎡ | 6/6 | 梅陇镇，235 万，50000 元 /㎡ （唯一 七日热门）

*   18 号楼位于小区中央位置，绿化及景观为小区最佳。小区整体较大，有两个门可进出，本楼栋靠近小区出口 2 号门，步行距离短，车辆可进出，出行便捷

### 春申居住示范区，教育 + 交通王牌

*   闵行区实验小学春城校区（一梯队小学） + 莘松中学春申校区（一梯队初中），春申景城幼儿园（市一级园）位于小区门口。

*   近三月梅陇板块新房认购率 334%，带动二手房挂牌价上涨；同时因科技产业聚集，学区房涨幅显著（2025 年新增 30 所中小学）

*   1 号线外环路站、莲花路站、莘庄站环绕，公交 156/162/180 路等 10 余条线路经停 "莲花南路高兴路" 站，直达莘庄枢纽（约 12 分钟车程）。

### 学区房价格洼地 急售催化议价空间

#### 近半年二手房成交趋势

| 成交套数           | 单价区间                    | 面积区间                |
| -------------- | ----------------------- | ------------------- |
| 3 套（近 6 月成交套数） | 2 套（66.7%，4.8 - 4.9w/㎡） | 2 套（66.7%，45 - 50㎡） |

挂牌量仅约 3 套在售（一室 2 套、两室 1 套），总价区间 179 万 - 250 万，供应极度稀缺

| 户型 | 面积（㎡）   | 当前总价          | 单价（万 /㎡）  | 年跌幅  |
| -- | ------- | ------------- | --------- | ---- |
| 一房 | 37 - 52 | 235 万 - 260 万 | 4.8 - 5.2 | -12% |
| 两房 | 59 - 77 | 318 万 - 380 万 | 4.8 - 5.0 | -10% |
| 三房 | 96 - 97 | 480 万 - 520 万 | 4.9 - 5.3 | -9%  |

#### 租赁价格走势

市场核心数据速览（截至 2025 年 3 月）

| 指标   | 当前值         | 环比变化   | 同比趋势           | 房源案例                           |
| ---- | ----------- | ------ | -------------- | ------------------------------ |
| 二室均价 | 4413 元 / 月  | ↑1.12% | ↑5.3%（vs 2024） | 59㎡精装南北两房（4400 元 / 月，付 3 押 1）  |
| 一室均价 | 3612 元 / 月  | ↑5.21% | ↑8.1%（vs 2024） | 46㎡南向一室（3300 元 / 月，押 1 付 3）    |
| 最小户型 | 36.65㎡一室    | -      | -              | 36.65㎡精装南向（3000 元 / 月，押 1 付 1） |
| 供需比  | 1：1.8（客多房少） | -      | 需求增长 22%       | 3 月新增挂牌 412 套，去化率 78%          |

##### 本地洞察

小区租金价格明显高于同区域内同类小区，原因为春申景城幼儿园（市一级园）位于小区门口，步行可达。

#### 同学区小区房源横向对比（2025 年 6 月数据）

| 小区名称    | 位置 / 距离   | 建筑年代   | 产品类型     | 2025 年 5 月挂牌均价（元 /㎡） | 67㎡两房总价预估    | 主要优缺点                               |
| ------- | --------- | ------ | -------- | -------------------- | ------------ | ----------------------------------- |
| 高兴花园一街坊 | 春朱路 879 弄 | 2000 年 | 多层楼梯房    | 50,810               | 259 万（急售！独梯） | 优势：双学区最低门槛、满五唯一；劣势：无电梯、户型设计老旧、车位紧张  |
| 中城绿苑    | 北 241 米   | 2005 年 | 小高层 / 高层 | 66,192               | 440 万 +      | 优势：楼龄新、电梯房、社区品质高；劣势：单价高、大户型总价门槛高    |
| 春天花园    | 西北 272 米  | 1999 年 | 多层       | 54,806               | 370 万 +      | 优势：单价低于新盘；劣势：无电梯、户型设计老旧             |
| 新梅花苑    | 南 314 米   | 1998 年 | 多层 + 别墅  | 70,109               | 470 万 +      | 优势：混合型社区、绿化率高、两房朝南占比高；劣势：楼龄较老、得房率中等 |
| 梅香苑     | 西北 392 米  | 2008 年 | 住宅       | 54,386               | 360 万 +      | 优势：楼龄新、得房率高；劣势：小户型少、挂牌量低            |

##### 房源核心优势

*   总价碾压性优势：比 2023 年同户型成交价低 15% - 25%，甚至低于 2025 年小区均价

*   满五唯一住房，买家仅需 1% 契税；

*   当前房源保养良好，省去拆改成本

### 置业专家建议

本房源适合预算有限且需快速入住的自住家庭：精装免拆改，带长租固定车位，超低总价 300 万内落户闵行一梯队学区。业主因出国急售，目前已搬空可随时看房交易。

\[于宝成 瑞房房产 电话联系 微信联系]

（注：部分图表因原数据展示形式难精准还原，模拟数据尽量贴合原文逻辑呈现；实际使用中，若有准确原始数据，可替换对应表格内容 ）

> （注：文档部分内容可能由 AI 生成）

<----------------------------(broker)---------------------------->
=

```json
{
  "姓名": "于宝成",
  "头像": "https://fyoss-test.fangyou.com/jpg_2406131213538d9a39dc8eda370e5764171fea405170a807",
  "学历": "本科学历",
  "工作年限": "10年以上",
  "服务类型": "金融贷款专家、产权尽调、免费接送",
  "联系电话": "13123456789"
}
```
