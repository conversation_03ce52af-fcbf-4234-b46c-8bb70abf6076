## 守护进程
```shell
# 查看状态:
sudo systemctl status docker
# 启动守护进程:
sudo systemctl enable docker
# 重启守护进程:
sudo systemctl restart docker
```

## 镜像仓库
```shell
# 登录
docker login --username=不动产交易协作 crpi-almj9syt1g1inbke.cn-shanghai.personal.cr.aliyuncs.com

# 推送镜像
docker push crpi-almj9syt1g1inbke.cn-shanghai.personal.cr.aliyuncs.com/eju-dcai/aixg:[镜像版本号]

# 拉取镜像
docker pull crpi-almj9syt1g1inbke.cn-shanghai.personal.cr.aliyuncs.com/eju-dcai/aixg:[镜像版本号]
```

## 管理镜像
```shell
# 构建镜像(基础)
docker build -t aixg:v250507-1 -f aixg-application/config/Dockerfile . --build-arg PUB_NAME=aixg --build-arg PUB_PORT=8010 --build-arg PUB_ENV=test 

# 构建镜像(多平台&传参&推送仓库)
docker buildx build --progress=plain -t <image_name>:<tag> -f aixg-application/config/Dockerfile . --push --no-cache --build-arg PUB_ENV=<value>

# 查看本地镜像
docker images
```

## 运行镜像
```shell
# 启动容器
sudo docker run -d -p 8010:8010 --name aixg-container aixg:v250420
# 查看容器进程
docker ps
# 查看日志
sudo docker logs -f aixg-container
# 停止容器
sudo docker stop aixg-container
# 删除容器
sudo docker rm aixg-container
```
