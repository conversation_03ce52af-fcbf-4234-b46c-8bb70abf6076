# Q3产研规划

## 产品目标

### 易用性

-  突出一键生成，快速生成结构清晰、数据详实、语言专业的初稿，经纪人只需进行个性化润色和确认，极大提升效率。

### 洞察力

- 生成的深度文章（如：同小区同户型历史成交对比、板块发展潜力分析、新政下该房源优劣势解读）为房源信息注入洞察力，使其从“信息”升级为“知识”和“决策依据”，显著提升对购房者的价值。

### 专业性

- 确保内容在专业性、数据准确性和时效性上达到较高水平（需配合数据源和审核），帮助经纪人持续输出高质量内容，在客户和流量平台心中树立“专家”形象，加速信任建立。

### 价值转换

- 从“发布渠道”升级为“获客赋能平台”，对标流量平台（如闲鱼、抖音、小红书、公众号等）生成优质、原创、有深度的内容吸引和留住用户，显著提升经纪人对平台的依赖度和付费意愿。

## 成功要素

### 数据底座

- 房源数据

	- 房源基础

		- 小区、地址、户型、楼层、面积、朝向、装修、年代、产权、挂牌价、挂牌时间、房源状态、房源特色（满五唯一、南北通透、业主急售、带花园、景观房等）、核心卖点（经纪人提炼）、房源描述（经纪人填写）、房源图片/视频

	- 小区详情

		- 开发商、物业公司、物业费、容积率、绿化率、总户数、车位比、楼栋分布图、建筑类型（高层、洋房、别墅等）

	- 交易需求

		- 出售原因（置换、移民、急用钱等）、心理底价（非必填）、看房便利性、议价空间（经纪人判断）、特殊要求（付款周期等）

	- 带看数据

		- 带看次数、带看日期、带看经纪人（可能跨店）、带看反馈（客户关注点、抗性点 - 需结构化录入）

- 周边配套

	- 学区信息

		- 对口小学、初中（精确到校区）、学区政策、学校排名/口碑（非官方）、历年入学条件/预警信息

	- 交通配套

		- 地铁站点（距离、线路）、公交站点（线路、距离）、主干道/快速路、高铁站/机场距离与通勤时间（驾车/公交）、交通规划（在建地铁/道路）

	- 生活配套

		- 商场/超市（距离、规模）、菜市场、医院（等级、距离）、公园绿地、银行、餐饮娱乐等。

	- 产业环境

		- 周边主要产业园区/写字楼集群、就业机会、环境污染源（垃圾场、工厂等 - 规避负面）、公园/水系景观。

	- 规划潜力

		- 政府重点发展区域、控规图（用地性质：住宅、商业、绿地、教育等）、重大投资项目（如CBD、科技城、大型商业体、学校医院新建）、旧改计划。

- 市场动态

	- 挂牌房源

		- 其他平台的挂牌数据，用于了解市场热度及价格对比

	- 成交房源

		- 小区、户型、面积、楼层、朝向、成交价、成交日期、成交周期

	- 市场行情

		- 城市/区域挂牌均价、成交均价、挂牌量、成交量、成交周期、带看量、调价比例（涨价/降价房源占比）、新增房源量、库存去化周期

	- 房产政策

		- 限购政策（资格要求）、限贷政策（首付比例、贷款利率）、税收政策（增值税、契税、个人所得税）、公积金政策（贷款额度、条件）、人才购房政策、保障性住房政策、房地产相关法律法规（如《民法典》物权编）、市场监管政策。

	- 金融信贷

		- 最新LPR报价、各大银行执行的首套/二套房贷利率、贷款审批条件与周期、评估价指导。

### 模型能力

- 领域微调

	- 通用大模型的局限性。需要在海量房产报告、政策文件、市场分析文章、优质经纪人话术上进行微调，让AI掌握“房产语言”和专业逻辑，打造懂房产的专业AI。

- 模块化设计

	- 将文章拆解成不同模块（房源亮点、同小区对比、板块分析、政策影响、购房建议等），允许经纪人按需组合或重点生成某部分。

- 可控性与可解释性

	- 生成的结论需要有数据或逻辑支撑，避免“一本正经胡说八道”。提供关键数据来源提示。

- 风格定制

	- 允许选择不同风格（严谨报告型、通俗易懂型、情感打动型），或学习特定优秀经纪人的行文风格。

## 产品设计

### 人机协作

- 非全自动发布

	-  生成的应是高质量初稿，经纪人必须审阅、修改、补充个人见解和本地化信息，加入个人IP元素。

- 简易编辑工具

	- 提供友好的编辑界面，方便经纪人修改、加粗重点、插入个性化图片/视频。

- 知识沉淀

	- 将修改后的优质内容（脱敏后）反馈给系统，用于持续优化模型。

- 操作流程

	- AI生成初稿 → 经纪人个性化编辑 → 发布平台

### 场景覆盖

- 房源深度报告

	- 输入房源ID → 自动生成含「房源优劣势分析+同小区竞品对比+板块价值解读+政策影响评估」的完整报告。

- 客户定制分析

	- 经纪人输入客户需求（如“首付200万浦东学区三房”）→ AI生成「匹配房源对比+学区政策解读+贷款方案建议」。

- 市场热点解读

	- 自动抓取新政策（如“上海放开外环限购”）→ 生成「政策原文+对板块/房源影响+经纪人应对话术」。

### 一键智能

-  简化操作，3步内生成可用内容。

- 简化发布流程，支持一键发布到经纪人授权的多个流量平台。

- 根据文章类型和经纪人目标客户群，推荐最佳发布渠道：深度分析（公众号/知乎），房源对比（闲鱼、安居客），板块利好解读（小红书/朋友圈）

### 效果追踪

- 效果归因

	- 建立追踪机制，将文章阅读/互动行为与最终留资、带看、成交关联起来，用数据证明AI内容的价值，并持续优化生成策略。

- 效果可视

	- 清晰追踪内容带来的线索量与转化率。

## 技术实施

### 数据补充

- 房源数据

	- 交易需求、带看数据

- 周边配套

	- 学区信息、产业环境、规划潜力

- 市场动态

	- 挂牌房源、市场行情、房产政策、金融信贷

### 评价算法

- 房源多维度打分

- 板块热度值

- 信贷价格影响幅度

### 模型打磨

- 提示词工程

	- 对新数据和已有数据建立专业知识库，并能精准召回相关内容；

	- 对算法有精准描述，并给予多角度样例，对输出结果进行严格定义；

- 合规审查

	- 政策合规： 对政策解读必须绝对准确，避免误导。

	- 数据合规：严格遵守数据隐私法规（如个人信息保护法），生成内容中不能包含敏感个人信息。

	- 避免虚假宣传：基于事实数据，避免夸大其词、制造虚假稀缺性或恐慌情绪。

	- 版权风险： 确保生成内容不侵犯他人版权，引用数据需注明来源（如果公开）。

- 容错机制

	- 境外模型风险：地域限制、网络安全、合规风险

	- 模型调用异常：多模型备份，可实现快速切换；

	- 小模型部署：服务稳定、成本低、数据安全；

- 模型微调

	- 数据整理-》模型微调-》专业地产AI

## 项目计划

### 7月

- 目标：优化现有产品，初步具备客户价值，实现市场验证目标；

- 交付

	- 深挖数据潜力：充分利用小区详情、图片、户型及周边数据，丰富报告内容；

	- 聚焦房源：深入房源特点(户型、楼层、面积、价格、成交意愿)，具象数据对比，提升内容专业度；

	- 移动化改造：将现有面向PC的版面样式，深度改造为合适移动端客户呈现和发布的内容结构；

### 8月

- 目标：增加经纪人互动提升内容差异化，通过多平台发布和效果跟踪验证推广效果；

- 交付

	- 文档编辑：经纪人可指定语气风格，并对生成内容进行在线编辑，实现内容差异化输出；

	- 多平台适配：针对目标平台特点，针对性生成发布内容，经纪人绑定平台并进行发布(无APP开发，需开发多平台小程序入口)

	- 效果跟踪：对发布路径预设埋点，收集C端用户阅读及分析数据，并将获客效果进行可视化呈现；

### 9月

- 目标：根据1.0市场反馈及2.0产品规划，适时推进产品迭代和深度模型调优；

- 交付

	- 数据升级：基于更多数据爬取建立新型知识库，并基于新数据重构评价体系，提升数据专业度；

	- 评价算法：根据中长期目标全新构建评价模型，精细打分体系，为专业报告2.0做准备

	- 技术升级：微调模型底座选型、技术实验、成本测算，适时打造【房产AI大模型】

