package com.dcai.aixg.domain.test;

import com.ejuetc.commons.base.querydomain.api.QueryDomainAPI;
import com.ejuetc.commons.base.querydomain.api.QueryResponse;
import com.ejuetc.commons.base.querydomain.api.QueryTerm;
import com.ejuetc.consumer.api.delegation.ApiQueryListPO;
import com.ejuetc.consumer.api.delegation.DelegationAPI;
import com.ejuetc.consumer.api.dto.DelegationDTO;
import com.ejuetc.consumer.web.vo.DelegationVO;
import org.junit.Test;

import com.alibaba.fastjson.JSON;
import com.ejuetc.commons.base.response.ApiResponse;
import com.ejuetc.consumer.api.community.BindCommunityRO;
import com.ejuetc.consumer.api.community.CommunityAPI;
import com.ejuetc.saasapi.sdk.SaaSApiSDK;

import java.util.List;

public class ConsumerApiTest {
    //测试环境
//    SaaSApiSDK sdk = new SaaSApiSDK(
//            "e200a3b268394d6db4ac9b97f2ea5bc9",
//            "mY24t2ARVPx3l11+Sjw/WNNCxiHX5zB64aWE7IoRIbk=",
//            "http://localhost:8095"
//            "http://saasapi-test.ebaas.com"
//    );
//    CommunityAPI communityAPI = sdk.feignClient(CommunityAPI.class, "http://ejuetc-consumer.tst.ejucloud.cn/consumer");
//    DelegationAPI delegationApi = sdk.feignClient(DelegationAPI.class,"http://localhost:8097/consumer");

    //预发环境
    SaaSApiSDK sdk = new SaaSApiSDK(
            "216da28ec52c47b78dace957e080160a",
            "nDVvPMCtPK3uTRIlGdzUJddnQZicYWKKLcfnL/Q+rDU=",
            "http://saasapi-uat.ebaas.com"
    );
    CommunityAPI communityAPI = sdk.feignClient(CommunityAPI.class, "http://ejuetc-consumer.uat.ejucloud.cn/consumer");
    DelegationAPI delegationApi = sdk.feignClient(DelegationAPI.class,"http://ejuetc-consumer.uat.ejucloud.cn/consumer");

    @Test
    public void apiBind() {
        ApiResponse<BindCommunityRO> bind = communityAPI.bind("上海静安", "慧芝湖");
        System.out.println(JSON.toJSONString(bind, true));
    }

    @Test
    public void queryDomain() {
        ApiResponse<List<DelegationVO>> response = delegationApi.query(new ApiQueryListPO().setDelegationIds(List.of(
                53354L,
                53353L,
                53302L
        )));
        System.out.println(JSON.toJSONString(response, true));
    }

}
