package com.dcai.aixg.domain.aiagent.model;

import com.ejuetc.commons.base.clazz.SubtypeCode;
import jakarta.persistence.DiscriminatorValue;
import jakarta.persistence.Entity;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;
import org.springframework.ai.chat.model.ChatModel;
import org.springframework.ai.ollama.OllamaChatModel;
import org.springframework.ai.ollama.api.OllamaApi;
import org.springframework.ai.ollama.api.OllamaOptions;

@Entity
@DiscriminatorValue("OLLAMA")
@SubtypeCode(parent = Model.class, code = "OLLAMA", name = "Ollama")
@NoArgsConstructor(access = AccessLevel.PROTECTED)
public class Model4Ollama extends Model<Model4Ollama> {

    @Override
    protected ChatModel buildChatClient() {
        // 使用builder模式创建OllamaApi
        OllamaApi.Builder apiBuilder = OllamaApi.builder();
        if (baseUrl != null) {
            apiBuilder.baseUrl(baseUrl);
        }
        OllamaApi ollamaApi = apiBuilder.build();

        // 使用builder模式创建OllamaOptions
        OllamaOptions.Builder optionsBuilder = OllamaOptions.builder();
        if (model != null)
            optionsBuilder.model(model);
        if (temperature != null)
            optionsBuilder.temperature(temperature.doubleValue());

        return OllamaChatModel.builder()
                .ollamaApi(ollamaApi)
                .defaultOptions(optionsBuilder.build())
                .build();
    }
}
