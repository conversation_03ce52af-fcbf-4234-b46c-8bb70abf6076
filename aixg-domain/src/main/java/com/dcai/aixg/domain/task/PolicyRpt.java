package com.dcai.aixg.domain.task;

import java.util.List;

import org.springframework.data.jpa.repository.Query;
import org.springframework.data.jpa.repository.support.JpaRepositoryImplementation;

public interface PolicyRpt extends JpaRepositoryImplementation<Policy, Long> {

    @Query("from Policy where cityName is null or cityName = '' or cityName=?1 order by cityName")
    List<Policy> findByCityName(String cityName);

}
