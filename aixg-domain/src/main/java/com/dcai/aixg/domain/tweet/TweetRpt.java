package com.dcai.aixg.domain.tweet;

import com.dcai.aixg.dto.TweetDTO;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.jpa.repository.support.JpaRepositoryImplementation;

import java.util.List;

public interface TweetRpt extends JpaRepositoryImplementation<Tweet, Long> {

    @Query("""
            select t.townId
            from Tweet t
            where t.type = :type
            and t.year = :year
            and t.month = :month
            """)
    List<Long> findTownIdByTypeAndYearAndMonth(TweetDTO.Type type, String year, String month);
}
