package com.dcai.aixg.domain.task;

import java.util.List;
import java.util.stream.Collectors;

import com.dcai.aixg.domain.broker.Broker;
import com.dcai.aixg.dto.task.TaskDTO;
import com.dcai.aixg.pro.task.ReportCreatePO;
import com.ejuetc.commons.base.clazz.SubtypeCode;

import jakarta.persistence.Column;
import jakarta.persistence.DiscriminatorValue;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import lombok.extern.slf4j.Slf4j;

@Getter
@Entity
@Slf4j
@DiscriminatorValue("REPORT")
@SubtypeCode(parent = Task.class, code = "REPORT", name = "报告")
@Accessors(chain = true)
@NoArgsConstructor
public class Report extends Task {

    @Column(name = "report_id", columnDefinition = "varchar(255) COMMENT '报告ID'")
    private String reportId;

    @Enumerated(EnumType.STRING)
    @Column(name = "reportType", insertable = false, updatable = false)
    private TaskDTO.ReportType reportType;
    
    public Report(Broker broker, String reportId, ReportCreatePO po) {
    	this.broker = broker;
    	this.reportType = po.getReportType();
    	this.reportId = reportId;
    	this.topic = po.getTopic();
    	if (po.getHouses() != null && po.getHouses().size() > 0) {
    		List<String> houseIds = po.getHouses().stream().map(house -> house.getString("id")).collect(Collectors.toList());
    		this.houseIds = houseIds;
    		this.houseInfos = po.getHouses().stream().map(house -> house.toString()).collect(Collectors.toList());
    		//this.houseInfos = po.getHouses();
    	}
    }

}
