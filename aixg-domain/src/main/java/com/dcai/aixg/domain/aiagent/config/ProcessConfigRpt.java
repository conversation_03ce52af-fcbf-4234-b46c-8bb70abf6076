package com.dcai.aixg.domain.aiagent.config;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.Optional;

/**
 * 工作流配置Repository
 *
 * <AUTHOR>
 */
@Repository
public interface ProcessConfigRpt extends JpaRepository<Config, Long> {

    @Query("from Config4Subflow where code = ?1")
    Optional<Config4Subflow> findFlowConfigByCode(String code);

    @Query("from Config4Node where code = ?1")
    Optional<Config4Node> findNodeConfigByCode(String code);
}
