package com.dcai.aixg.domain.aiagent.functioncall;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import static com.alibaba.fastjson.JSON.toJSONString;
import static com.ejuetc.commons.base.spring.SpringUtil.getBean;

/**
 * Tool方法AOP切面
 * 拦截所有标记为@Tool的方法，记录执行信息
 */
@Slf4j
@Aspect
@Component
@RequiredArgsConstructor
public class FunctionCallAspect {

    private final FunctionCallRpt stepRpt;

    /**
     * 环绕通知：拦截所有标记为@Tool的方法
     */
    @Around("@annotation(org.springframework.ai.tool.annotation.Tool)")
    public Object aroundToolMethod(ProceedingJoinPoint joinPoint) {
        FunctionCall step = getBean(FunctionCallAspect.class).createFunctionCall(joinPoint);
        return getBean(FunctionCallAspect.class).execFunctionCall(joinPoint, step);
    }

    @Transactional(propagation = Propagation.REQUIRES_NEW)
    protected Object execFunctionCall(ProceedingJoinPoint joinPoint, FunctionCall step) {
        return step.exec(joinPoint);
    }

    @Transactional(propagation = Propagation.REQUIRES_NEW)
    protected FunctionCall createFunctionCall(ProceedingJoinPoint joinPoint) {
        return new FunctionCall(joinPoint).save();
    }

}
