package com.dcai.aixg.domain.order;

import com.dcai.aixg.dto.OrderDTO;
import org.springframework.data.jpa.repository.Lock;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.jpa.repository.support.JpaRepositoryImplementation;

import java.time.LocalDateTime;
import java.util.List;

import static jakarta.persistence.LockModeType.PESSIMISTIC_WRITE;

public interface OrderRpt extends JpaRepositoryImplementation<Order, Long> {

    @Lock(PESSIMISTIC_WRITE)
    List<Order> findAndLockByKerOrderNo(String kerOrderNo);

    @Query("""
            FROM Order od
            WHERE od.status = :status
            and od.updateTime <= :updateTime
            """)
    @Lock(PESSIMISTIC_WRITE)
    List<Order> findAndLockByStatusAndUpdateTime(OrderDTO.Status status, LocalDateTime updateTime);
}
