package com.dcai.aixg.domain.aiagent.model;

import com.ejuetc.commons.base.clazz.SubtypeCode;
import jakarta.persistence.DiscriminatorValue;
import jakarta.persistence.Entity;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;
import org.springframework.ai.chat.model.ChatModel;
import org.springframework.ai.openai.OpenAiChatModel;
import org.springframework.ai.openai.OpenAiChatOptions;
import org.springframework.ai.openai.api.OpenAiApi;


@Entity
@DiscriminatorValue("OPENAI")
@SubtypeCode(parent = Model.class, code = "OPENAI", name = "OpenAI")
@NoArgsConstructor(access = AccessLevel.PROTECTED)
public class Model4OpenAI extends Model<Model4OpenAI> {

    @Override
    protected ChatModel buildChatClient() {
        // 使用builder模式创建OpenAiApi
        OpenAiApi.Builder apiBuilder = OpenAiApi.builder();
        if (apiKey != null) {
            apiBuilder.apiKey(apiKey);
        }
        if (baseUrl != null) {
            apiBuilder.baseUrl(baseUrl);
        }
        OpenAiApi openAiApi = apiBuilder.build();

        // 使用builder模式创建OpenAiChatOptions
        OpenAiChatOptions.Builder optionsBuilder = OpenAiChatOptions.builder();
        if (model != null)
            optionsBuilder.model(model);
        if (temperature != null)
            optionsBuilder.temperature(temperature.doubleValue());
        if (maxTokens != null)
            optionsBuilder.maxTokens(maxTokens);

        return OpenAiChatModel.builder()
                .openAiApi(openAiApi)
                .defaultOptions(optionsBuilder.build())
                .build();
    }
}
