package com.dcai.aixg.domain.aiagent.node;

import static com.ejuetc.commons.base.spring.SpringUtil.getBean;

import com.alibaba.fastjson.JSONObject;
import com.dcai.aixg.api.WriteAPI;
import com.dcai.aixg.domain.aiagent.config.Config4Node;
import com.dcai.aixg.domain.aiagent.flow.Flow;
import com.dcai.aixg.dto.NodeDTO;
import com.dcai.aixg.dto.task.WriteCreateDTO;
import com.dcai.aixg.pro.task.WriteCreatePO;
import com.ejuetc.commons.base.clazz.SubtypeCode;
import com.ejuetc.commons.base.response.ApiResponse;
import com.fasterxml.jackson.databind.ObjectMapper;

import jakarta.persistence.DiscriminatorValue;
import jakarta.persistence.Entity;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * 生成任务实体
 *
 * <AUTHOR>
 */
@Slf4j
@Getter
@Entity
@NoArgsConstructor
@DiscriminatorValue("POLICY_COMMENT")
@SubtypeCode(parent = Node.class, code = "POLICY_COMMENT", name = "克尔瑞政策解读")
public class Node4PolicyComment extends Node {

    protected Node4PolicyComment(Config4Node config, JSONObject request, Flow flow) {
        super(config, request, flow);
    }

    @Override
    protected NodeDTO.Status doExec() throws Exception {
        log.info("调用Cric获取数据");
        ObjectMapper mapper = new ObjectMapper();
        WriteCreatePO po = mapper.readValue(getRequest().toJSONString(), WriteCreatePO.class);
        setProcessInfo1(JSONObject.toJSONString(po, true));
        ApiResponse<WriteCreateDTO> result = getBean(WriteAPI.class).doWriteByTaskId(getFlow().getSrcId(), this.getFlow().getId(), po);
        if (result.isSucc()) {
        	setProcessInfo2(result.getData().getWriteId());
        	setProcessInfo3(result.getData().getArticleId());
        }
        receiveAsyncResponse(NodeDTO.Status.SUCCESS, JSONObject.toJSONString(result.getData()));
        return NodeDTO.Status.GENERATING;
    }

}
