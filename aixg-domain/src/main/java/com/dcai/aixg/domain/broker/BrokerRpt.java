package com.dcai.aixg.domain.broker;

import com.dcai.aixg.dto.TownDTO;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.jpa.repository.support.JpaRepositoryImplementation;

import java.util.List;

import static com.ejuetc.commons.base.utils.ThreadUtils.redisLock;

public interface BrokerRpt extends JpaRepositoryImplementation<Broker, Long> {

    default Broker findAndLockById(Long brokerId) {
        redisLock(10, 10, "BrokerRpt.findAndLockById", brokerId);
        return findById(brokerId).orElse(null);
    }

    @Query("from Broker where messageOpenId = :messageOpenId")
    List<Broker> findByMessageOpenId(String messageOpenId);

    @Query("from Broker where openId = :openId")
    List<Broker> findByOpenId(String openId);

    @Query("select distinct new com.dcai.aixg.dto.TownDTO(b.cityId, b.cityName, b.townId, b.townName) from Broker b where b.townId is not null and b.townId not in :townIds")
    List<TownDTO> findTown(List<Long> townIds, Pageable pageable);

    @Query("select b.id from Broker b where b.townId = :townId")
    List<Long> findIdsByTownId(String townId);

    @Query("select b.id from Broker b")
    List<Long> findIds();

    @Query("select b.id from Broker b where b.cityId = :cityId")
    List<Long> findIdsByCityId(String cityId);
}
