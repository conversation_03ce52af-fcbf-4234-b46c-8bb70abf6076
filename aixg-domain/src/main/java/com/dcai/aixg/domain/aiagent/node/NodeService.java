package com.dcai.aixg.domain.aiagent.node;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.dcai.aixg.api.NodeAPI;
import com.dcai.aixg.domain.aiagent.config.Config4Node;
import com.dcai.aixg.domain.aiagent.config.ProcessConfigRpt;
import com.dcai.aixg.domain.aiagent.flow.FlowService;
import com.dcai.aixg.domain.aiagent.model.Model;
import com.dcai.aixg.domain.aiagent.model.ModelRpt;
import com.dcai.aixg.dto.NodeDTO;
import com.dcai.aixg.pro.LaunchNodePO;
import com.ejuetc.commons.base.response.ApiResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import static com.dcai.aixg.domain.aiagent.node.Node.newGenerate;
import static com.ejuetc.commons.base.querydomain.impl.Domain2DTOConvert.convert2DTO;
import static com.ejuetc.commons.base.response.ApiResponse.succ;
import static com.ejuetc.commons.base.spring.SpringUtil.getBean;
import static com.ejuetc.commons.base.utils.ThreadUtils.asyncExec;

/**
 * 生成API实现
 *
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequiredArgsConstructor
@Transactional
public class NodeService implements NodeAPI {

    private final NodeRpt nodeRpt;
    private final ProcessConfigRpt configRpt;
    private final ModelRpt modelRpt;

    @Override
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public ApiResponse<NodeDTO> launch(LaunchNodePO po) {
        Node node = getBean(NodeService.class).doCreate(po);
        Long generateId = node.getId();
        switch (po.getRunMode()) {
            case SYNC:
                node = doExec(generateId);
                break;
            case ASYNC:
                asyncExec(() -> getBean(NodeService.class).doExec(generateId));
                break;
            case STREAM:
                throw new RuntimeException("流式模式暂不支持");
        }
        return succ(convert2DTO(node, new NodeDTO()));
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public ApiResponse<Long> launch4test(String modeName, String resultType, String promptContent) {
        Node node = getBean(NodeService.class).doCreate(modeName, promptContent, resultType);
        asyncExec(() -> getBean(NodeService.class).doExec(node.getId()));
        return succ(node.getId());
    }


    @Transactional(propagation = Propagation.REQUIRES_NEW)
    protected Node doCreate(String modeName, String promptContent, String resultType) {
        Model<?> model = modelRpt.findByName(modeName);
        return new Node4LlmGen(model, promptContent, resultType).save();
    }

    @Transactional(propagation = Propagation.REQUIRES_NEW)
    protected Node doCreate(LaunchNodePO po) {
        Config4Node config = configRpt.findNodeConfigByCode(po.getConfigCode())
                .orElseThrow(() -> new RuntimeException("生成器配置不存在: " + po.getConfigCode()));
        return newGenerate(config, new JSONObject(po.getRequest())).save();
    }

    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public Node doExec(Long generateId) {
        Node node = nodeRpt.getReferenceById(generateId);
        node.exec();
        return node;
    }

    @Override
    public ApiResponse<?> receiveAsyncResponse(Long nodeId, NodeDTO.Status status, String result) {
        Node node = getBean(NodeService.class).doReceiveAsyncResponse(nodeId, status, result);
        Long flowId = node.getFlowId();
        if (flowId != null) getBean(FlowService.class).execFlow(flowId);
        return succ();
    }


    @Transactional(propagation = Propagation.REQUIRES_NEW)
    protected Node doReceiveAsyncResponse(Long generateId, NodeDTO.Status status, String result) {
        Node node = nodeRpt.getReferenceById(generateId);
        node.receiveAsyncResponse(status, result);
        return node;
    }

    @Override
    public ResponseEntity<String> queryResponse(Long id) {
        Node node = nodeRpt.getReferenceById(id);
        String content;
        MediaType mediaType;

        if (!node.isCompleted()) {
            content = "生成中...";
            mediaType = MediaType.TEXT_PLAIN;
        } else if (!node.isSuccess()) {
            content = "生成失败:" + node.getErrorMessage();
            mediaType = MediaType.TEXT_PLAIN;
        } else {
            content = node.getResponse();
            // 判断响应内容类型，如果是JSON格式则设置为application/json
            if (content != null && content.trim().startsWith("{") && content.trim().endsWith("}")) {
                mediaType = MediaType.APPLICATION_JSON;
            } else {
                mediaType = MediaType.TEXT_PLAIN;
            }
        }

        return ResponseEntity.ok()
                .contentType(mediaType)
                .header(HttpHeaders.CONTENT_DISPOSITION, "inline")
                .body(content);
    }

    @GetMapping("/web/generate/query")
    public JSONObject webQuery(@RequestParam Long id) {
        Node node = nodeRpt.getReferenceById(id);
        JSONObject obj = new JSONObject();
        if (!node.isCompleted()) {
            obj.put("code", 100);
            obj.put("message", "生成中");
        } else if (node.isSuccess()) {
            obj.put("code", 200);
            obj.put("message", "成功");
            obj.put("data", JSON.parseObject(node.getResponse()));
        } else {
            obj.put("code", 500);
            obj.put("message", "生成失败:" + node.getErrorMessage());
        }
        return obj;
    }
}
