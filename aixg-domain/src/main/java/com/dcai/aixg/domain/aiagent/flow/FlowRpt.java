package com.dcai.aixg.domain.aiagent.flow;

import com.dcai.aixg.dto.FlowDTO;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.Optional;

/**
 * 工作流Repository
 *
 * <AUTHOR>
 */
@Repository
public interface FlowRpt extends JpaRepository<Flow, Long> {

    Optional<Flow> findBySrcTypeAndSrcId(FlowDTO.SrcType srcType, Long srcId);
}
