package com.dcai.aixg.domain.aiagent.commons;

import com.dcai.aixg.dto.ProcessDTO;
import com.ejuetc.commons.base.entity.BaseEntity;
import jakarta.persistence.Column;
import jakarta.persistence.MappedSuperclass;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.time.Duration;
import java.time.LocalDateTime;

@Getter
@MappedSuperclass
@NoArgsConstructor
public abstract class ProcessBase<Subtype extends ProcessBase<?>> extends BaseEntity<Subtype> {

    @Column(name = "started_time", columnDefinition = "datetime COMMENT '开始时间'")
    private LocalDateTime startedTime;

    @Column(name = "completed_time", columnDefinition = "datetime COMMENT '完成时间'")
    private LocalDateTime completedTime;

    @Column(name = "duration", columnDefinition = "bigint COMMENT '执行时长(毫秒)'")
    private Long duration;

    @Setter(AccessLevel.PROTECTED)
    @Column(name = "response", columnDefinition = "longtext COMMENT '最终响应内容'")
    private String response;

    @Setter(AccessLevel.PROTECTED)
    @Column(name = "error_message", columnDefinition = "text COMMENT '错误信息'")
    private String errorMessage;

    protected void timerStart() {
        this.startedTime = LocalDateTime.now();
    }

    protected void timerStop() {
        this.completedTime = LocalDateTime.now();
        this.duration = Duration.between(startedTime, completedTime).toMillis();
    }

    public abstract boolean isSuccess();

    public abstract boolean isCompleted();

    public abstract ProcessDTO.Type getProcessType();

    public boolean equals(ProcessDTO.Type type, Long id) {
        return getProcessType().equals(type) && getId().equals(id);
    }
}
