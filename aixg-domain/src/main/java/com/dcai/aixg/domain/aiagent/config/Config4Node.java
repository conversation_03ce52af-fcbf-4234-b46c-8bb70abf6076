package com.dcai.aixg.domain.aiagent.config;

import com.dcai.aixg.domain.aiagent.commons.ProcessBase;
import com.dcai.aixg.domain.aiagent.flow.Flow;
import com.dcai.aixg.domain.aiagent.node.Node;
import com.dcai.aixg.domain.aiagent.model.Model;
import com.dcai.aixg.dto.NodeDTO;
import com.ejuetc.commons.base.clazz.SubtypeCode;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.hibernate.annotations.Comment;

@Slf4j
@Getter
@Entity
@NoArgsConstructor
@DiscriminatorValue("NODE")
@SubtypeCode(parent = Config.class, code = "NODE", name = "节点")
public class Config4Node extends Config {

    @Column(name = "content", columnDefinition = "longtext COMMENT '配置内容'")
    private String content;

    @Enumerated(EnumType.STRING)
    @Comment("生成器类型")
    @Column(name = "node_type", nullable = false)
    private NodeDTO.Type nodeType;

    @Column(name = "result_type", columnDefinition = "varchar(255) COMMENT '状态'")
    private String resultType;

    @ManyToOne(fetch = FetchType.LAZY, cascade = CascadeType.ALL)
    @JoinColumn(name = "model_id", columnDefinition = "bigint(20) COMMENT '模型ID'")
    private Model<?> model;

    @Override
    public ProcessBase<?> newProcess(Flow flow) {
        return Node.newGenerate(this, flow);
    }


}
