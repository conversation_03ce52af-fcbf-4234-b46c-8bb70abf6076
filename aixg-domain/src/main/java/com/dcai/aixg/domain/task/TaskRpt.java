package com.dcai.aixg.domain.task;

import static jakarta.persistence.LockModeType.PESSIMISTIC_WRITE;

import java.util.List;

import org.springframework.data.jpa.repository.Lock;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.jpa.repository.support.JpaRepositoryImplementation;

import com.dcai.aixg.domain.broker.Broker;
import com.dcai.aixg.dto.task.TaskDTO;

public interface TaskRpt extends JpaRepositoryImplementation<Task, Long> {

    @Lock(PESSIMISTIC_WRITE)
    Task findAndLockById(Long id);
    
    @Query("from Write where id = ?1")
    Write findByTaskId(Long id);
    
    @Query("from Write where flowId = ?1")
    Write findByFlowId(Long flowId);
    
    //@Lock(PESSIMISTIC_WRITE)
    @Query("from Task where broker = :broker and type = :type and status = :status")
    List<Task> findAllByTypeAndStatus(Broker broker, TaskDTO.Type type, TaskDTO.Status status);

    @Query("from Write where articleId = ?1")
    @Lock(PESSIMISTIC_WRITE)
    List<Write> findAndLockByArticleId(String articleId);

    @Query("from Write where writeId = ?1")
    @Lock(PESSIMISTIC_WRITE)
    List<Write> findAndLockByWriteId(String writeId);

    @Query("from Write where cricDataId = ?1")
    @Lock(PESSIMISTIC_WRITE)
    List<Write> findAndLockByCricDataId(String cricDataId);

    @Query("from Report where reportId = ?1")
    @Lock(PESSIMISTIC_WRITE)
    List<Report> findAndLockByReportId(String reportId);

    @Query("from Task where openId = :openId and status = :status")
    List<Task> findByOpenIdAndStatus(String openId, TaskDTO.Status status);

}
