package com.dcai.aixg.domain.aiagent.node;

import com.alibaba.fastjson.JSONObject;
import com.dcai.aixg.api.WriteAPI;
import com.dcai.aixg.domain.aiagent.config.Config4Node;
import com.dcai.aixg.domain.aiagent.flow.Flow;
import com.dcai.aixg.dto.NodeDTO;
import com.dcai.aixg.dto.task.WriteCreateDTO;
import com.dcai.aixg.pro.task.WriteCreatePO;
import com.ejuetc.commons.base.clazz.SubtypeCode;
import com.ejuetc.commons.base.response.ApiResponse;
import com.fasterxml.jackson.databind.ObjectMapper;
import jakarta.persistence.DiscriminatorValue;
import jakarta.persistence.Entity;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import static com.ejuetc.commons.base.spring.SpringUtil.getBean;

/**
 * 生成任务实体
 *
 * <AUTHOR>
 */
@Slf4j
@Getter
@Entity
@NoArgsConstructor
@DiscriminatorValue("MONTHLY_REPORT")
@SubtypeCode(parent = Node.class, code = "MONTHLY_REPORT", name = "月度分析报告")
public class Node4MonthlyMarket extends Node {

    protected Node4MonthlyMarket(Config4Node config, JSONObject request, Flow flow) {
        super(config, request, flow);
    }

    @Override
    protected NodeDTO.Status doExec() throws Exception {
        log.info("调用Cric获取数据");
        ObjectMapper mapper = new ObjectMapper();
        Long kerUserId = getRequest().getLong("userId");
        WriteCreatePO po = mapper.readValue(getRequest().toJSONString(), WriteCreatePO.class);
        ApiResponse<WriteCreateDTO> result = getBean(WriteAPI.class).doWrite(kerUserId, po);
        log.info("调用Cric获取数据 po = {}, result = {}", po, result);
        setProcessInfo1(JSONObject.toJSONString(po, true));
        setProcessInfo2(result.getData().getWriteId());
        setProcessInfo3(result.getData().getArticleId());
        receiveAsyncResponse(NodeDTO.Status.SUCCESS, JSONObject.toJSONString(result.getData()));
        return NodeDTO.Status.GENERATING;
    }
}
