package com.dcai.aixg.domain.aiagent.node;

import com.ejuetc.commons.base.utils.IOUtils;
import lombok.Getter;
import lombok.experimental.Accessors;

import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.util.HashMap;
import java.util.Map;

@Getter
public class PromptContent {

    private static final String BEGIN_SPLIT = "<----------------------------(";
    private static final String END_SPLIT = ")---------------------------->";
    public static final String SYSTEM_PROMPT_KEY = "system_prompt";
    public static final String USER_PROMPT_KEY = "user_prompt";
    public static final String PLACE_HOLDER = "${%s}";
    public static final String FLOW_KEY_PATTERN = "%s_flow";
    private final boolean inFlow;

    @Getter
    private String promptContent = "";

    @Getter(lazy = true)
    @Accessors(fluent = true)
    private final Map<String, String> values = parseValues();

    public PromptContent(String promptContent, boolean inFlow) {
        if (promptContent == null) throw new RuntimeException("提示词内容不能为空");
        this.promptContent = promptContent;
        this.inFlow = inFlow;
    }

    public Map<String, String> parseValues() {
        Map<String, String> values = new HashMap<>();
        int startIdx = 0;
        while (true) {
            Prompt prompt = splitProm(promptContent, startIdx);
            values.put(prompt.key, prompt.content);
            if (prompt.finishIdx == promptContent.length()) break;
            startIdx = prompt.finishIdx;
        }

        for (String key : values.keySet()) {
            String placeHolder = PLACE_HOLDER.formatted(key);
            String replacement = values.get(key);
            if (inFlow) {
                String flowKey = FLOW_KEY_PATTERN.formatted(key);
                String flowValue = values.get(flowKey);
                if (flowValue != null)
                    replacement = flowValue;
            }
            for (String ownerKey : values.keySet()) {
                String owner = values.get(ownerKey);
                owner = owner.replace(placeHolder, replacement);
                values.put(ownerKey, owner);
            }
        }

        return values;
    }

    private Prompt splitProm(String prompt, int startIdx) {
        int beginIdx = prompt.indexOf(BEGIN_SPLIT, startIdx);
        if (beginIdx == -1) throw new RuntimeException("提示词格式错误，未找到分隔符: " + BEGIN_SPLIT);
        int endIdx = prompt.indexOf(END_SPLIT, beginIdx + BEGIN_SPLIT.length());
        if (endIdx == -1) throw new RuntimeException("提示词格式错误，未找到分隔符: " + END_SPLIT);
        String key = prompt.substring(beginIdx + BEGIN_SPLIT.length(), endIdx);
        int finishIdx = prompt.indexOf(BEGIN_SPLIT, endIdx + END_SPLIT.length());
        if (finishIdx == -1) finishIdx = prompt.length();
        String content = prompt.substring(endIdx + END_SPLIT.length(), finishIdx).trim();
        if (content.startsWith("=\n")) content = content.substring(2);
        return new Prompt(key, content, finishIdx);
    }

    record Prompt(String key, String content, int finishIdx) {
    }

    public static void main(String[] args) throws FileNotFoundException {
        String file = "/Users/<USER>/IdeaProjects/dcai/aixg/docs/prompt/Markdown转换JSON/v5_基于JSON模板生成/提示词/prompt_v3_模板优先JSON结构定义.md";
        String fileContent = IOUtils.read(new FileInputStream(file));

        PromptContent promptContent = new PromptContent(fileContent, true);
        System.out.println(promptContent.getUserPrompt());
    }

    public String getSystemPrompt() {
        return values().get(SYSTEM_PROMPT_KEY);
    }

    public String getUserPrompt() {
        return values().get(USER_PROMPT_KEY);
    }

}
