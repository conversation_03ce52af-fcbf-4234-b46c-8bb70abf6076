package com.dcai.aixg.domain.banner;

import com.ejuetc.commons.base.entity.BaseEntity;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.Comment;
import org.hibernate.annotations.Where;


@Getter
@Entity
@NoArgsConstructor
@Comment("广告banner")
@Table(name = "tb_banner")
@Where(clause = "logic_delete = 0")
public class Banner extends BaseEntity<Banner> {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id", columnDefinition = "bigint(20) COMMENT 'ID'")
    private Long id;

    @Column(name = "city_id", columnDefinition = "varchar(20) COMMENT '城市id'", nullable = false)
    private String cityId;

    @Column(name = "app_id", columnDefinition = "varchar(50) COMMENT '小程序id'")
    private String appId;

    @Column(name = "path", columnDefinition = "varchar(255) COMMENT '跳转链接'")
    private String path;

    @Column(name = "img_url", columnDefinition = "varchar(255) COMMENT '图片链接'")
    private String imgUrl;

    @Column(name = "name", columnDefinition = "varchar(255) COMMENT '名称'")
    private String name;
}
