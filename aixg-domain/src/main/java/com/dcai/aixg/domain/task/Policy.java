package com.dcai.aixg.domain.task;

import org.hibernate.annotations.Comment;
import org.hibernate.annotations.Where;

import com.ejuetc.commons.base.entity.BaseEntity;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.NoArgsConstructor;

@Getter
@Entity
@NoArgsConstructor
@Comment("政策")
@Table(name = "tb_policy")
@Where(clause = "logic_delete = 0")
public class Policy extends BaseEntity<Policy> {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id", columnDefinition = "bigint(20) COMMENT 'ID'")
    private Long id;

    @Column(name = "cityName", columnDefinition = "varchar(255) COMMENT '城市名称'")
    private String cityName;

    @Column(name = "title", columnDefinition = "varchar(255) COMMENT '政策标题'")
    private String title;

}
