package com.dcai.aixg.domain.commons;

import lombok.extern.slf4j.Slf4j;

import java.util.Stack;

@Slf4j
public class ThreadLocalStack<T> {
    private final ThreadLocal<Stack<T>> localStack = new ThreadLocal<Stack<T>>() {
        @Override
        protected Stack<T> initialValue() {
            return new Stack<>();
        }
    };

    public T peek() {
        Stack<T> stack = localStack.get();
        return stack.isEmpty() ? null : stack.peek();
    }

    public void push(T value) {
        localStack.get().push(value);
        log.debug("推入运行栈: {}, 当前栈深度: {}", value, localStack.get().size());
    }

    public T pop() {
        Stack<T> stack = localStack.get();
        if (stack.isEmpty()) {
            throw new RuntimeException("尝试从空栈中弹出元素");
        }
        T value = stack.pop();
        log.debug("弹出运行栈: {}, 当前栈深度: {}", value, stack.size());

        // 如果栈为空，清理ThreadLocal以避免内存泄漏
        if (stack.isEmpty()) {
            localStack.remove();
        }

        return value;
    }

}
