package com.dcai.aixg.domain.aiagent.config;

import com.dcai.aixg.domain.aiagent.commons.ProcessBase;
import com.dcai.aixg.domain.aiagent.flow.Flow;
import com.dcai.aixg.dto.ConfigDTO;
import com.ejuetc.commons.base.clazz.ClassUtils;
import com.ejuetc.commons.base.entity.BaseEntity;
import com.ejuetc.commons.base.usertype.MapContentUT;
import com.ejuetc.commons.base.valueobj.MapContent;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.hibernate.annotations.Comment;
import org.hibernate.annotations.Type;
import org.hibernate.annotations.Where;

import java.util.ArrayList;
import java.util.List;

@Slf4j
@Getter
@Entity
@NoArgsConstructor
@Comment("工作流节点")
@Table(name = "tb_ai_config")
@Where(clause = "logic_delete = 0")
@Inheritance(strategy = InheritanceType.SINGLE_TABLE)
@DiscriminatorColumn(name = "type", columnDefinition = "enum('NODE','SUBFLOW') COMMENT '类型'")
public abstract class Config extends BaseEntity<Config> {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id", columnDefinition = "bigint(20) COMMENT '配置ID'")
    private Long id;

    @Column(name = "name", columnDefinition = "varchar(200) COMMENT '节点名称'")
    private String name;

    @Column(name = "code", columnDefinition = "varchar(100) COMMENT '配置代码'", nullable = false, unique = true)
    private String code;

    @ManyToOne(fetch = FetchType.LAZY, cascade = CascadeType.ALL)
    @JoinColumn(name = "parent_id", columnDefinition = "bigint(20) COMMENT '父节点ID'")
    private Config parent;

    @Enumerated(EnumType.STRING)
    @Column(name = "type", nullable = false, insertable = false, updatable = false)
    private ConfigDTO.Type type;

    @OrderColumn(name = "sort", columnDefinition = "int COMMENT '步骤顺序'")
    @OneToMany(mappedBy = "parent", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    private List<Config> children = new ArrayList<>();

    @Type(MapContentUT.class)
    @Column(name = "properties", columnDefinition = "varchar(511) COMMENT '流程属性配置'")
    private MapContent properties = new MapContent();

    public Config nextNode(Flow flow) {
        return children != null && !children.isEmpty() ? children.get(0) : null;
    }

    public abstract ProcessBase<?> newProcess(Flow flow);

    public ConfigDTO.Type getType() {
        if (type == null)
            type = ConfigDTO.Type.valueOf(ClassUtils.getClassAnnotation(this.getClass(), DiscriminatorValue.class).value());
        return type;
    }

}
