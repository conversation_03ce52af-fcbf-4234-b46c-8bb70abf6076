package com.dcai.aixg.domain.search;

import static jakarta.persistence.LockModeType.PESSIMISTIC_WRITE;

import java.util.List;

import org.springframework.data.jpa.repository.Lock;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.jpa.repository.support.JpaRepositoryImplementation;

public interface SearchRpt extends JpaRepositoryImplementation<Search, Long> {
	
    @Lock(PESSIMISTIC_WRITE)
    List<Search> findAndLockByChatId(String chatId);

    @Query("""
            from Search s
            where s.chatId in ?1
            """)
    List<Search> findByChatIds(List<String> userIds);

}
