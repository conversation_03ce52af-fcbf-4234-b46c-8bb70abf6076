package com.dcai.aixg.domain.aiagent.model;

import com.ejuetc.commons.base.clazz.SubtypeCode;
import jakarta.persistence.DiscriminatorValue;
import jakarta.persistence.Entity;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;
import org.springframework.ai.chat.model.ChatModel;
import org.springframework.ai.deepseek.DeepSeekChatModel;
import org.springframework.ai.deepseek.DeepSeekChatOptions;
import org.springframework.ai.deepseek.api.DeepSeekApi;

@Entity
@DiscriminatorValue("DEEPSEEK")
@SubtypeCode(parent = Model.class, code = "DEEPSEEK", name = "DeepSeek")
@NoArgsConstructor(access = AccessLevel.PROTECTED)
public class Model4DeepSeek extends Model<Model4DeepSeek> {

    @Override
    protected ChatModel buildChatClient() {
        // 使用builder模式创建DeepSeekApi
        DeepSeekApi.Builder apiBuilder = DeepSeekApi.builder();
        if (apiKey != null) {
            apiBuilder.apiKey(apiKey);
        }
        if (baseUrl != null) {
            apiBuilder.baseUrl(baseUrl);
        }
        DeepSeekApi deepSeekApi = apiBuilder.build();

        // 使用builder模式创建DeepSeekChatOptions
        DeepSeekChatOptions.Builder optionsBuilder = DeepSeekChatOptions.builder();
        if (model != null)
            optionsBuilder.model(model);
        if (temperature != null)
            optionsBuilder.temperature(temperature.doubleValue());
        if (maxTokens != null)
            optionsBuilder.maxTokens(maxTokens);

        return DeepSeekChatModel.builder()
                .deepSeekApi(deepSeekApi)
                .defaultOptions(optionsBuilder.build())
                .build();
    }
}
