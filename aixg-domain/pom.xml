<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>com.dcai.aixg</groupId>
        <artifactId>aixg</artifactId>
        <version>0.1.8</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>aixg-domain</artifactId>
    <properties>
        <maven.deploy.skip>true</maven.deploy.skip>
    </properties>
<!--    <dependencyManagement>-->
<!--        <dependencies>-->
<!--            <dependency>-->
<!--                <groupId>org.springframework.ai</groupId>-->
<!--                <artifactId>spring-ai-bom</artifactId>-->
<!--                <version>1.0.0.M1</version>-->
<!--                <type>pom</type>-->
<!--                <scope>import</scope>-->
<!--            </dependency>-->
<!--        </dependencies>-->
<!--    </dependencyManagement>-->
    <dependencies>
<!--        <dependency>-->
<!--            <groupId>com.aliyun</groupId>-->
<!--            <artifactId>dyplsapi20170525</artifactId>-->
<!--            <version>3.0.0</version>-->
<!--        </dependency>-->
<!--        <dependency>-->
<!--            <groupId>com.aliyun</groupId>-->
<!--            <artifactId>mns_open20220119</artifactId>-->
<!--            <version>1.0.3</version>-->
<!--        </dependency>-->
<!--        <dependency>-->
<!--            <groupId>com.aliyun.mns</groupId>-->
<!--            <artifactId>aliyun-sdk-mns</artifactId>-->
<!--            <version>1.1.3</version>-->
<!--        </dependency>-->
<!--        <dependency>-->
<!--            <groupId>com.aliyun.oss</groupId>-->
<!--            <artifactId>aliyun-sdk-oss</artifactId>-->
<!--            <version>3.18.1</version>-->
<!--        </dependency>-->
<!--        <dependency>-->
<!--            <groupId>com.belerweb</groupId>-->
<!--            <artifactId>pinyin4j</artifactId>-->
<!--            <version>2.5.0</version>-->
<!--        </dependency>-->
<!--        <dependency>-->
<!--            <groupId>org.apache.poi</groupId>-->
<!--            <artifactId>poi-ooxml</artifactId>-->
<!--            <version>5.2.3</version>-->
<!--        </dependency>-->
        <dependency>
            <groupId>commons-codec</groupId>
            <artifactId>commons-codec</artifactId>
        </dependency>
        <dependency>
            <groupId>com.dcai.aixg</groupId>
            <artifactId>aixg-integration</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.taobao.api</groupId>
            <artifactId>taobao-sdk-java-auto</artifactId>
            <version>20230825</version>
        </dependency>
        <dependency>
            <groupId>com.ejuetc.saasapi</groupId>
            <artifactId>saasapi-api</artifactId>
        </dependency>
<!--        <dependency>-->
<!--            <groupId>org.springframework.ai</groupId>-->
<!--            <artifactId>spring-ai-starter-model-deepseek</artifactId>-->
<!--        </dependency>-->
        <dependency>
            <groupId>org.springframework.ai</groupId>
            <artifactId>spring-ai-deepseek</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.ai</groupId>
            <artifactId>spring-ai-client-chat</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.ai</groupId>
            <artifactId>spring-ai-openai</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.ai</groupId>
            <artifactId>spring-ai-ollama</artifactId>
        </dependency>
        <dependency>
            <groupId>org.freemarker</groupId>
            <artifactId>freemarker</artifactId>
        </dependency>
        <dependency>
            <groupId>com.ejuetc.consumer</groupId>
            <artifactId>consumer-api</artifactId>
<!--            <exclusions>-->
<!--                <exclusion>-->
<!--                    <groupId>com.ejuetc.channel</groupId>-->
<!--                    <artifactId>channel-api</artifactId>-->
<!--                </exclusion>-->
<!--            </exclusions>-->
        </dependency>
        <dependency>
            <groupId>com.ejuetc.commons</groupId>
            <artifactId>commons-querydomain</artifactId>
        </dependency>
        <dependency>
            <groupId>com.ejuetc.commons</groupId>
            <artifactId>commons-base</artifactId>
        </dependency>
        <dependency>
            <groupId>io.github.lastincisor</groupId>
            <artifactId>mysql-connector-java</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-redis</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>com.dcai.aixg</groupId>
            <artifactId>aixg-api</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>
        <dependency>
            <groupId>com.dcai.gateway</groupId>
            <artifactId>gateway-sdk</artifactId>
        </dependency>
        <dependency>
            <groupId>org.hibernate.orm</groupId>
            <artifactId>hibernate-jcache</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.data</groupId>
            <artifactId>spring-data-jpa</artifactId>
        </dependency>
        <dependency>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-annotations</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-jpa</artifactId>
        </dependency>
        <dependency>
            <groupId>jakarta.validation</groupId>
            <artifactId>jakarta.validation-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.dcai.message</groupId>
            <artifactId>message-api</artifactId>
        </dependency>
<!--        &lt;!&ndash; Selenium &ndash;&gt;-->
<!--        <dependency>-->
<!--            <groupId>org.seleniumhq.selenium</groupId>-->
<!--            <artifactId>selenium-java</artifactId>-->
<!--            <version>4.1.2</version>-->
<!--        </dependency>-->
<!--        &lt;!&ndash; ChromeDriver &ndash;&gt;-->
<!--        <dependency>-->
<!--            <groupId>io.github.bonigarcia</groupId>-->
<!--            <artifactId>webdrivermanager</artifactId>-->
<!--            <version>5.1.0</version>-->
<!--        </dependency>-->
        <dependency>
            <groupId>com.microsoft.playwright</groupId>
            <artifactId>playwright</artifactId>
            <version>1.43.0</version>
        </dependency>
        <dependency>
            <groupId>net.coobird</groupId>
            <artifactId>thumbnailator</artifactId>
            <version>0.4.19</version>
        </dependency>
    </dependencies>
</project>